use anyhow::{Result, Context};
use std::collections::{HashMap, HashSet, VecDeque};
use tracing::{debug, warn};

use super::types::*;

/// Graph algorithms for dependency resolution
impl DependencyGraph {
    /// Perform topological sort to get installation order
    pub fn topological_sort(&self) -> Result<Vec<String>> {
        let mut in_degree: HashMap<String, usize> = HashMap::new();
        let mut adj_list: HashMap<String, Vec<String>> = HashMap::new();
        
        // Initialize in-degree and adjacency list
        for node_name in self.nodes.keys() {
            in_degree.insert(node_name.clone(), 0);
            adj_list.insert(node_name.clone(), Vec::new());
        }
        
        // Build the graph
        for edge in &self.edges {
            adj_list.get_mut(&edge.from)
                .unwrap()
                .push(edge.to.clone());
            
            *in_degree.get_mut(&edge.to).unwrap() += 1;
        }
        
        // <PERSON>'s algorithm
        let mut queue = VecDeque::new();
        let mut result = Vec::new();
        
        // Find all nodes with no incoming edges
        for (node, &degree) in &in_degree {
            if degree == 0 {
                queue.push_back(node.clone());
            }
        }
        
        while let Some(node) = queue.pop_front() {
            result.push(node.clone());
            
            // Remove this node from the graph
            if let Some(neighbors) = adj_list.get(&node) {
                for neighbor in neighbors {
                    let degree = in_degree.get_mut(neighbor).unwrap();
                    *degree -= 1;
                    
                    if *degree == 0 {
                        queue.push_back(neighbor.clone());
                    }
                }
            }
        }
        
        // Check for cycles
        if result.len() != self.nodes.len() {
            return Err(anyhow::anyhow!("Circular dependency detected"));
        }
        
        Ok(result)
    }

    /// Get nodes in installation order (topologically sorted)
    pub fn get_install_order(&self) -> Vec<&DependencyNode> {
        match self.topological_sort() {
            Ok(sorted_names) => {
                sorted_names.iter()
                    .filter_map(|name| self.nodes.get(name))
                    .collect()
            }
            Err(_) => {
                // If topological sort fails, return all nodes in arbitrary order
                warn!("Topological sort failed, using arbitrary order");
                self.nodes.values().collect()
            }
        }
    }
    
    /// Detect circular dependencies using DFS
    pub fn detect_cycles(&self) -> Vec<Vec<String>> {
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();
        let mut cycles = Vec::new();
        
        for node_name in self.nodes.keys() {
            if !visited.contains(node_name) {
                let mut path = Vec::new();
                self.dfs_cycle_detection(
                    node_name,
                    &mut visited,
                    &mut rec_stack,
                    &mut path,
                    &mut cycles,
                );
            }
        }
        
        cycles
    }
    
    fn dfs_cycle_detection(
        &self,
        node: &str,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
        path: &mut Vec<String>,
        cycles: &mut Vec<Vec<String>>,
    ) {
        visited.insert(node.to_string());
        rec_stack.insert(node.to_string());
        path.push(node.to_string());
        
        // Get neighbors
        let neighbors: Vec<String> = self.edges
            .iter()
            .filter(|edge| edge.from == node)
            .map(|edge| edge.to.clone())
            .collect();
        
        for neighbor in neighbors {
            if !visited.contains(&neighbor) {
                self.dfs_cycle_detection(&neighbor, visited, rec_stack, path, cycles);
            } else if rec_stack.contains(&neighbor) {
                // Found a cycle
                if let Some(cycle_start) = path.iter().position(|n| n == &neighbor) {
                    let cycle = path[cycle_start..].to_vec();
                    cycles.push(cycle);
                }
            }
        }
        
        rec_stack.remove(node);
        path.pop();
    }
    
    /// Find all packages that depend on a given package
    pub fn find_dependents(&self, package: &str) -> Vec<String> {
        self.edges
            .iter()
            .filter(|edge| edge.to == package)
            .map(|edge| edge.from.clone())
            .collect()
    }
    
    /// Find all packages that a given package depends on
    pub fn find_dependencies(&self, package: &str) -> Vec<String> {
        self.edges
            .iter()
            .filter(|edge| edge.from == package)
            .map(|edge| edge.to.clone())
            .collect()
    }
    
    /// Get the depth of a package in the dependency tree
    pub fn get_depth(&self, package: &str) -> usize {
        let mut visited = HashSet::new();
        self.calculate_depth(package, &mut visited)
    }
    
    fn calculate_depth(&self, package: &str, visited: &mut HashSet<String>) -> usize {
        if visited.contains(package) {
            return 0; // Avoid infinite recursion in cycles
        }
        
        visited.insert(package.to_string());
        
        let dependencies = self.find_dependencies(package);
        if dependencies.is_empty() {
            return 0;
        }
        
        let max_depth = dependencies
            .iter()
            .map(|dep| self.calculate_depth(dep, visited))
            .max()
            .unwrap_or(0);
        
        visited.remove(package);
        max_depth + 1
    }
    
    /// Optimize the graph by removing redundant dependencies
    pub fn optimize(&mut self) -> Result<()> {
        // TODO: Implement graph optimization
        // - Remove duplicate dependencies
        // - Hoist common dependencies
        // - Flatten dependency tree where possible
        
        debug!("Graph optimization not yet implemented");
        Ok(())
    }
}
