use anyhow::{Result, Context};
use std::path::{Path, PathBuf};
use tokio::fs;
use tracing::{debug, info, warn};
use memmap2::Mmap;

use crate::utils::{FsUtils, TimeUtils};
use super::{CacheStats, VerificationResult};

/// Storage backend for the cache
#[derive(Debug, Clone)]
pub struct Storage {
    cache_dir: PathBuf,
}

impl Storage {
    pub fn new(cache_dir: PathBuf) -> Self {
        Self { cache_dir }
    }
    
    /// Check if a package exists in storage
    pub fn exists(&self, name: &str, version: &str) -> bool {
        self.get_path(name, version).exists()
    }
    
    /// Get the storage path for a package
    pub fn get_path(&self, name: &str, version: &str) -> PathBuf {
        let normalized_name = crate::utils::Utils::normalize_package_name(name);
        self.cache_dir
            .join("packages")
            .join(&normalized_name)
            .join(version)
    }
    
    /// Store package data
    pub async fn store(&self, name: &str, version: &str, data: &[u8]) -> Result<PathBuf> {
        let path = self.get_path(name, version);
        
        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }
        
        // Write data to file
        fs::write(&path, data).await
            .context("Failed to write package to cache")?;
        
        debug!("Stored {}@{} in cache", name, version);
        Ok(path)
    }

    /// Retrieve package data (traditional method)
    pub async fn retrieve(&self, name: &str, version: &str) -> Result<Vec<u8>> {
        let path = self.get_path(name, version);

        if !path.exists() {
            return Err(anyhow::anyhow!("Package {}@{} not found in cache", name, version));
        }

        let data = fs::read(&path).await
            .context("Failed to read package from cache")?;

        debug!("Retrieved {}@{} from cache", name, version);
        Ok(data)
    }

    /// Zero-copy retrieval using memory mapping for ultra-fast cache hits
    pub fn retrieve_mmap(&self, name: &str, version: &str) -> Result<Mmap> {
        let path = self.get_path(name, version);

        if !path.exists() {
            return Err(anyhow::anyhow!("Package {}@{} not found in cache", name, version));
        }

        let file = std::fs::File::open(&path)
            .context("Failed to open cached package file")?;

        let mmap = unsafe { Mmap::map(&file) }
            .context("Failed to memory map cached package")?;

        debug!("Zero-copy retrieved {}@{} from cache", name, version);
        Ok(mmap)
    }

    /// Remove a package from storage
    pub async fn remove(&self, name: &str, version: &str) -> Result<()> {
        let path = self.get_path(name, version);
        
        if path.exists() {
            if path.is_file() {
                fs::remove_file(&path).await?;
            } else {
                FsUtils::remove_dir_all(&path).await?;
            }
            
            // Remove empty parent directories
            self.cleanup_empty_dirs(&path).await?;
        }
        
        Ok(())
    }
    
    /// Clear all cached packages
    pub async fn clear(&self) -> Result<u64> {
        let packages_dir = self.cache_dir.join("packages");
        
        if !packages_dir.exists() {
            return Ok(0);
        }
        
        // Calculate size before removal
        let size = FsUtils::get_dir_size(&packages_dir).await.unwrap_or(0);
        
        // Remove the entire packages directory
        FsUtils::remove_dir_all(&packages_dir).await?;
        
        info!("Cleared cache, freed {} bytes", size);
        Ok(size)
    }
    
    /// Get cache statistics
    pub async fn get_stats(&self) -> Result<CacheStats> {
        let packages_dir = self.cache_dir.join("packages");
        
        if !packages_dir.exists() {
            return Ok(CacheStats {
                total_packages: 0,
                total_size_bytes: 0,
                cache_dir: self.cache_dir.clone(),
                last_cleanup: None,
            });
        }
        
        let mut total_packages = 0;
        let total_size_bytes = FsUtils::get_dir_size(&packages_dir).await.unwrap_or(0);
        
        // Count packages
        let mut entries = fs::read_dir(&packages_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let mut version_entries = fs::read_dir(entry.path()).await?;
                while let Some(_version_entry) = version_entries.next_entry().await? {
                    total_packages += 1;
                }
            }
        }
        
        Ok(CacheStats {
            total_packages,
            total_size_bytes,
            cache_dir: self.cache_dir.clone(),
            last_cleanup: None, // TODO: Track last cleanup time
        })
    }
    
    /// Verify cache integrity
    pub async fn verify(&self) -> Result<VerificationResult> {
        let packages_dir = self.cache_dir.join("packages");
        
        if !packages_dir.exists() {
            return Ok(VerificationResult {
                total_packages: 0,
                verified_packages: 0,
                corrupted_packages: Vec::new(),
                missing_packages: Vec::new(),
            });
        }
        
        let mut total_packages = 0;
        let mut verified_packages = 0;
        let mut corrupted_packages = Vec::new();
        
        // Walk through all packages
        let mut entries = fs::read_dir(&packages_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let package_name = entry.file_name().to_string_lossy().to_string();
                
                let mut version_entries = fs::read_dir(entry.path()).await?;
                while let Some(version_entry) = version_entries.next_entry().await? {
                    total_packages += 1;
                    let version = version_entry.file_name().to_string_lossy().to_string();
                    
                    // Basic verification - check if file exists and is readable
                    match fs::metadata(version_entry.path()).await {
                        Ok(_) => verified_packages += 1,
                        Err(_) => {
                            corrupted_packages.push(format!("{}@{}", package_name, version));
                        }
                    }
                }
            }
        }
        
        Ok(VerificationResult {
            total_packages,
            verified_packages,
            corrupted_packages,
            missing_packages: Vec::new(), // TODO: Check against expected packages
        })
    }
    
    /// Prune old cache entries
    pub async fn prune(&self, max_age_days: u64) -> Result<u64> {
        let packages_dir = self.cache_dir.join("packages");
        
        if !packages_dir.exists() {
            return Ok(0);
        }
        
        let mut freed_bytes = 0;
        let mut entries = fs::read_dir(&packages_dir).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let mut version_entries = fs::read_dir(entry.path()).await?;
                
                while let Some(version_entry) = version_entries.next_entry().await? {
                    let metadata = version_entry.metadata().await?;
                    
                    if let Ok(modified) = metadata.modified() {
                        let age_days = TimeUtils::file_age_days(modified);
                        
                        if age_days > max_age_days {
                            let size = metadata.len();
                            
                            if version_entry.file_type().await?.is_file() {
                                fs::remove_file(version_entry.path()).await?;
                            } else {
                                FsUtils::remove_dir_all(version_entry.path()).await?;
                            }
                            
                            freed_bytes += size;
                            debug!("Pruned old cache entry: {}", version_entry.path().display());
                        }
                    }
                }
                
                // Remove empty package directories
                self.cleanup_empty_dirs(&entry.path()).await?;
            }
        }
        
        info!("Pruned cache, freed {} bytes", freed_bytes);
        Ok(freed_bytes)
    }
    
    /// Remove empty directories
    async fn cleanup_empty_dirs(&self, path: &Path) -> Result<()> {
        let mut current_path = path.parent();

        while let Some(parent) = current_path {
            if parent == self.cache_dir || !parent.exists() {
                break;
            }

            // Check if directory is empty
            let mut entries = fs::read_dir(parent).await?;
            if entries.next_entry().await?.is_none() {
                fs::remove_dir(parent).await?;
                debug!("Removed empty directory: {}", parent.display());
                current_path = parent.parent();
            } else {
                break;
            }
        }

        Ok(())
    }
}
