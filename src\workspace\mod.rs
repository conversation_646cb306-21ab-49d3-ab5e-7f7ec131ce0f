use anyhow::{Result, Context};
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Workspace configuration and management
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Workspace {
    /// Root directory of the workspace
    pub root: PathBuf,
    /// List of workspace packages
    pub packages: Vec<WorkspacePackage>,
    /// Workspace configuration
    pub config: WorkspaceConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WorkspacePackage {
    /// Package name
    pub name: String,
    /// Package version
    pub version: String,
    /// Relative path from workspace root
    pub path: PathBuf,
    /// Package dependencies
    pub dependencies: HashMap<String, String>,
    /// Dev dependencies
    pub dev_dependencies: HashMap<String, String>,
    /// Peer dependencies
    pub peer_dependencies: HashMap<String, String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WorkspaceConfig {
    /// Shared dependencies across workspace
    pub shared_dependencies: HashMap<String, String>,
    /// Workspace-level scripts
    pub scripts: HashMap<String, String>,
    /// Hoisting configuration
    pub hoist: bool,
    /// Private packages (not published)
    pub private: bool,
}

impl Workspace {
    /// Discover workspace from current directory
    pub fn discover() -> Result<Option<Self>> {
        let current_dir = std::env::current_dir()
            .context("Failed to get current directory")?;
        
        Self::discover_from_path(&current_dir)
    }

    /// Discover workspace from a specific path
    pub fn discover_from_path(start_path: &Path) -> Result<Option<Self>> {
        let mut current = start_path.to_path_buf();
        
        loop {
            // Check for package.json with workspaces field
            let package_json_path = current.join("package.json");
            if package_json_path.exists() {
                if let Ok(content) = std::fs::read_to_string(&package_json_path) {
                    if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&content) {
                        if parsed.get("workspaces").is_some() {
                            return Self::load_from_package_json(&current, &parsed);
                        }
                    }
                }
            }

            // Check for nx.json (Nx workspace)
            let nx_json_path = current.join("nx.json");
            if nx_json_path.exists() {
                return Self::load_from_nx_json(&current);
            }

            // Check for lerna.json (Lerna workspace)
            let lerna_json_path = current.join("lerna.json");
            if lerna_json_path.exists() {
                return Self::load_from_lerna_json(&current);
            }

            // Move up one directory
            if let Some(parent) = current.parent() {
                current = parent.to_path_buf();
            } else {
                break;
            }
        }

        Ok(None)
    }

    /// Load workspace from package.json with workspaces field
    fn load_from_package_json(root: &Path, package_json: &serde_json::Value) -> Result<Option<Self>> {
        let workspaces = package_json.get("workspaces");
        if workspaces.is_none() {
            return Ok(None);
        }

        let mut packages = Vec::new();
        let workspace_patterns = if let Some(array) = workspaces.and_then(|w| w.as_array()) {
            array.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>()
        } else if let Some(obj) = workspaces.and_then(|w| w.as_object()) {
            if let Some(packages_array) = obj.get("packages").and_then(|p| p.as_array()) {
                packages_array.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>()
            } else {
                return Ok(None);
            }
        } else {
            return Ok(None);
        };

        // Find all packages matching the patterns
        for pattern in workspace_patterns {
            let package_paths = Self::glob_packages(root, pattern)?;
            for package_path in package_paths {
                if let Some(package) = Self::load_package(&package_path)? {
                    packages.push(package);
                }
            }
        }

        let config = WorkspaceConfig {
            shared_dependencies: HashMap::new(),
            scripts: HashMap::new(),
            hoist: true,
            private: package_json.get("private").and_then(|p| p.as_bool()).unwrap_or(false),
        };

        Ok(Some(Workspace {
            root: root.to_path_buf(),
            packages,
            config,
        }))
    }

    /// Load workspace from nx.json
    fn load_from_nx_json(root: &Path) -> Result<Option<Self>> {
        // Simplified Nx workspace support
        let packages = Self::find_nx_packages(root)?;
        
        let config = WorkspaceConfig {
            shared_dependencies: HashMap::new(),
            scripts: HashMap::new(),
            hoist: true,
            private: false,
        };

        Ok(Some(Workspace {
            root: root.to_path_buf(),
            packages,
            config,
        }))
    }

    /// Load workspace from lerna.json
    fn load_from_lerna_json(root: &Path) -> Result<Option<Self>> {
        let lerna_json_path = root.join("lerna.json");
        let content = std::fs::read_to_string(lerna_json_path)
            .context("Failed to read lerna.json")?;
        
        let lerna_config: serde_json::Value = serde_json::from_str(&content)
            .context("Failed to parse lerna.json")?;

        let mut packages = Vec::new();
        if let Some(packages_array) = lerna_config.get("packages").and_then(|p| p.as_array()) {
            for pattern in packages_array.iter().filter_map(|v| v.as_str()) {
                let package_paths = Self::glob_packages(root, pattern)?;
                for package_path in package_paths {
                    if let Some(package) = Self::load_package(&package_path)? {
                        packages.push(package);
                    }
                }
            }
        }

        let config = WorkspaceConfig {
            shared_dependencies: HashMap::new(),
            scripts: HashMap::new(),
            hoist: true,
            private: false,
        };

        Ok(Some(Workspace {
            root: root.to_path_buf(),
            packages,
            config,
        }))
    }

    /// Find packages using glob patterns
    fn glob_packages(root: &Path, pattern: &str) -> Result<Vec<PathBuf>> {
        let mut packages = Vec::new();
        
        // Simple glob implementation for common patterns
        if pattern.ends_with("/*") {
            let base_path = root.join(&pattern[..pattern.len() - 2]);
            if base_path.exists() && base_path.is_dir() {
                if let Ok(entries) = std::fs::read_dir(base_path) {
                    for entry in entries {
                        if let Ok(entry) = entry {
                            let path = entry.path();
                            if path.is_dir() && path.join("package.json").exists() {
                                packages.push(path);
                            }
                        }
                    }
                }
            }
        } else {
            let package_path = root.join(pattern);
            if package_path.exists() && package_path.join("package.json").exists() {
                packages.push(package_path);
            }
        }

        Ok(packages)
    }

    /// Find Nx packages
    fn find_nx_packages(root: &Path) -> Result<Vec<WorkspacePackage>> {
        let mut packages = Vec::new();
        
        // Look for packages in common Nx locations
        let common_paths = ["apps", "libs", "packages"];
        
        for base_path in common_paths {
            let full_path = root.join(base_path);
            if full_path.exists() && full_path.is_dir() {
                if let Ok(entries) = std::fs::read_dir(full_path) {
                    for entry in entries {
                        if let Ok(entry) = entry {
                            let path = entry.path();
                            if path.is_dir() && path.join("package.json").exists() {
                                if let Some(package) = Self::load_package(&path)? {
                                    packages.push(package);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(packages)
    }

    /// Load a single package from its directory
    fn load_package(package_path: &Path) -> Result<Option<WorkspacePackage>> {
        let package_json_path = package_path.join("package.json");
        if !package_json_path.exists() {
            return Ok(None);
        }

        let content = std::fs::read_to_string(package_json_path)
            .context("Failed to read package.json")?;
        
        let package_json: serde_json::Value = serde_json::from_str(&content)
            .context("Failed to parse package.json")?;

        let name = package_json.get("name")
            .and_then(|n| n.as_str())
            .unwrap_or("unknown")
            .to_string();

        let version = package_json.get("version")
            .and_then(|v| v.as_str())
            .unwrap_or("0.0.0")
            .to_string();

        let dependencies = Self::extract_dependencies(&package_json, "dependencies");
        let dev_dependencies = Self::extract_dependencies(&package_json, "devDependencies");
        let peer_dependencies = Self::extract_dependencies(&package_json, "peerDependencies");

        Ok(Some(WorkspacePackage {
            name,
            version,
            path: package_path.to_path_buf(),
            dependencies,
            dev_dependencies,
            peer_dependencies,
        }))
    }

    /// Extract dependencies from package.json
    fn extract_dependencies(package_json: &serde_json::Value, key: &str) -> HashMap<String, String> {
        package_json.get(key)
            .and_then(|deps| deps.as_object())
            .map(|deps| {
                deps.iter()
                    .filter_map(|(k, v)| v.as_str().map(|version| (k.clone(), version.to_string())))
                    .collect()
            })
            .unwrap_or_default()
    }

    /// Get all workspace package names
    pub fn package_names(&self) -> Vec<String> {
        self.packages.iter().map(|p| p.name.clone()).collect()
    }

    /// Check if a package is part of this workspace
    pub fn contains_package(&self, name: &str) -> bool {
        self.packages.iter().any(|p| p.name == name)
    }

    /// Get package by name
    pub fn get_package(&self, name: &str) -> Option<&WorkspacePackage> {
        self.packages.iter().find(|p| p.name == name)
    }

    /// Get workspace root
    pub fn root(&self) -> &Path {
        &self.root
    }
}
