use colored::*;
use std::io::{self, Write};

/// Clean, minimal UI similar to npm's output style
#[derive(Debug, <PERSON>lone)]
pub struct CleanUI {
    quiet: bool,
}

impl CleanUI {
    pub fn new() -> Self {
        Self { quiet: false }
    }

    pub fn new_quiet() -> Self {
        Self { quiet: true }
    }

    /// Print a simple message without any formatting
    pub fn info(&self, message: &str) {
        if !self.quiet {
            println!("{}", message);
        }
    }

    /// Print a success message with minimal formatting
    pub fn success(&self, message: &str) {
        if !self.quiet {
            println!("{}", message);
        }
    }

    /// Print an error message
    pub fn error(&self, message: &str) {
        eprintln!("{}: {}", "npm ERR!".red().bold(), message);
    }

    /// Print a warning message
    pub fn warning(&self, message: &str) {
        if !self.quiet {
            println!("{}: {}", "npm WARN".yellow().bold(), message);
        }
    }

    /// Print installation progress in npm style
    pub fn install_progress(&self, current: usize, total: usize, package: &str) {
        if !self.quiet && total > 1 {
            print!("\r{}/{} {}", current, total, package);
            io::stdout().flush().unwrap();
        }
    }

    /// Clear the current line
    pub fn clear_line(&self) {
        if !self.quiet {
            print!("\r\x1b[K");
            io::stdout().flush().unwrap();
        }
    }

    /// Print final installation summary
    pub fn install_summary(&self, packages: usize, time: f64) {
        if !self.quiet {
            if packages == 1 {
                println!("\nadded 1 package in {:.1}s", time);
            } else {
                println!("\nadded {} packages in {:.1}s", packages, time);
            }
        }
    }

    /// Print script execution message
    pub fn script_start(&self, script: &str) {
        if !self.quiet {
            println!("\n> {}", script);
        }
    }

    /// Print package removal message
    pub fn removed(&self, package: &str) {
        if !self.quiet {
            println!("removed {}", package);
        }
    }

    /// Print initialization message
    pub fn init_success(&self, name: &str) {
        if !self.quiet {
            println!("Wrote to {}/package.json", name);
        }
    }

    /// Print dependency type during installation
    pub fn dependency_type(&self, dep_type: &str, count: usize) {
        if !self.quiet && count > 0 {
            match dep_type {
                "dev" => println!("\nadded {} packages, and audited {} packages in total", count, count),
                "peer" => println!("peer dependencies: {}", count),
                "optional" => println!("optional dependencies: {}", count),
                _ => {}
            }
        }
    }

    /// Print audit summary (minimal)
    pub fn audit_summary(&self, vulnerabilities: usize) {
        if !self.quiet {
            if vulnerabilities == 0 {
                println!("\nfound 0 vulnerabilities");
            } else {
                println!("\nfound {} vulnerabilities", vulnerabilities);
            }
        }
    }

    /// Print package version info during installation
    pub fn package_version(&self, name: &str, version: &str) {
        if !self.quiet {
            println!("+ {}@{}", name, version);
        }
    }

    /// Print simple status without progress bars
    pub fn status(&self, message: &str) {
        if !self.quiet {
            println!("{}", message);
        }
    }

    /// Print npm-style command help
    pub fn help(&self, command: &str, description: &str) {
        println!("  {:<20} {}", command.cyan(), description);
    }

    /// Print version information
    pub fn version(&self, version: &str) {
        println!("{}", version);
    }

    /// Print npm-style error with code
    pub fn error_with_code(&self, code: &str, message: &str) {
        eprintln!("{} {} {}", "npm ERR!".red().bold(), code.red(), message);
    }

    /// Print simple list item
    pub fn list_item(&self, item: &str) {
        if !self.quiet {
            println!("  {}", item);
        }
    }
}

impl Default for CleanUI {
    fn default() -> Self {
        Self::new()
    }
}
