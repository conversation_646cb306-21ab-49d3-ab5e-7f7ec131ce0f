# nx - Ultra-Fast Package Manager

A blazingly fast npm package manager written in Rust, designed to be a drop-in replacement for npm with superior performance and clean output.

## Features

- 🚀 **Ultra-fast installations** - Sub-3 second installation times for most packages
- 🔄 **50+ concurrent downloads** - Parallel download engine with intelligent connection pooling
- 🎯 **npm compatibility** - Drop-in replacement for npm with full command compatibility
- 🧹 **Clean output** - Minimal, beautiful output similar to npm's style
- 📦 **Smart caching** - Advanced caching system with deduplication
- 🔒 **Security focused** - Integrity verification and safe extraction
- 🌐 **Cross-platform** - Works on Windows, macOS, and Linux
- 📊 **Workspace support** - Full monorepo and workspace support

## Installation

### Via npm (Recommended)

```bash
npm install -g nx-package-manager
```

### Via Cargo (Build from source)

```bash
cargo install nx-package-manager
```

### Download Binary

Download pre-built binaries from [GitHub Releases](https://github.com/nx-package-manager/nx/releases).

## Usage

nx is designed to be a drop-in replacement for npm. All your familiar npm commands work:

### Package Installation

```bash
# Install dependencies from package.json
nx install

# Install specific packages
nx install express react lodash

# Install dev dependencies
nx install -D typescript @types/node

# Install globally
nx install -g nx-package-manager
```

### Script Execution

```bash
# Run npm scripts
nx run build
nx run test
nx run dev

# Shortcuts
nx start    # equivalent to nx run start
nx test     # equivalent to nx run test

# Run JavaScript files directly
nx run index.js
```

### Package Management

```bash
# Uninstall packages
nx uninstall lodash
nx remove express    # alias for uninstall

# List installed packages
nx list
nx ls               # alias for list

# Initialize new project
nx init
nx init --name my-project
```

### Other Commands

```bash
# Show help
nx --help
nx help

# Show version
nx --version

# Cache management
nx cache clean
nx cache verify
```

## Command Aliases

nx supports all npm command aliases for seamless migration:

- `nx i` → `nx install`
- `nx add` → `nx install`
- `nx rm` → `nx uninstall`
- `nx remove` → `nx uninstall`
- `nx r` → `nx uninstall`
- `nx un` → `nx uninstall`
- `nx ls` → `nx list`
- `nx t` → `nx test`

## Performance

nx is designed for speed:

- **Sub-3 second installations** for most packages
- **50+ concurrent downloads** with intelligent connection pooling
- **Smart caching** with deduplication and integrity verification
- **Memory-efficient** operations with optimized algorithms
- **Bandwidth optimization** with compression and delta updates

## Node.js API

nx also provides a Node.js API for programmatic usage:

```javascript
const { nx } = require('nx-package-manager');

// Install packages
await nx.install(['express', 'lodash']);

// Install dev dependencies
await nx.install(['typescript'], { dev: true });

// Run scripts
await nx.run('build');

// List packages
const result = await nx.list();
console.log(result.stdout);
```

## Configuration

nx respects npm configuration files and environment variables:

- `.npmrc` files (project and global)
- `NPM_CONFIG_*` environment variables
- npm registry settings
- Authentication tokens

## Workspace Support

nx fully supports npm workspaces and monorepos:

```json
{
  "name": "my-monorepo",
  "workspaces": [
    "packages/*",
    "apps/*"
  ]
}
```

## Migration from npm

Migrating from npm to nx is seamless:

1. Install nx: `npm install -g nx-package-manager`
2. Replace `npm` with `nx` in your commands
3. Enjoy faster installations!

Your existing `package.json`, `package-lock.json`, and `.npmrc` files work without modification.

## Compatibility

- **Node.js**: 16.0.0 or higher
- **Platforms**: Windows, macOS, Linux
- **Architectures**: x64, ARM64
- **npm**: Compatible with npm v6, v7, v8, v9, v10

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

MIT License - see [LICENSE](LICENSE) for details.

## Support

- 📖 [Documentation](https://github.com/nx-package-manager/nx/wiki)
- 🐛 [Issue Tracker](https://github.com/nx-package-manager/nx/issues)
- 💬 [Discussions](https://github.com/nx-package-manager/nx/discussions)

---

Made with ❤️ by the nx team. Built with Rust for maximum performance.
