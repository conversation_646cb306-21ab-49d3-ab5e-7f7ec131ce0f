use anyhow::Result;
use clap::Args;
use serde_json::Value;
use tracing::{info, warn};

use crate::registry::Registry;
use crate::ui::UI;

/// Search for packages in the npm registry
#[derive(Debug, Args)]
pub struct SearchArgs {
    /// Search query
    query: String,
    
    /// Maximum number of results to show
    #[arg(short, long, default_value = "20")]
    limit: usize,
    
    /// Show detailed information
    #[arg(short, long)]
    detailed: bool,
    
    /// Search only in package names
    #[arg(long)]
    names_only: bool,
}

pub async fn search(args: SearchArgs) -> Result<()> {
    let ui = UI::new();
    let registry = Registry::new();
    
    ui.info(&format!("🔍 Searching for packages matching '{}'...", args.query));
    
    let spinner = ui.create_spinner("Searching npm registry");
    
    // Search packages using npm registry search API
    let search_results = registry.search_packages(&args.query, args.limit).await?;
    
    spinner.finish_with_message("✓ Search completed");
    
    if search_results.is_empty() {
        ui.warning(&format!("No packages found matching '{}'", args.query));
        return Ok(());
    }
    
    ui.success(&format!("Found {} packages:", search_results.len()));
    println!();
    
    for (i, package) in search_results.iter().enumerate() {
        if i >= args.limit {
            break;
        }
        
        let name = package.get("name").and_then(|n| n.as_str()).unwrap_or("unknown");
        let version = package.get("version").and_then(|v| v.as_str()).unwrap_or("unknown");
        let description = package.get("description")
            .and_then(|d| d.as_str())
            .unwrap_or("No description available");
        
        if args.detailed {
            // Detailed view
            println!("📦 {}", name);
            println!("   Version: {}", version);
            println!("   Description: {}", description);
            
            if let Some(keywords) = package.get("keywords").and_then(|k| k.as_array()) {
                let keyword_strings: Vec<String> = keywords
                    .iter()
                    .filter_map(|k| k.as_str())
                    .map(|s| s.to_string())
                    .collect();
                if !keyword_strings.is_empty() {
                    println!("   Keywords: {}", keyword_strings.join(", "));
                }
            }
            
            if let Some(author) = package.get("author") {
                if let Some(author_name) = author.get("name").and_then(|n| n.as_str()) {
                    println!("   Author: {}", author_name);
                } else if let Some(author_str) = author.as_str() {
                    println!("   Author: {}", author_str);
                }
            }
            
            if let Some(homepage) = package.get("homepage").and_then(|h| h.as_str()) {
                println!("   Homepage: {}", homepage);
            }
            
            println!();
        } else {
            // Compact view
            println!("{:<30} {:<10} {}", name, version, description);
        }
    }
    
    if search_results.len() >= args.limit {
        ui.info(&format!("Showing first {} results. Use --limit to see more.", args.limit));
    }
    
    Ok(())
}
