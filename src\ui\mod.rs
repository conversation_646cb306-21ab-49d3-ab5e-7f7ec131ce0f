pub mod progress;
pub mod spinner;
pub mod theme;
pub mod clean;

use colored::*;
use indicatif::{ProgressBar, ProgressStyle, MultiProgress, ProgressState};
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};

pub use progress::*;
pub use spinner::*;
pub use theme::*;
pub use clean::*;

/// Enhanced UI system with beautiful progress bars and animations
#[derive(Debug, Clone)]
pub struct UI {
    multi_progress: Arc<MultiProgress>,
    theme: Theme,
    start_time: Arc<Mutex<Option<Instant>>>,
}

impl UI {
    pub fn new() -> Self {
        Self {
            multi_progress: Arc::new(MultiProgress::new()),
            theme: Theme::default(),
            start_time: Arc::new(Mutex::new(None)),
        }
    }

    /// Start timing for operations
    pub fn start_timer(&self) {
        *self.start_time.lock().unwrap() = Some(Instant::now());
    }

    /// Get elapsed time since start
    pub fn elapsed(&self) -> Duration {
        self.start_time.lock().unwrap()
            .map(|start| start.elapsed())
            .unwrap_or_default()
    }

    /// Create a beautiful download progress bar
    pub fn create_download_progress(&self, total_bytes: u64, package_name: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total_bytes));

        let style = ProgressStyle::with_template(
            "🚀 {msg} [{elapsed_precise}] [{wide_bar:.bright_cyan/bright_blue}] {bytes}/{total_bytes} ({bytes_per_sec}, ETA: {eta})"
        )
        .unwrap()
        .with_key("eta", |state: &ProgressState, w: &mut dyn std::fmt::Write| {
            write!(w, "{:.1}s", state.eta().as_secs_f64()).unwrap()
        })
        .progress_chars("█▉▊▋▌▍▎▏  ");

        pb.set_style(style);
        pb.set_message(format!("{}", package_name.bright_cyan().bold()));
        pb
    }

    /// Create a beautiful installation progress bar
    pub fn create_install_progress(&self, total_packages: u64) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total_packages));

        let style = ProgressStyle::with_template(
            "⚡ {msg} [{elapsed_precise}] [{wide_bar:.bright_green/bright_yellow}] {pos}/{len} packages ({per_sec}/s)"
        )
        .unwrap()
        .progress_chars("█▉▊▋▌▍▎▏  ");

        pb.set_style(style);
        pb.set_message("Installing packages".bright_white().bold().to_string());
        pb
    }

    /// Create a beautiful resolution spinner
    pub fn create_resolution_spinner(&self) -> ProgressBar {
        let spinner = self.multi_progress.add(ProgressBar::new_spinner());

        let style = ProgressStyle::with_template(
            "{spinner:.blue} {msg} {elapsed_precise}"
        )
        .unwrap()
        .tick_strings(&[
            "⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"
        ]);

        spinner.set_style(style);
        spinner.set_message("🔍 Resolving dependencies".bright_blue().to_string());
        spinner.enable_steady_tick(Duration::from_millis(80));
        spinner
    }

    /// Create a beautiful extraction spinner
    pub fn create_extraction_spinner(&self, package_name: &str) -> ProgressBar {
        let spinner = self.multi_progress.add(ProgressBar::new_spinner());

        let style = ProgressStyle::with_template(
            "{spinner:.yellow} {msg} {elapsed_precise}"
        )
        .unwrap()
        .tick_strings(&[
            "📦", "📦", "📦", "📦", "📦", "📦", "📦", "📦"
        ]);

        spinner.set_style(style);
        spinner.set_message(format!("📂 Extracting {}", package_name.bright_yellow()));
        spinner.enable_steady_tick(Duration::from_millis(150));
        spinner
    }

    /// Create a beautiful linking spinner
    pub fn create_linking_spinner(&self) -> ProgressBar {
        let spinner = self.multi_progress.add(ProgressBar::new_spinner());

        let style = ProgressStyle::with_template(
            "{spinner:.magenta} {msg} {elapsed_precise}"
        )
        .unwrap()
        .tick_strings(&[
            "🔗", "🔗", "🔗", "🔗", "🔗", "🔗", "🔗", "🔗"
        ]);

        spinner.set_style(style);
        spinner.set_message("🔗 Linking binaries".bright_magenta().to_string());
        spinner.enable_steady_tick(Duration::from_millis(120));
        spinner
    }

    /// Create a progress bar for any operation
    pub fn create_progress_bar(&self, total: u64, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total));
        pb.set_style(self.theme.progress_style());
        pb.set_message(message.to_string());
        pb
    }

    /// Create a spinner for indeterminate operations
    pub fn create_spinner(&self, message: &str) -> ProgressBar {
        let spinner = self.multi_progress.add(ProgressBar::new_spinner());
        spinner.set_style(self.theme.spinner_style());
        spinner.set_message(message.to_string());
        spinner.enable_steady_tick(Duration::from_millis(100));
        spinner
    }

    /// Display success message with beautiful formatting
    pub fn success(&self, message: &str) {
        println!("{} {}", "✓".bright_green().bold(), message.bright_white());
    }

    /// Display error message with beautiful formatting
    pub fn error(&self, message: &str) {
        eprintln!("{} {}", "✗".bright_red().bold(), message.bright_white());
    }

    /// Display warning message with beautiful formatting
    pub fn warning(&self, message: &str) {
        println!("{} {}", "⚠".bright_yellow().bold(), message.bright_white());
    }

    /// Display info message with beautiful formatting
    pub fn info(&self, message: &str) {
        println!("{} {}", "ℹ".bright_blue().bold(), message.bright_white());
    }

    /// Display step message with beautiful formatting
    pub fn step(&self, message: &str) {
        println!("{} {}", "→".bright_blue().bold(), message.bright_white());
    }

    /// Display completion message with timing
    pub fn complete(&self, message: &str) {
        let elapsed = self.elapsed();
        println!("{} {} {}",
            "✓".bright_green().bold(),
            message.bright_white(),
            format!("in {:.2}s", elapsed.as_secs_f64()).bright_black()
        );
    }

    /// Display package download success with details
    pub fn package_success(&self, name: &str, version: &str, size: &str, duration: &str) {
        println!("  {} Downloaded {}@{} ({} in {})",
            "✓".bright_green().bold(),
            name.bright_cyan().bold(),
            version.bright_green(),
            size.bright_blue(),
            duration.bright_yellow()
        );
    }

    /// Display final installation success summary
    pub fn final_success(&self, count: usize, total_time: &str) {
        println!("\n{} Successfully installed {} packages in {}",
            "🎉".to_string(),
            count.to_string().bright_green().bold(),
            total_time.bright_yellow().bold()
        );
    }

    /// Display package installation progress
    pub fn package_installing(&self, name: &str, version: &str) {
        println!("  {} Installing {}@{}",
            "📦".to_string(),
            name.bright_cyan().bold(),
            version.bright_green()
        );
    }

    /// Clear all progress bars
    pub fn clear(&self) {
        self.multi_progress.clear().ok();
    }
}

impl Default for UI {
    fn default() -> Self {
        Self::new()
    }
}
