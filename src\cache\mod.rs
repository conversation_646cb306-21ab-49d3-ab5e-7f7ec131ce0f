pub mod storage;
pub mod integrity;

use anyhow::{Result, Context};
use std::path::PathBuf;
use tracing::{debug, info};

pub use storage::*;
pub use integrity::*;

/// Cache manager for package storage and retrieval
#[derive(Debug, Clone)]
pub struct Cache {
    cache_dir: PathBuf,
    storage: Storage,
    integrity_checker: IntegrityChecker,
}

impl Cache {
    pub fn new(cache_dir: PathBuf) -> Result<Self> {
        std::fs::create_dir_all(&cache_dir)
            .context("Failed to create cache directory")?;
        
        let storage = Storage::new(cache_dir.clone());
        let integrity_checker = IntegrityChecker::new();
        
        Ok(Self {
            cache_dir,
            storage,
            integrity_checker,
        })
    }
    
    /// Check if a package is cached
    pub fn is_cached(&self, name: &str, version: &str) -> bool {
        self.storage.exists(name, version)
    }
    
    /// Get the path to a cached package
    pub fn get_package_path(&self, name: &str, version: &str) -> Option<PathBuf> {
        if self.is_cached(name, version) {
            Some(self.storage.get_path(name, version))
        } else {
            None
        }
    }
    
    /// Store a package in the cache
    pub async fn store_package(
        &self,
        name: &str,
        version: &str,
        data: &[u8],
        integrity: Option<&str>,
    ) -> Result<PathBuf> {
        // Verify integrity if provided
        if let Some(expected_integrity) = integrity {
            self.integrity_checker.verify(data, expected_integrity)?;
        }
        
        // Store the package
        let path = self.storage.store(name, version, data).await?;
        
        debug!("Cached {}@{} at {}", name, version, path.display());
        Ok(path)
    }
    
    /// Remove a package from the cache
    pub async fn remove_package(&self, name: &str, version: &str) -> Result<()> {
        self.storage.remove(name, version).await?;
        debug!("Removed {}@{} from cache", name, version);
        Ok(())
    }
    
    /// Clear all cached packages
    pub async fn clear(&self) -> Result<u64> {
        let freed_bytes = self.storage.clear().await?;
        info!("Cleared cache, freed {} bytes", freed_bytes);
        Ok(freed_bytes)
    }
    
    /// Get cache statistics
    pub async fn get_stats(&self) -> Result<CacheStats> {
        self.storage.get_stats().await
    }
    
    /// Verify cache integrity
    pub async fn verify(&self) -> Result<VerificationResult> {
        self.storage.verify().await
    }
    
    /// Prune unused cache entries
    pub async fn prune(&self, max_age_days: u64) -> Result<u64> {
        self.storage.prune(max_age_days).await
    }
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub total_packages: u64,
    pub total_size_bytes: u64,
    pub cache_dir: PathBuf,
    pub last_cleanup: Option<std::time::SystemTime>,
}

/// Cache verification result
#[derive(Debug, Clone)]
pub struct VerificationResult {
    pub total_packages: u64,
    pub verified_packages: u64,
    pub corrupted_packages: Vec<String>,
    pub missing_packages: Vec<String>,
}
