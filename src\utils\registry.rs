use anyhow::{anyhow, Result};
use reqwest::{Client, Response};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, warn};

use crate::types::{PackageMetadata, VersionMetadata, DistInfo, Repository, Maintainer, RegistryConfig};

pub struct RegistryClient {
    client: Client,
    config: RegistryConfig,
}

impl RegistryClient {
    pub fn new(config: RegistryConfig) -> Result<Self> {
        let client = Client::builder()
            .user_agent("nx/1.0.0 (ultra-fast package manager)")
            .timeout(config.timeout)
            .connect_timeout(Duration::from_secs(5))
            .pool_max_idle_per_host(100) // High connection pooling
            .pool_idle_timeout(Duration::from_secs(90))
            .tcp_keepalive(Duration::from_secs(60))
            .tcp_nodelay(true)
            .gzip(true)
            .build()?;

        Ok(Self { client, config })
    }

    /// Get package metadata from npm registry
    pub async fn get_package_metadata(&self, name: &str) -> Result<PackageMetadata> {
        let url = format!("{}/{}", self.config.url.trim_end_matches('/'), name);
        debug!("Fetching metadata for package: {} from {}", name, url);

        let response = self.make_request(&url).await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("Package '{}' not found (status: {})", name, response.status()));
        }

        let package_data: Value = response.json().await?;
        self.parse_package_metadata(name, &package_data)
    }

    /// Download package tarball
    pub async fn download_package(&self, url: &str) -> Result<Response> {
        debug!("Downloading package from: {}", url);
        
        let response = self.make_request(url).await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("Failed to download package from {} (status: {})", url, response.status()));
        }

        Ok(response)
    }

    /// Search packages in npm registry
    pub async fn search_packages(&self, query: &str, limit: usize) -> Result<Vec<SearchResult>> {
        let url = format!("{}/-/v1/search?text={}&size={}", 
                         self.config.url.trim_end_matches('/'), 
                         urlencoding::encode(query), 
                         limit);
        
        debug!("Searching packages: {}", url);
        
        let response = self.make_request(&url).await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("Search failed (status: {})", response.status()));
        }

        let search_data: Value = response.json().await?;
        self.parse_search_results(&search_data)
    }

    /// Get package download statistics
    pub async fn get_download_stats(&self, name: &str, period: &str) -> Result<DownloadStats> {
        let url = format!("https://api.npmjs.org/downloads/point/{}/{}", period, name);
        
        let response = self.make_request(&url).await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("Failed to get download stats (status: {})", response.status()));
        }

        let stats_data: Value = response.json().await?;
        Ok(DownloadStats {
            downloads: stats_data["downloads"].as_u64().unwrap_or(0),
            period: period.to_string(),
            package: name.to_string(),
        })
    }

    async fn make_request(&self, url: &str) -> Result<Response> {
        let mut request = self.client.get(url);
        
        // Add authentication if available
        if let Some(token) = &self.config.auth_token {
            request = request.bearer_auth(token);
        }

        // Retry with exponential backoff
        let mut delay = Duration::from_millis(100);
        let mut last_error = None;

        for attempt in 0..self.config.retries {
            match request.try_clone().unwrap().send().await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    last_error = Some(e);
                    if attempt < self.config.retries - 1 {
                        warn!("Request failed (attempt {}), retrying in {:?}", attempt + 1, delay);
                        tokio::time::sleep(delay).await;
                        delay *= 2; // Exponential backoff
                    }
                }
            }
        }

        Err(anyhow!("Request failed after {} attempts: {:?}", self.config.retries, last_error))
    }

    fn parse_package_metadata(&self, name: &str, data: &Value) -> Result<PackageMetadata> {
        let versions_obj = data["versions"].as_object()
            .ok_or_else(|| anyhow!("Invalid package metadata: missing versions"))?;

        let mut versions = HashMap::new();
        
        for (version, version_data) in versions_obj {
            let version_meta = self.parse_version_metadata(version, version_data)?;
            versions.insert(version.clone(), version_meta);
        }

        let latest = data["dist-tags"]["latest"].as_str()
            .unwrap_or_else(|| versions.keys().next().unwrap())
            .to_string();

        let time = data["time"].as_object()
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            })
            .unwrap_or_default();

        let maintainers = data["maintainers"].as_array()
            .map(|arr| {
                arr.iter()
                    .filter_map(|m| {
                        Some(Maintainer {
                            name: m["name"].as_str()?.to_string(),
                            email: m["email"].as_str().map(|s| s.to_string()),
                        })
                    })
                    .collect()
            });

        let repository = data["repository"].as_object()
            .map(|repo| Repository {
                repo_type: repo["type"].as_str().map(|s| s.to_string()),
                url: repo["url"].as_str().unwrap_or("").to_string(),
                directory: repo["directory"].as_str().map(|s| s.to_string()),
            });

        Ok(PackageMetadata {
            name: name.to_string(),
            description: data["description"].as_str().map(|s| s.to_string()),
            versions,
            latest,
            time,
            maintainers,
            license: data["license"].as_str().map(|s| s.to_string()),
            homepage: data["homepage"].as_str().map(|s| s.to_string()),
            repository,
            bugs: data["bugs"]["url"].as_str().map(|s| s.to_string()),
            keywords: data["keywords"].as_array()
                .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()),
        })
    }

    fn parse_version_metadata(&self, version: &str, data: &Value) -> Result<VersionMetadata> {
        let dist = data["dist"].as_object()
            .ok_or_else(|| anyhow!("Invalid version metadata: missing dist"))?;

        let dist_info = DistInfo {
            tarball: dist["tarball"].as_str()
                .ok_or_else(|| anyhow!("Missing tarball URL"))?
                .to_string(),
            shasum: dist["shasum"].as_str()
                .ok_or_else(|| anyhow!("Missing shasum"))?
                .to_string(),
            integrity: dist["integrity"].as_str().map(|s| s.to_string()),
            unpacked_size: dist["unpackedSize"].as_u64(),
            file_count: dist["fileCount"].as_u64().map(|n| n as u32),
        };

        let dependencies = self.parse_dependencies(data, "dependencies");
        let dev_dependencies = self.parse_dependencies(data, "devDependencies");
        let peer_dependencies = self.parse_dependencies(data, "peerDependencies");
        let optional_dependencies = self.parse_dependencies(data, "optionalDependencies");

        let bin = data["bin"].as_object()
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            });

        let scripts = data["scripts"].as_object()
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            });

        let engines = data["engines"].as_object()
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            });

        let repository = data["repository"].as_object()
            .map(|repo| Repository {
                repo_type: repo["type"].as_str().map(|s| s.to_string()),
                url: repo["url"].as_str().unwrap_or("").to_string(),
                directory: repo["directory"].as_str().map(|s| s.to_string()),
            });

        Ok(VersionMetadata {
            name: data["name"].as_str().unwrap_or("").to_string(),
            version: version.to_string(),
            description: data["description"].as_str().map(|s| s.to_string()),
            main: data["main"].as_str().map(|s| s.to_string()),
            dependencies,
            dev_dependencies,
            peer_dependencies,
            optional_dependencies,
            bin,
            scripts,
            engines,
            os: data["os"].as_array()
                .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()),
            cpu: data["cpu"].as_array()
                .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()),
            dist: dist_info,
            license: data["license"].as_str().map(|s| s.to_string()),
            author: data["author"].as_str().map(|s| s.to_string()),
            homepage: data["homepage"].as_str().map(|s| s.to_string()),
            repository,
            bugs: data["bugs"]["url"].as_str().map(|s| s.to_string()),
            keywords: data["keywords"].as_array()
                .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()),
        })
    }

    fn parse_dependencies(&self, data: &Value, key: &str) -> HashMap<String, String> {
        data[key].as_object()
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            })
            .unwrap_or_default()
    }

    fn parse_search_results(&self, data: &Value) -> Result<Vec<SearchResult>> {
        let objects = data["objects"].as_array()
            .ok_or_else(|| anyhow!("Invalid search response"))?;

        let mut results = Vec::new();
        
        for obj in objects {
            let package = &obj["package"];
            
            results.push(SearchResult {
                name: package["name"].as_str().unwrap_or("").to_string(),
                version: package["version"].as_str().unwrap_or("").to_string(),
                description: package["description"].as_str().map(|s| s.to_string()),
                keywords: package["keywords"].as_array()
                    .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect())
                    .unwrap_or_default(),
                author: package["author"]["name"].as_str().map(|s| s.to_string()),
                date: package["date"].as_str().map(|s| s.to_string()),
                score: obj["score"]["final"].as_f64().unwrap_or(0.0),
            });
        }

        Ok(results)
    }
}

#[derive(Debug, Clone)]
pub struct SearchResult {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub keywords: Vec<String>,
    pub author: Option<String>,
    pub date: Option<String>,
    pub score: f64,
}

#[derive(Debug, Clone)]
pub struct DownloadStats {
    pub downloads: u64,
    pub period: String,
    pub package: String,
}
