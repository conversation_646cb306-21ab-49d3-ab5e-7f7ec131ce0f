use anyhow::{anyhow, Result};
use futures::stream::{self, StreamExt};
use indicatif::ProgressBar;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Instant;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use tracing::{debug, info, warn};

use crate::cache::UltraFastCache;
use crate::types::{DependencyTree, InstallConfig, InstallPackage, InstallPlan};
use crate::ui::{InstallSummary, UI};
use crate::utils::registry::RegistryClient;

pub struct UltraFastInstaller {
    registry: Arc<RegistryClient>,
    cache: Arc<UltraFastCache>,
    config: InstallConfig,
}

impl UltraFastInstaller {
    pub fn new(
        registry: Arc<RegistryClient>,
        cache: Arc<UltraFastCache>,
        config: InstallConfig,
    ) -> Self {
        Self {
            registry,
            cache,
            config,
        }
    }

    /// Ultra-fast installation with 2000+ concurrent operations
    pub async fn install_packages(
        &self,
        dependency_tree: &DependencyTree,
        ui: &UI,
    ) -> Result<Vec<InstallSummary>> {
        let start_time = Instant::now();
        let install_plan = self.create_install_plan(dependency_tree)?;
        
        info!("🚀 Installing {} packages with ultra-fast parallel processing", 
              install_plan.total_count);

        if self.config.dry_run {
            self.show_dry_run_plan(&install_plan, ui);
            return Ok(Vec::new());
        }

        // Create node_modules directory
        fs::create_dir_all(&self.config.node_modules_path).await?;

        // Phase 1: Ultra-fast parallel downloads (2000 concurrent)
        let download_progress = ui.create_install_progress(install_plan.total_count as u64);
        download_progress.set_message("Downloading packages...");

        let download_futures = install_plan.packages.iter().map(|package| {
            let installer = self.clone();
            let package = package.clone();
            let progress = download_progress.clone();
            async move {
                let result = installer.download_and_cache_package(&package).await;
                progress.inc(1);
                result.map(|_| package)
            }
        });

        let download_results: Vec<_> = stream::iter(download_futures)
            .buffer_unordered(2000) // Ultra-high concurrency for downloads
            .collect()
            .await;

        download_progress.finish_with_message("✅ Downloads completed");

        // Phase 2: Ultra-fast parallel extraction and installation
        let install_progress = ui.create_install_progress(install_plan.total_count as u64);
        install_progress.set_message("Installing packages...");

        let install_futures = download_results.into_iter().filter_map(|result| {
            match result {
                Ok(package) => Some(package),
                Err(e) => {
                    warn!("Download failed: {}", e);
                    None
                }
            }
        }).map(|package| {
            let installer = self.clone();
            let progress = install_progress.clone();
            async move {
                let start = Instant::now();
                let result = installer.extract_and_install_package(&package).await;
                progress.inc(1);
                
                match result {
                    Ok(_) => Ok(InstallSummary {
                        name: package.name.clone(),
                        version: package.version.clone(),
                        size: format_size(package.size.unwrap_or(0)),
                        time: format!("{:.2}s", start.elapsed().as_secs_f64()),
                    }),
                    Err(e) => Err(e),
                }
            }
        });

        let install_results: Vec<_> = stream::iter(install_futures)
            .buffer_unordered(1000) // High concurrency for installation
            .collect()
            .await;

        install_progress.finish_with_message("✅ Installation completed");

        // Collect successful installations
        let mut summaries = Vec::new();
        let mut failed_count = 0;

        for result in install_results {
            match result {
                Ok(summary) => summaries.push(summary),
                Err(e) => {
                    warn!("Installation failed: {}", e);
                    failed_count += 1;
                }
            }
        }

        let total_time = start_time.elapsed();
        info!("✅ Installed {} packages in {:.3}s ({:.0} packages/sec, {} failed)", 
              summaries.len(), 
              total_time.as_secs_f64(),
              summaries.len() as f64 / total_time.as_secs_f64(),
              failed_count);

        Ok(summaries)
    }

    async fn download_and_cache_package(&self, package: &InstallPackage) -> Result<()> {
        let cache_key = format!("{}@{}", package.name, package.version);
        
        // Check cache first
        if self.cache.has_package(&cache_key).await {
            debug!("Cache hit for {}", cache_key);
            return Ok(());
        }

        // Download package
        debug!("Downloading {}", cache_key);
        let response = self.registry.download_package(&package.resolved_url).await?;
        let bytes = response.bytes().await?;

        // Verify integrity if available
        if let Some(integrity) = &package.integrity {
            self.verify_integrity(&bytes, integrity)?;
        }

        // Cache the package
        self.cache.store_package(&cache_key, &bytes).await?;
        
        Ok(())
    }

    async fn extract_and_install_package(&self, package: &InstallPackage) -> Result<()> {
        let cache_key = format!("{}@{}", package.name, package.version);
        
        // Get package from cache
        let package_data = self.cache.get_package(&cache_key).await?;
        
        // Extract to node_modules
        let extract_path = self.config.node_modules_path.join(&package.name);
        self.extract_tarball(&package_data, &extract_path).await?;

        // Install binaries if any
        if let Some(bin) = &package.bin {
            self.install_binaries(bin, &extract_path).await?;
        }

        Ok(())
    }

    fn create_install_plan(&self, dependency_tree: &DependencyTree) -> Result<InstallPlan> {
        let mut packages = Vec::new();
        let mut total_size = 0;

        for (_, node) in &dependency_tree.nodes {
            if let Some(resolved) = &node.resolved {
                let install_path = self.config.node_modules_path.join(&resolved.name);
                
                let package = InstallPackage {
                    name: resolved.name.clone(),
                    version: resolved.version.clone(),
                    resolved_url: resolved.resolved_url.clone(),
                    integrity: resolved.integrity.clone(),
                    install_path,
                    size: resolved.size,
                    bin: resolved.bin.clone(),
                    engines: resolved.engines.clone(),
                    os: resolved.os.clone(),
                    cpu: resolved.cpu.clone(),
                };

                if let Some(size) = resolved.size {
                    total_size += size;
                }

                packages.push(package);
            }
        }

        Ok(InstallPlan {
            total_count: packages.len(),
            total_size,
            packages,
        })
    }

    async fn extract_tarball(&self, data: &[u8], extract_path: &Path) -> Result<()> {
        use flate2::read::GzDecoder;
        use std::io::Cursor;
        use tar::Archive;

        // Remove existing directory
        if extract_path.exists() {
            fs::remove_dir_all(extract_path).await?;
        }

        // Create parent directory
        if let Some(parent) = extract_path.parent() {
            fs::create_dir_all(parent).await?;
        }

        // Extract tarball
        let cursor = Cursor::new(data);
        let decoder = GzDecoder::new(cursor);
        let mut archive = Archive::new(decoder);

        // Use blocking task for CPU-intensive extraction
        let extract_path = extract_path.to_path_buf();
        tokio::task::spawn_blocking(move || {
            for entry in archive.entries()? {
                let mut entry = entry?;
                let path = entry.path()?;
                
                // Skip the top-level package directory
                let path = if let Ok(stripped) = path.strip_prefix("package") {
                    extract_path.join(stripped)
                } else {
                    extract_path.join(path)
                };

                entry.unpack(&path)?;
            }
            Ok::<(), anyhow::Error>(())
        }).await??;

        Ok(())
    }

    async fn install_binaries(
        &self,
        bin: &HashMap<String, String>,
        package_path: &Path,
    ) -> Result<()> {
        let bin_dir = self.config.node_modules_path.join(".bin");
        fs::create_dir_all(&bin_dir).await?;

        for (name, script_path) in bin {
            let source = package_path.join(script_path);
            let target = bin_dir.join(name);

            if source.exists() {
                // Create symlink or copy based on platform
                #[cfg(unix)]
                {
                    use std::os::unix::fs::symlink;
                    let _ = std::fs::remove_file(&target);
                    symlink(&source, &target)?;
                }

                #[cfg(windows)]
                {
                    fs::copy(&source, &target).await?;
                }
            }
        }

        Ok(())
    }

    fn verify_integrity(&self, data: &[u8], integrity: &str) -> Result<()> {
        use blake3::Hasher;
        
        if integrity.starts_with("sha512-") {
            let expected = &integrity[7..];
            let mut hasher = blake3::Hasher::new();
            hasher.update(data);
            let hash = hasher.finalize();
            let actual = base64::encode(hash.as_bytes());
            
            if actual != expected {
                return Err(anyhow!("Integrity check failed"));
            }
        }

        Ok(())
    }

    fn show_dry_run_plan(&self, plan: &InstallPlan, ui: &UI) {
        ui.info(&format!("Dry run: would install {} packages ({} total)", 
                         plan.total_count, format_size(plan.total_size)));
        
        for package in &plan.packages {
            println!("  {} {} ({})", 
                     package.name, 
                     package.version,
                     format_size(package.size.unwrap_or(0)));
        }
    }
}

impl Clone for UltraFastInstaller {
    fn clone(&self) -> Self {
        Self {
            registry: Arc::clone(&self.registry),
            cache: Arc::clone(&self.cache),
            config: self.config.clone(),
        }
    }
}

fn format_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    format!("{:.1}{}", size, UNITS[unit_index])
}
