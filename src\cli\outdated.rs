use anyhow::Result;
use clap::Args;
use std::collections::HashMap;
use std::path::Path;
use tracing::{info, warn};

use crate::package::PackageJson;
use crate::registry::Registry;
use crate::ui::UI;

/// Show outdated packages
#[derive(Debug, Args)]
pub struct OutdatedArgs {
    /// Check global packages
    #[arg(short, long)]
    pub global: bool,

    /// Output format (table, json)
    #[arg(long, default_value = "table")]
    pub format: String,

    /// Show all packages, not just outdated ones
    #[arg(long)]
    pub all: bool,
}

#[derive(Debug, serde::Serialize)]
struct PackageStatus {
    name: String,
    current: String,
    wanted: String,
    latest: String,
    location: String,
}

pub async fn outdated(args: OutdatedArgs) -> Result<()> {
    let ui = UI::new();
    let registry = Registry::new();
    
    if args.global {
        ui.warning("Global package checking not yet implemented");
        return Ok(());
    }
    
    // Check if package.json exists
    if !Path::new("package.json").exists() {
        ui.error("No package.json found in current directory");
        return Ok(());
    }
    
    ui.info("🔍 Checking for outdated packages...");
    
    let spinner = ui.create_spinner("Analyzing dependencies");
    
    // Read package.json
    let package_json = PackageJson::read("package.json").await?;
    
    let mut all_deps = HashMap::new();
    
    // Collect all dependencies
    for (name, spec) in &package_json.dependencies {
        all_deps.insert(name.clone(), (spec.clone(), "dependencies".to_string()));
    }
    
    for (name, spec) in &package_json.dev_dependencies {
        all_deps.insert(name.clone(), (spec.clone(), "devDependencies".to_string()));
    }
    
    if all_deps.is_empty() {
        spinner.finish_with_message("✓ No dependencies found");
        ui.info("No dependencies found in package.json");
        return Ok(());
    }
    
    let mut package_statuses = Vec::new();
    
    // Check each dependency
    for (name, (spec, location)) in all_deps {
        match registry.get_package_metadata(&name).await {
            Ok(metadata) => {
                let latest = metadata.latest_version.clone();
                
                // Parse the current spec to determine wanted version
                let wanted = if spec.starts_with('^') || spec.starts_with('~') {
                    // For semver ranges, the wanted version is the latest that satisfies the range
                    // For now, we'll use the spec as-is (this could be improved with proper semver resolution)
                    spec.clone()
                } else {
                    spec.clone()
                };
                
                // Check if package is installed
                let node_modules_path = format!("node_modules/{}", name);
                let current = if Path::new(&node_modules_path).exists() {
                    // Try to read the installed version from package.json
                    let installed_package_path = format!("{}/package.json", node_modules_path);
                    if let Ok(installed_package) = PackageJson::read(&installed_package_path).await {
                        installed_package.version
                    } else {
                        "unknown".to_string()
                    }
                } else {
                    "MISSING".to_string()
                };
                
                let status = PackageStatus {
                    name: name.clone(),
                    current,
                    wanted,
                    latest,
                    location,
                };
                
                // Only include if outdated or if --all flag is used
                if args.all || is_outdated(&status) {
                    package_statuses.push(status);
                }
            }
            Err(e) => {
                warn!("Failed to get metadata for {}: {}", name, e);
            }
        }
    }
    
    spinner.finish_with_message("✓ Dependency analysis completed");
    
    if package_statuses.is_empty() {
        ui.success("All packages are up to date! 🎉");
        return Ok(());
    }
    
    if args.format == "json" {
        let json_output = serde_json::to_string_pretty(&package_statuses)?;
        println!("{}", json_output);
        return Ok(());
    }
    
    // Table format
    println!();
    println!("📦 Package Status");
    println!("═══════════════════════════════════════════════════════════════");
    println!("{:<25} {:<12} {:<12} {:<12} {:<15}", "Package", "Current", "Wanted", "Latest", "Location");
    println!("───────────────────────────────────────────────────────────────");
    
    let mut outdated_count = 0;
    
    for status in &package_statuses {
        let current_color = if status.current == "MISSING" {
            "🔴"
        } else if is_outdated(status) {
            "🟡"
            
        } else {
            "🟢"
        };
        
        if is_outdated(status) {
            outdated_count += 1;
        }
        
        println!(
            "{} {:<24} {:<12} {:<12} {:<12} {:<15}",
            current_color,
            status.name,
            status.current,
            status.wanted,
            status.latest,
            status.location
        );
    }
    
    println!();
    
    if outdated_count > 0 {
        ui.warning(&format!("{} package(s) are outdated", outdated_count));
        ui.info("Run 'nx update' to update packages to their latest versions");
    } else {
        ui.success("All packages are up to date!");
    }
    
    Ok(())
}

fn is_outdated(status: &PackageStatus) -> bool {
    if status.current == "MISSING" {
        return true;
    }
    
    // Simple version comparison (could be improved with proper semver)
    status.current != status.latest && status.current != "unknown"
}
