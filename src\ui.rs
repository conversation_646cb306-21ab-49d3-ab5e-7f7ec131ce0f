use colored::*;
use console::{style, Term};
use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use std::sync::Arc;
use std::time::Duration;

pub struct UI {
    pub multi_progress: Arc<MultiProgress>,
    pub term: Term,
    pub no_color: bool,
    pub no_emoji: bool,
}

impl UI {
    pub fn new() -> Self {
        Self {
            multi_progress: Arc::new(MultiProgress::new()),
            term: Term::stdout(),
            no_color: std::env::var("NO_COLOR").is_ok(),
            no_emoji: std::env::var("NO_EMOJI").is_ok(),
        }
    }

    pub fn show_banner(&self) {
        if !self.no_emoji {
            println!("{}", "🚀 nx - Ultra-fast package manager".bright_cyan().bold());
        } else {
            println!("{}", "nx - Ultra-fast package manager".bright_cyan().bold());
        }
    }

    pub fn success(&self, message: &str) {
        let emoji = if self.no_emoji { "✓" } else { "✅" };
        println!("{} {}", emoji.green(), message);
    }

    pub fn error(&self, message: &str) {
        let emoji = if self.no_emoji { "✗" } else { "❌" };
        eprintln!("{} {}", emoji.red(), message.red());
    }

    pub fn warning(&self, message: &str) {
        let emoji = if self.no_emoji { "!" } else { "⚠️" };
        println!("{} {}", emoji.yellow(), message.yellow());
    }

    pub fn info(&self, message: &str) {
        let emoji = if self.no_emoji { "i" } else { "ℹ️" };
        println!("{} {}", emoji.blue(), message);
    }

    pub fn step(&self, message: &str) {
        let emoji = if self.no_emoji { "→" } else { "🔍" };
        println!("{} {}", emoji.cyan(), message);
    }

    pub fn final_success(&self, duration: Duration) {
        let emoji = if self.no_emoji { "✓" } else { "✅" };
        println!(
            "\n{} {} {}",
            emoji.green(),
            "Done in".green(),
            format!("{:.2}s!", duration.as_secs_f64()).bright_green().bold()
        );
    }

    // Ultra-fast spinner styles
    pub fn create_resolution_spinner(&self, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new_spinner());
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.blue} {msg}")
                .unwrap(),
        );
        pb.set_message(format!("🔍 {}", message));
        pb.enable_steady_tick(Duration::from_millis(100));
        pb
    }

    pub fn create_download_spinner(&self, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new_spinner());
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⣾", "⣽", "⣻", "⢿", "⡿", "⣟", "⣯", "⣷"])
                .template("{spinner:.cyan} {msg}")
                .unwrap(),
        );
        pb.set_message(format!("📥 {}", message));
        pb.enable_steady_tick(Duration::from_millis(80));
        pb
    }

    pub fn create_install_spinner(&self, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new_spinner());
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["▉", "▊", "▋", "▌", "▍", "▎", "▏", " ", "▏", "▎", "▍", "▌", "▋", "▊"])
                .template("{spinner:.green} {msg}")
                .unwrap(),
        );
        pb.set_message(format!("📦 {}", message));
        pb.enable_steady_tick(Duration::from_millis(120));
        pb
    }

    // Ultra-fast progress bars with gradients
    pub fn create_download_progress(&self, total_bytes: u64, package: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total_bytes));
        pb.set_style(
            ProgressStyle::default_bar()
                .template("📥 {msg} [{bar:40.cyan/blue}] {percent}% | {bytes}/{total_bytes} | {bytes_per_sec} | ETA {eta}")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  "),
        );
        pb.set_message(package.to_string());
        pb
    }

    pub fn create_install_progress(&self, total_packages: u64) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total_packages));
        pb.set_style(
            ProgressStyle::default_bar()
                .template("📦 Installing [{bar:40.green/bright_green}] {pos}/{len} packages | {elapsed_precise}")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  "),
        );
        pb
    }

    // Beautiful summary table
    pub fn show_install_summary(&self, packages: &[InstallSummary]) {
        if packages.is_empty() {
            return;
        }

        println!("\n┌─────────────────────────────────────────────────────────────┐");
        println!("│ {} │", "Installation Summary".bright_cyan().bold());
        println!("├───────────────────┬─────────────┬─────────┬─────────────────┤");
        println!("│ {} │ {} │ {} │ {} │", 
                 "Package".bright_white().bold(),
                 "Version".bright_white().bold(),
                 "Size".bright_white().bold(),
                 "Time".bright_white().bold());
        println!("├───────────────────┼─────────────┼─────────┼─────────────────┤");

        for summary in packages {
            println!("│ {:<17} │ {:<11} │ {:<7} │ {:<15} │",
                     summary.name.bright_green(),
                     summary.version.bright_blue(),
                     summary.size.bright_yellow(),
                     summary.time.bright_magenta());
        }

        println!("└───────────────────┴─────────────┴─────────┴─────────────────┘");
    }

    // Package info display
    pub fn show_package_info(&self, info: &PackageInfo) {
        println!("\n┌─────────────────────────────────────────────────────────────┐");
        println!("│ {} │", info.name.bright_cyan().bold());
        println!("├─────────────────────┬───────────────────────────────────────┤");
        println!("│ {} │ {:<37} │", "Version".bright_white().bold(), info.version.bright_green());
        println!("│ {} │ {:<37} │", "Dependencies".bright_white().bold(), info.dependencies.to_string().bright_blue());
        println!("│ {} │ {:<37} │", "Size".bright_white().bold(), info.size.bright_yellow());
        println!("│ {} │ {:<37} │", "License".bright_white().bold(), info.license.bright_magenta());
        if let Some(description) = &info.description {
            println!("│ {} │ {:<37} │", "Description".bright_white().bold(), description.bright_white());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    // Outdated packages table
    pub fn show_outdated_table(&self, packages: &[OutdatedPackage]) {
        if packages.is_empty() {
            self.success("All packages are up to date!");
            return;
        }

        println!("\n┌─────────────────────────────────────────────────────────────┐");
        println!("│ {} │", "Outdated Packages".bright_yellow().bold());
        println!("├─────────────────────┬─────────────┬─────────────────────────┤");
        println!("│ {} │ {} │ {} │", 
                 "Package".bright_white().bold(),
                 "Current".bright_white().bold(),
                 "Latest".bright_white().bold());
        println!("├─────────────────────┼─────────────┼─────────────────────────┤");

        for pkg in packages {
            println!("│ {:<19} │ {:<11} │ {:<23} │",
                     pkg.name.bright_green(),
                     pkg.current.bright_red(),
                     pkg.latest.bright_green());
        }

        println!("└─────────────────────┴─────────────┴─────────────────────────┘");
    }

    pub fn clear_line(&self) {
        print!("\r\x1b[K");
    }
}

#[derive(Debug)]
pub struct InstallSummary {
    pub name: String,
    pub version: String,
    pub size: String,
    pub time: String,
}

#[derive(Debug)]
pub struct PackageInfo {
    pub name: String,
    pub version: String,
    pub dependencies: usize,
    pub size: String,
    pub license: String,
    pub description: Option<String>,
}

#[derive(Debug)]
pub struct OutdatedPackage {
    pub name: String,
    pub current: String,
    pub latest: String,
}

impl Default for UI {
    fn default() -> Self {
        Self::new()
    }
}
