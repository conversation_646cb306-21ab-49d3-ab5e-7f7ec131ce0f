{"name": "test-project", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"Add build script here\"", "start": "node index.js", "dev": "node index.js"}, "dependencies": {"express": "^1.0.0", "lodash": "^1.0.0", "ejs": "^1.0.0", "puppeteer": "^1.0.0"}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "keywords": [], "author": "", "license": "ISC", "homepage": null, "repository": null, "bugs": null, "bin": null, "engines": {}, "os": [], "cpu": [], "private": false, "workspaces": null}