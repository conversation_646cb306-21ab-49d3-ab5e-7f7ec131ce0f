{"name": "test-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "echo \"Add build script here\"", "start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1", "dev": "node index.js"}, "dependencies": {"lodash": "^1.0.0"}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "keywords": [], "author": "", "license": "ISC", "homepage": null, "repository": null, "bugs": null, "bin": null, "engines": {}, "os": [], "cpu": [], "private": false, "workspaces": null}