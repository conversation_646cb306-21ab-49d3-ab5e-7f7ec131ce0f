use anyhow::{anyhow, Result};
use std::collections::HashMap;
use std::path::PathBuf;
use std::process::Stdio;
use tokio::process::Command;
use tracing::{info, warn};

use crate::cli::{ExecArgs, RunArgs};
use crate::ui::UI;

/// Execute nx run command
pub async fn execute(args: RunArgs, ui: &UI) -> Result<()> {
    if args.list {
        return list_scripts(ui).await;
    }

    ui.step(&format!("🚀 Running script: {}", args.script));

    // Read package.json to get scripts
    let scripts = read_package_scripts().await?;
    
    let script_command = scripts.get(&args.script)
        .ok_or_else(|| anyhow!("Script '{}' not found in package.json", args.script))?;

    // Execute the script
    execute_script(script_command, &args.args, ui).await
}

/// Execute nx exec command
pub async fn execute_exec(args: ExecArgs, ui: &UI) -> Result<()> {
    ui.step(&format!("🔧 Executing binary: {}", args.binary));

    // Find the binary in node_modules/.bin
    let bin_path = find_binary(&args.binary).await?;
    
    // Execute the binary
    execute_binary(&bin_path, &args.args, ui).await
}

async fn list_scripts(ui: &UI) -> Result<()> {
    let scripts = read_package_scripts().await?;

    if scripts.is_empty() {
        ui.info("No scripts found in package.json");
        return Ok(());
    }

    use colored::*;

    println!("\n📜 {}", "Available Scripts".bright_cyan().bold());
    println!("┌─────────────────────┬───────────────────────────────────────┐");
    println!("│ {} │ {} │", 
             "Script".bright_white().bold(),
             "Command".bright_white().bold());
    println!("├─────────────────────┼───────────────────────────────────────┤");

    let mut script_names: Vec<_> = scripts.keys().collect();
    script_names.sort();

    for name in script_names {
        let command = scripts.get(name).unwrap();
        let truncated_command = if command.len() > 37 {
            format!("{}...", &command[..34])
        } else {
            command.clone()
        };

        println!("│ {:<19} │ {:<37} │",
                 name.bright_green(),
                 truncated_command.bright_blue());
    }

    println!("└─────────────────────┴───────────────────────────────────────┘");

    ui.info(&format!("\n💡 Run scripts with: nx run <script-name>"));

    Ok(())
}

async fn read_package_scripts() -> Result<HashMap<String, String>> {
    let package_json_path = PathBuf::from("package.json");
    if !package_json_path.exists() {
        return Err(anyhow!("package.json not found"));
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;

    let scripts = package["scripts"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok(scripts)
}

async fn execute_script(script_command: &str, args: &[String], ui: &UI) -> Result<()> {
    info!("Executing script: {}", script_command);

    // Prepare the full command with arguments
    let full_command = if args.is_empty() {
        script_command.to_string()
    } else {
        format!("{} {}", script_command, args.join(" "))
    };

    // Determine shell based on platform
    let (shell, shell_arg) = if cfg!(windows) {
        ("cmd", "/C")
    } else {
        ("sh", "-c")
    };

    // Execute the command
    let mut child = Command::new(shell)
        .arg(shell_arg)
        .arg(&full_command)
        .current_dir(".")
        .stdin(Stdio::inherit())
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit())
        .spawn()?;

    let status = child.wait().await?;

    if status.success() {
        ui.success(&format!("✅ Script '{}' completed successfully", script_command));
    } else {
        let exit_code = status.code().unwrap_or(-1);
        return Err(anyhow!("Script failed with exit code: {}", exit_code));
    }

    Ok(())
}

async fn find_binary(binary_name: &str) -> Result<PathBuf> {
    // Check local node_modules/.bin first
    let local_bin = PathBuf::from("node_modules").join(".bin").join(binary_name);
    
    // Add .cmd extension on Windows
    let local_bin_with_ext = if cfg!(windows) {
        local_bin.with_extension("cmd")
    } else {
        local_bin.clone()
    };

    if local_bin_with_ext.exists() {
        return Ok(local_bin_with_ext);
    }

    if local_bin.exists() {
        return Ok(local_bin);
    }

    // Check global installation
    let global_bin = dirs::home_dir()
        .unwrap_or_default()
        .join(".nx")
        .join("global")
        .join("node_modules")
        .join(".bin")
        .join(binary_name);

    let global_bin_with_ext = if cfg!(windows) {
        global_bin.with_extension("cmd")
    } else {
        global_bin.clone()
    };

    if global_bin_with_ext.exists() {
        return Ok(global_bin_with_ext);
    }

    if global_bin.exists() {
        return Ok(global_bin);
    }

    // Check if it's a system binary
    if let Ok(output) = Command::new("which")
        .arg(binary_name)
        .output()
        .await
    {
        if output.status.success() {
            let path_str = String::from_utf8_lossy(&output.stdout);
            let path_str = path_str.trim();
            if !path_str.is_empty() {
                return Ok(PathBuf::from(path_str));
            }
        }
    }

    // On Windows, try 'where' command
    if cfg!(windows) {
        if let Ok(output) = Command::new("where")
            .arg(binary_name)
            .output()
            .await
        {
            if output.status.success() {
                let path_str = String::from_utf8_lossy(&output.stdout);
                let path_str = path_str.trim();
                if !path_str.is_empty() {
                    return Ok(PathBuf::from(path_str.lines().next().unwrap_or("")));
                }
            }
        }
    }

    Err(anyhow!("Binary '{}' not found", binary_name))
}

async fn execute_binary(binary_path: &PathBuf, args: &[String], ui: &UI) -> Result<()> {
    info!("Executing binary: {} with args: {:?}", binary_path.display(), args);

    let mut command = Command::new(binary_path);
    command
        .args(args)
        .current_dir(".")
        .stdin(Stdio::inherit())
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit());

    let mut child = command.spawn()?;
    let status = child.wait().await?;

    if status.success() {
        ui.success(&format!("✅ Binary '{}' completed successfully", binary_path.display()));
    } else {
        let exit_code = status.code().unwrap_or(-1);
        return Err(anyhow!("Binary failed with exit code: {}", exit_code));
    }

    Ok(())
}

/// Get available binaries from installed packages
pub async fn get_available_binaries() -> Result<Vec<String>> {
    let mut binaries = Vec::new();

    // Check local .bin directory
    let local_bin_dir = PathBuf::from("node_modules").join(".bin");
    if local_bin_dir.exists() {
        let mut entries = tokio::fs::read_dir(&local_bin_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let name = entry.file_name().to_string_lossy().to_string();
            // Skip Windows .cmd files in the list (show base name only)
            if cfg!(windows) && name.ends_with(".cmd") {
                binaries.push(name.trim_end_matches(".cmd").to_string());
            } else if !name.ends_with(".cmd") {
                binaries.push(name);
            }
        }
    }

    // Check global .bin directory
    let global_bin_dir = dirs::home_dir()
        .unwrap_or_default()
        .join(".nx")
        .join("global")
        .join("node_modules")
        .join(".bin");

    if global_bin_dir.exists() {
        let mut entries = tokio::fs::read_dir(&global_bin_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let name = entry.file_name().to_string_lossy().to_string();
            // Skip Windows .cmd files and avoid duplicates
            if cfg!(windows) && name.ends_with(".cmd") {
                let base_name = name.trim_end_matches(".cmd").to_string();
                if !binaries.contains(&base_name) {
                    binaries.push(base_name);
                }
            } else if !name.ends_with(".cmd") && !binaries.contains(&name) {
                binaries.push(name);
            }
        }
    }

    binaries.sort();
    Ok(binaries)
}

/// Check if Node.js is available
pub async fn check_node_available() -> bool {
    Command::new("node")
        .arg("--version")
        .output()
        .await
        .map(|output| output.status.success())
        .unwrap_or(false)
}

/// Get Node.js version
pub async fn get_node_version() -> Result<String> {
    let output = Command::new("node")
        .arg("--version")
        .output()
        .await?;

    if output.status.success() {
        let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
        Ok(version)
    } else {
        Err(anyhow!("Failed to get Node.js version"))
    }
}

/// Check if npm is available
pub async fn check_npm_available() -> bool {
    Command::new("npm")
        .arg("--version")
        .output()
        .await
        .map(|output| output.status.success())
        .unwrap_or(false)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_check_node_available() {
        // This test will pass if Node.js is installed
        let available = check_node_available().await;
        println!("Node.js available: {}", available);
    }

    #[tokio::test]
    async fn test_get_available_binaries() {
        let binaries = get_available_binaries().await.unwrap_or_default();
        println!("Available binaries: {:?}", binaries);
    }
}
