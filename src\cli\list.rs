use anyhow::Result;
use std::path::Path;
// Clean output - no UI needed

/// List installed packages (equivalent to npm list)
pub async fn execute(global: bool, depth: Option<usize>) -> Result<()> {
    if global {
        eprintln!("npm ERR! Global package listing not yet implemented");
        return Ok(());
    }
    
    // Read package.json to get project info
    if let Ok(package_json) = std::fs::read_to_string("package.json") {
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&package_json) {
            if let Some(name) = parsed.get("name").and_then(|n| n.as_str()) {
                if let Some(version) = parsed.get("version").and_then(|v| v.as_str()) {
                    println!("{}@{}", name, version);
                }
            }
        }
    }
    
    // List packages in node_modules
    let node_modules = Path::new("node_modules");
    if node_modules.exists() {
        list_packages_in_directory(node_modules, depth.unwrap_or(0), 0)?;
    } else {
        println!("(empty)");
    }
    
    Ok(())
}

fn list_packages_in_directory(
    dir: &Path,
    max_depth: usize,
    current_depth: usize
) -> Result<()> {
    if max_depth > 0 && current_depth >= max_depth {
        return Ok(());
    }
    
    if let Ok(entries) = std::fs::read_dir(dir) {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                let name = entry.file_name();
                
                if let Some(name_str) = name.to_str() {
                    // Skip hidden directories and .bin
                    if name_str.starts_with('.') {
                        continue;
                    }
                    
                    if path.is_dir() {
                        // Check if it's a scoped package directory
                        if name_str.starts_with('@') {
                            // List packages in the scoped directory
                            if let Ok(scoped_entries) = std::fs::read_dir(&path) {
                                for scoped_entry in scoped_entries {
                                    if let Ok(scoped_entry) = scoped_entry {
                                        let scoped_path = scoped_entry.path();
                                        let scoped_name = scoped_entry.file_name();
                                        
                                        if let Some(scoped_name_str) = scoped_name.to_str() {
                                            if scoped_path.is_dir() {
                                                let full_name = format!("{}/{}", name_str, scoped_name_str);
                                                let version = get_package_version(&scoped_path);
                                                print_package(&full_name, &version, current_depth);
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            // Regular package
                            let version = get_package_version(&path);
                            print_package(name_str, &version, current_depth);
                        }
                    }
                }
            }
        }
    }
    
    Ok(())
}

fn get_package_version(package_dir: &Path) -> String {
    let package_json_path = package_dir.join("package.json");
    if let Ok(content) = std::fs::read_to_string(package_json_path) {
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&content) {
            if let Some(version) = parsed.get("version").and_then(|v| v.as_str()) {
                return version.to_string();
            }
        }
    }
    "unknown".to_string()
}

fn print_package(name: &str, version: &str, depth: usize) {
    let indent = "  ".repeat(depth + 1);
    println!("{}├── {}@{}", indent, name, version);
}
