use anyhow::{Result, Context};
use sha2::{Sha256, Sha512, Digest};
use base64::{Engine as _, engine::general_purpose};

/// Hash utilities for integrity verification
pub struct HashUtils;

impl HashUtils {
    /// Calculate SHA-256 hash of data
    pub fn sha256(data: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(data);
        let result = hasher.finalize();
        hex::encode(result)
    }
    
    /// Calculate SHA-512 hash of data
    pub fn sha512(data: &[u8]) -> String {
        let mut hasher = Sha512::new();
        hasher.update(data);
        let result = hasher.finalize();
        hex::encode(result)
    }
    
    /// Calculate SHA-256 hash and return as base64
    pub fn sha256_base64(data: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(data);
        let result = hasher.finalize();
        general_purpose::STANDARD.encode(result)
    }
    
    /// Calculate SHA-512 hash and return as base64
    pub fn sha512_base64(data: &[u8]) -> String {
        let mut hasher = Sha512::new();
        hasher.update(data);
        let result = hasher.finalize();
        general_purpose::STANDARD.encode(result)
    }
    
    /// Verify integrity using npm-style integrity string
    pub fn verify_integrity(data: &[u8], integrity: &str) -> Result<bool> {
        // Parse integrity string (e.g., "sha512-abc123==")
        if let Some((algorithm, expected_hash)) = integrity.split_once('-') {
            let actual_hash = match algorithm {
                "sha256" => Self::sha256_base64(data),
                "sha512" => Self::sha512_base64(data),
                _ => return Err(anyhow::anyhow!("Unsupported hash algorithm: {}", algorithm)),
            };
            
            Ok(actual_hash == expected_hash)
        } else {
            Err(anyhow::anyhow!("Invalid integrity format: {}", integrity))
        }
    }
    
    /// Generate npm-style integrity string
    pub fn generate_integrity(data: &[u8], algorithm: &str) -> Result<String> {
        let hash = match algorithm {
            "sha256" => Self::sha256_base64(data),
            "sha512" => Self::sha512_base64(data),
            _ => return Err(anyhow::anyhow!("Unsupported hash algorithm: {}", algorithm)),
        };
        
        Ok(format!("{}-{}", algorithm, hash))
    }
    
    /// Calculate multiple hashes for a file
    pub fn calculate_hashes(data: &[u8]) -> FileHashes {
        FileHashes {
            sha256: Self::sha256(data),
            sha512: Self::sha512(data),
            sha256_base64: Self::sha256_base64(data),
            sha512_base64: Self::sha512_base64(data),
        }
    }
}

/// Collection of hashes for a file
#[derive(Debug, Clone)]
pub struct FileHashes {
    pub sha256: String,
    pub sha512: String,
    pub sha256_base64: String,
    pub sha512_base64: String,
}

impl FileHashes {
    /// Get integrity string for npm
    pub fn npm_integrity(&self) -> String {
        format!("sha512-{}", self.sha512_base64)
    }
    
    /// Verify against another set of hashes
    pub fn verify(&self, other: &FileHashes) -> bool {
        self.sha512 == other.sha512
    }
}
