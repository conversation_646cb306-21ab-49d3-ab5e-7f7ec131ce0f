use anyhow::{Result, Context};
use colored::*;
use std::collections::HashMap;
use tracing::{info, warn};
use crate::ConfigCommands;

pub async fn execute(action: ConfigCommands) -> Result<()> {
    match action {
        ConfigCommands::Get { key } => get_config(&key).await,
        ConfigCommands::Set { key, value } => set_config(&key, &value).await,
        ConfigCommands::List => list_config().await,
        ConfigCommands::Reset => reset_config().await,
    }
}

async fn get_config(key: &str) -> Result<()> {
    let config = load_config().await?;
    
    if let Some(value) = config.get(key) {
        println!("{}", value.cyan());
    } else {
        println!("{} Configuration key '{}' not found", "✗".red().bold(), key);
        return Err(anyhow::anyhow!("Configuration key not found"));
    }
    
    Ok(())
}

async fn set_config(key: &str, value: &str) -> Result<()> {
    let mut config = load_config().await?;
    config.insert(key.to_string(), value.to_string());
    
    save_config(&config).await?;
    
    println!("{} Set {} = {}", "✓".green().bold(), key.cyan(), value.cyan());
    
    Ok(())
}

async fn list_config() -> Result<()> {
    let config = load_config().await?;
    
    if config.is_empty() {
        println!("{} No configuration found", "ℹ".yellow().bold());
        return Ok(());
    }
    
    println!("{} Configuration", "⚙️".bold());
    println!();
    
    for (key, value) in config.iter() {
        println!("  {} = {}", key.cyan(), value);
    }
    
    Ok(())
}

async fn reset_config() -> Result<()> {
    let config = get_default_config();
    save_config(&config).await?;
    
    println!("{} Configuration reset to defaults", "✓".green().bold());
    
    Ok(())
}

async fn load_config() -> Result<HashMap<String, String>> {
    let config_path = get_config_path()?;
    
    if !config_path.exists() {
        return Ok(get_default_config());
    }
    
    let content = std::fs::read_to_string(&config_path)
        .context("Failed to read config file")?;
    
    let config: HashMap<String, String> = toml::from_str(&content)
        .context("Failed to parse config file")?;
    
    Ok(config)
}

async fn save_config(config: &HashMap<String, String>) -> Result<()> {
    let config_path = get_config_path()?;
    
    // Ensure config directory exists
    if let Some(parent) = config_path.parent() {
        std::fs::create_dir_all(parent)
            .context("Failed to create config directory")?;
    }
    
    let content = toml::to_string_pretty(config)
        .context("Failed to serialize config")?;
    
    std::fs::write(&config_path, content)
        .context("Failed to write config file")?;
    
    Ok(())
}

fn get_config_path() -> Result<std::path::PathBuf> {
    let config_dir = dirs::config_dir()
        .or_else(|| dirs::home_dir().map(|h| h.join(".config")))
        .context("Could not determine config directory")?;
    
    Ok(config_dir.join("nx").join("config.toml"))
}

fn get_default_config() -> HashMap<String, String> {
    let mut config = HashMap::new();
    
    config.insert("registry".to_string(), "https://registry.npmjs.org".to_string());
    config.insert("cache_dir".to_string(), "~/.nx/cache".to_string());
    config.insert("parallel_downloads".to_string(), "8".to_string());
    config.insert("timeout".to_string(), "30".to_string());
    config.insert("retry_attempts".to_string(), "3".to_string());
    config.insert("progress".to_string(), "true".to_string());
    config.insert("color".to_string(), "auto".to_string());
    
    config
}
