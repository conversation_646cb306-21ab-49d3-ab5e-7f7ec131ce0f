use anyhow::{anyhow, Result};
use std::collections::HashMap;
use std::path::Path;

pub use crate::types::{Lockfile, LockfileEntry};

impl Lockfile {
    /// Read lockfile from disk
    pub async fn read<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = tokio::fs::read_to_string(path).await?;
        let lockfile: Lockfile = serde_json::from_str(&content)?;
        Ok(lockfile)
    }

    /// Write lockfile to disk
    pub async fn write<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }

    /// Get all package entries as a flat list
    pub fn get_all_packages(&self) -> Vec<(&String, &LockfileEntry)> {
        self.packages.iter().collect()
    }

    /// Get a specific package entry
    pub fn get_package(&self, name: &str, version: &str) -> Option<&LockfileEntry> {
        let key = format!("{}@{}", name, version);
        self.packages.get(&key)
    }

    /// Add or update a package entry
    pub fn set_package(&mut self, name: String, version: String, entry: LockfileEntry) {
        let key = format!("{}@{}", name, version);
        self.packages.insert(key, entry);
    }

    /// Remove a package entry
    pub fn remove_package(&mut self, name: &str, version: &str) -> Option<LockfileEntry> {
        let key = format!("{}@{}", name, version);
        self.packages.remove(&key)
    }

    /// Validate lockfile integrity
    pub fn validate(&self) -> Result<()> {
        // Check lockfile version
        if self.lockfile_version < 1 || self.lockfile_version > 3 {
            return Err(anyhow!("Unsupported lockfile version: {}", self.lockfile_version));
        }

        // Validate package entries
        for (key, entry) in &self.packages {
            if !key.contains('@') {
                return Err(anyhow!("Invalid package key format: {}", key));
            }

            if entry.version.is_empty() {
                return Err(anyhow!("Empty version for package: {}", key));
            }

            if entry.resolved.is_empty() {
                return Err(anyhow!("Empty resolved URL for package: {}", key));
            }
        }

        Ok(())
    }

    /// Get statistics about the lockfile
    pub fn get_stats(&self) -> LockfileStats {
        let total_packages = self.packages.len();
        let mut dev_packages = 0;
        let mut optional_packages = 0;
        let mut peer_packages = 0;

        for entry in self.packages.values() {
            if entry.dev == Some(true) {
                dev_packages += 1;
            }
            if entry.optional == Some(true) {
                optional_packages += 1;
            }
            if entry.peer == Some(true) {
                peer_packages += 1;
            }
        }

        LockfileStats {
            total_packages,
            dev_packages,
            optional_packages,
            peer_packages,
        }
    }
}

#[derive(Debug, Clone)]
pub struct LockfileStats {
    pub total_packages: usize,
    pub dev_packages: usize,
    pub optional_packages: usize,
    pub peer_packages: usize,
}

/// Create a new empty lockfile
pub fn create_empty_lockfile() -> Lockfile {
    Lockfile {
        name: None,
        version: None,
        lockfile_version: 3,
        requires: Some(true),
        packages: HashMap::new(),
        dependencies: None,
    }
}
