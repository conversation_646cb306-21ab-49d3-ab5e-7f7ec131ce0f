use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;

pub use crate::types::{Lockfile, LockfileEntry};

impl Lockfile {
    /// Read lockfile from disk
    pub async fn read<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = tokio::fs::read_to_string(path).await?;
        let lockfile: Lockfile = serde_json::from_str(&content)?;
        Ok(lockfile)
    }

    /// Write lockfile to disk
    pub async fn write<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }

    /// Get all package entries as a flat list
    pub fn get_all_packages(&self) -> Vec<(&String, &LockfileEntry)> {
        self.packages.iter().collect()
    }

    /// Get a specific package entry
    pub fn get_package(&self, name: &str, version: &str) -> Option<&LockfileEntry> {
        let key = format!("{}@{}", name, version);
        self.packages.get(&key)
    }

    /// Add or update a package entry
    pub fn set_package(&mut self, name: String, version: String, entry: LockfileEntry) {
        let key = format!("{}@{}", name, version);
        self.packages.insert(key, entry);
    }

    /// Remove a package entry
    pub fn remove_package(&mut self, name: &str, version: &str) -> Option<LockfileEntry> {
        let key = format!("{}@{}", name, version);
        self.packages.remove(&key)
    }

    /// Validate lockfile integrity
    pub fn validate(&self) -> Result<()> {
        // Check lockfile version
        if self.lockfile_version < 1 || self.lockfile_version > 3 {
            return Err(anyhow!("Unsupported lockfile version: {}", self.lockfile_version));
        }

        // Validate package entries
        for (key, entry) in &self.packages {
            if !key.contains('@') {
                return Err(anyhow!("Invalid package key format: {}", key));
            }

            if entry.version.is_empty() {
                return Err(anyhow!("Empty version for package: {}", key));
            }

            if entry.resolved.is_empty() {
                return Err(anyhow!("Empty resolved URL for package: {}", key));
            }
        }

        Ok(())
    }

    /// Get dependency tree from lockfile
    pub fn get_dependency_tree(&self) -> HashMap<String, Vec<String>> {
        let mut tree = HashMap::new();

        for (key, entry) in &self.packages {
            let mut deps = Vec::new();
            
            if let Some(dependencies) = &entry.dependencies {
                for (name, version) in dependencies {
                    deps.push(format!("{}@{}", name, version));
                }
            }
            
            tree.insert(key.clone(), deps);
        }

        tree
    }

    /// Check if lockfile is compatible with package.json
    pub async fn is_compatible_with_package_json<P: AsRef<Path>>(&self, package_json_path: P) -> Result<bool> {
        let content = tokio::fs::read_to_string(package_json_path).await?;
        let package: serde_json::Value = serde_json::from_str(&content)?;

        // Check if all dependencies in package.json are in lockfile
        if let Some(deps) = package["dependencies"].as_object() {
            for (name, _) in deps {
                let found = self.packages.keys().any(|key| key.starts_with(&format!("{}@", name)));
                if !found {
                    return Ok(false);
                }
            }
        }

        if let Some(dev_deps) = package["devDependencies"].as_object() {
            for (name, _) in dev_deps {
                let found = self.packages.keys().any(|key| key.starts_with(&format!("{}@", name)));
                if !found {
                    return Ok(false);
                }
            }
        }

        Ok(true)
    }

    /// Get statistics about the lockfile
    pub fn get_stats(&self) -> LockfileStats {
        let total_packages = self.packages.len();
        let mut dev_packages = 0;
        let mut optional_packages = 0;
        let mut peer_packages = 0;
        let mut total_size = 0u64;

        for entry in self.packages.values() {
            if entry.dev == Some(true) {
                dev_packages += 1;
            }
            if entry.optional == Some(true) {
                optional_packages += 1;
            }
            if entry.peer == Some(true) {
                peer_packages += 1;
            }
            
            // Estimate size from integrity hash (rough approximation)
            if let Some(integrity) = &entry.integrity {
                if integrity.starts_with("sha512-") {
                    // Base64 encoded SHA512 is roughly 88 characters
                    // Estimate package size based on hash complexity
                    total_size += 1024 * 100; // Rough estimate: 100KB per package
                }
            }
        }

        LockfileStats {
            total_packages,
            dev_packages,
            optional_packages,
            peer_packages,
            estimated_size: total_size,
        }
    }
}

#[derive(Debug, Clone)]
pub struct LockfileStats {
    pub total_packages: usize,
    pub dev_packages: usize,
    pub optional_packages: usize,
    pub peer_packages: usize,
    pub estimated_size: u64,
}

/// Create a new empty lockfile
pub fn create_empty_lockfile() -> Lockfile {
    Lockfile {
        name: None,
        version: None,
        lockfile_version: 3,
        requires: Some(true),
        packages: HashMap::new(),
        dependencies: None,
    }
}

/// Merge two lockfiles
pub fn merge_lockfiles(base: &Lockfile, other: &Lockfile) -> Lockfile {
    let mut merged = base.clone();
    
    // Merge packages
    for (key, entry) in &other.packages {
        merged.packages.insert(key.clone(), entry.clone());
    }
    
    // Use the higher lockfile version
    merged.lockfile_version = base.lockfile_version.max(other.lockfile_version);
    
    merged
}

/// Convert npm lockfile to nx format
pub fn convert_from_npm_lockfile(npm_lockfile: &serde_json::Value) -> Result<Lockfile> {
    let mut packages = HashMap::new();
    
    if let Some(deps) = npm_lockfile["dependencies"].as_object() {
        for (name, dep_info) in deps {
            if let Some(version) = dep_info["version"].as_str() {
                let entry = LockfileEntry {
                    version: version.to_string(),
                    resolved: dep_info["resolved"].as_str().unwrap_or("").to_string(),
                    integrity: dep_info["integrity"].as_str().map(|s| s.to_string()),
                    dependencies: dep_info["requires"].as_object().map(|obj| {
                        obj.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    }),
                    dev: dep_info["dev"].as_bool(),
                    optional: dep_info["optional"].as_bool(),
                    peer: None,
                };
                
                packages.insert(format!("{}@{}", name, version), entry);
            }
        }
    }
    
    Ok(Lockfile {
        name: npm_lockfile["name"].as_str().map(|s| s.to_string()),
        version: npm_lockfile["version"].as_str().map(|s| s.to_string()),
        lockfile_version: npm_lockfile["lockfileVersion"].as_u64().unwrap_or(1) as u32,
        requires: npm_lockfile["requires"].as_bool(),
        packages,
        dependencies: None,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_empty_lockfile() {
        let lockfile = create_empty_lockfile();
        assert_eq!(lockfile.lockfile_version, 3);
        assert!(lockfile.packages.is_empty());
        assert_eq!(lockfile.requires, Some(true));
    }

    #[test]
    fn test_lockfile_validation() {
        let mut lockfile = create_empty_lockfile();
        
        // Valid lockfile should pass
        assert!(lockfile.validate().is_ok());
        
        // Invalid package key should fail
        lockfile.packages.insert("invalid-key".to_string(), LockfileEntry {
            version: "1.0.0".to_string(),
            resolved: "https://example.com".to_string(),
            integrity: None,
            dependencies: None,
            dev: None,
            optional: None,
            peer: None,
        });
        
        assert!(lockfile.validate().is_err());
    }
}
