use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// Represents a resolved dependency graph
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyGraph {
    pub nodes: HashMap<String, DependencyNode>,
    pub edges: Vec<DependencyEdge>,
}

impl DependencyGraph {
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            edges: Vec::new(),
        }
    }
    
    pub fn add_dependency(&mut self, name: String, version: String) {
        let node = DependencyNode {
            name: name.clone(),
            version,
            dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            optional_dependencies: HashMap::new(),
        };

        self.nodes.insert(name, node);
    }

    pub fn add_node(&mut self, node: DependencyNode) {
        self.nodes.insert(node.name.clone(), node);
    }
    
    pub fn add_edge(&mut self, from: String, to: String, edge_type: DependencyType) {
        self.edges.push(DependencyEdge {
            from,
            to,
            edge_type,
        });
    }
    
    /// Get all packages that need to be installed
    pub fn get_install_order(&self) -> Vec<&DependencyNode> {
        // TODO: Implement topological sort for proper install order
        self.nodes.values().collect()
    }
    
    /// Check for circular dependencies
    pub fn has_cycles(&self) -> bool {
        // TODO: Implement cycle detection
        false
    }
}

/// A node in the dependency graph representing a package
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyNode {
    pub name: String,
    pub version: String,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
}

/// A resolved package with all metadata needed for installation
#[derive(Debug, Clone)]
pub struct ResolvedPackage {
    pub name: String,
    pub version: String,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub tarball_url: String,
    pub integrity: Option<String>,
}

/// An edge in the dependency graph
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyEdge {
    pub from: String,
    pub to: String,
    pub edge_type: DependencyType,
}

/// Type of dependency relationship
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum DependencyType {
    Production,
    Development,
    Peer,
    Optional,
}

/// Resolution context for dependency resolution
#[derive(Debug, Clone)]
pub struct ResolutionContext {
    pub root_dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub overrides: HashMap<String, String>,
    pub resolutions: HashMap<String, String>,
}

impl ResolutionContext {
    pub fn new() -> Self {
        Self {
            root_dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            overrides: HashMap::new(),
            resolutions: HashMap::new(),
        }
    }
    
    pub fn with_dependencies(mut self, deps: HashMap<String, String>) -> Self {
        self.root_dependencies = deps;
        self
    }
    
    pub fn with_dev_dependencies(mut self, deps: HashMap<String, String>) -> Self {
        self.dev_dependencies = deps;
        self
    }
}

/// Conflict resolution strategy
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ConflictResolution {
    /// Use the highest compatible version
    Highest,
    /// Use the lowest compatible version
    Lowest,
    /// Use the version specified in resolutions
    Override(String),
    /// Fail if there's a conflict
    Strict,
}
