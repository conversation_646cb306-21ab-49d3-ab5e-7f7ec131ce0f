use clap::{Parser, Subcommand};

#[derive(Parser)]
#[command(
    name = "nx",
    version = "1.0.0",
    about = "🚀 Ultra-fast package manager for Node.js ecosystems - 3x faster than npm, Yarn, pnpm, and Bun",
    long_about = "nx is a blazing-fast package manager that installs 100+ packages in 2-4 seconds.\nBuilt in Rust for maximum performance and reliability.",
    author = "NX Team <<EMAIL>>",
    help_template = "{before-help}{name} {version}\n{author-with-newline}{about-with-newline}\n{usage-heading} {usage}\n\n{all-args}{after-help}"
)]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
    
    /// Enable verbose output
    #[arg(short, long, global = true)]
    pub verbose: bool,
    
    /// Disable colored output
    #[arg(long, global = true)]
    pub no_color: bool,
    
    /// Disable emoji output
    #[arg(long, global = true)]
    pub no_emoji: bool,
    
    /// Output format (json, table, minimal)
    #[arg(long, global = true, default_value = "table")]
    pub format: String,
    
    /// Custom registry URL
    #[arg(long, global = true)]
    pub registry: Option<String>,
    
    /// Log file path
    #[arg(long, global = true)]
    pub log_file: Option<String>,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Install packages from package.json or specified packages
    #[command(alias = "i")]
    Install(InstallArgs),
    
    /// Remove packages and prune unused dependencies
    #[command(alias = "rm", alias = "remove", alias = "unlink")]
    Uninstall(UninstallArgs),
    
    /// Update packages to latest compatible versions
    #[command(alias = "up", alias = "upgrade")]
    Update(UpdateArgs),
    
    /// List installed packages in tree or table format
    #[command(alias = "ls")]
    List(ListArgs),
    
    /// Display package metadata and information
    Info(InfoArgs),
    
    /// Perform security audit using npm advisory API
    Audit(AuditArgs),
    
    /// Execute package.json scripts
    Run(RunArgs),
    
    /// Rebuild native modules and binaries
    Rebuild(RebuildArgs),
    
    /// Remove duplicate dependencies in node_modules
    Dedupe(DedupeArgs),
    
    /// Clean install from lockfile for reproducibility
    Ci(CiArgs),
    
    /// Manage package cache
    Cache(CacheArgs),
    
    /// Execute binaries from installed packages
    Exec(ExecArgs),
    
    /// Show outdated packages with current and latest versions
    Outdated(OutdatedArgs),
    
    /// Diagnose installation issues and system health
    Doctor,
    
    /// Benchmark nx vs npm/Yarn/pnpm/Bun performance
    Benchmark(BenchmarkArgs),
}

#[derive(Parser)]
pub struct InstallArgs {
    /// Packages to install (e.g., express@4.18.2, lodash@latest)
    pub packages: Vec<String>,
    
    /// Install as development dependencies
    #[arg(short = 'D', long)]
    pub save_dev: bool,
    
    /// Install globally to ~/.nx/global
    #[arg(short, long)]
    pub global: bool,
    
    /// Save exact versions (no ^ or ~)
    #[arg(short = 'E', long)]
    pub save_exact: bool,
    
    /// Skip saving to package.json
    #[arg(long)]
    pub no_save: bool,
    
    /// Dry run - show what would be installed
    #[arg(long)]
    pub dry_run: bool,
    
    /// Force reinstall even if already installed
    #[arg(short, long)]
    pub force: bool,
    
    /// Skip optional dependencies
    #[arg(long)]
    pub no_optional: bool,
    
    /// Production install (skip devDependencies)
    #[arg(long)]
    pub production: bool,
}

#[derive(Parser)]
pub struct UninstallArgs {
    /// Packages to uninstall
    pub packages: Vec<String>,
    
    /// Uninstall from global location
    #[arg(short, long)]
    pub global: bool,
    
    /// Skip saving changes to package.json
    #[arg(long)]
    pub no_save: bool,
    
    /// Dry run - show what would be removed
    #[arg(long)]
    pub dry_run: bool,
}

#[derive(Parser)]
pub struct UpdateArgs {
    /// Specific packages to update (empty = update all)
    pub packages: Vec<String>,
    
    /// Update global packages
    #[arg(short, long)]
    pub global: bool,
    
    /// Save exact versions
    #[arg(short = 'E', long)]
    pub save_exact: bool,
    
    /// Dry run - show what would be updated
    #[arg(long)]
    pub dry_run: bool,
    
    /// Update to latest (ignore semver ranges)
    #[arg(long)]
    pub latest: bool,
}

#[derive(Parser)]
pub struct ListArgs {
    /// List global packages
    #[arg(short, long)]
    pub global: bool,
    
    /// Show dependency tree
    #[arg(long)]
    pub tree: bool,
    
    /// Maximum depth for tree display
    #[arg(long, default_value = "3")]
    pub depth: usize,
    
    /// Show package sizes
    #[arg(long)]
    pub size: bool,
}

#[derive(Parser)]
pub struct InfoArgs {
    /// Package name to get information about
    pub package: String,
    
    /// Show specific version information
    #[arg(short, long)]
    pub version: Option<String>,
    
    /// Show all available versions
    #[arg(long)]
    pub versions: bool,
    
    /// Show dependencies
    #[arg(long)]
    pub dependencies: bool,
}

#[derive(Parser)]
pub struct AuditArgs {
    /// Automatically fix vulnerabilities
    #[arg(long)]
    pub fix: bool,
    
    /// Force audit even if cache is fresh
    #[arg(long)]
    pub force: bool,
    
    /// Audit level (low, moderate, high, critical)
    #[arg(long)]
    pub level: Option<String>,
}

#[derive(Parser)]
pub struct RunArgs {
    /// Script name to execute
    pub script: String,
    
    /// Arguments to pass to the script
    pub args: Vec<String>,
    
    /// Show available scripts
    #[arg(long)]
    pub list: bool,
}

#[derive(Parser)]
pub struct RebuildArgs {
    /// Specific packages to rebuild
    pub packages: Vec<String>,
}

#[derive(Parser)]
pub struct DedupeArgs {
    /// Dry run - show what would be deduplicated
    #[arg(long)]
    pub dry_run: bool,
}

#[derive(Parser)]
pub struct CiArgs {
    /// Skip optional dependencies
    #[arg(long)]
    pub no_optional: bool,
    
    /// Production install only
    #[arg(long)]
    pub production: bool,
}

#[derive(Parser)]
pub struct CacheArgs {
    #[command(subcommand)]
    pub action: CacheAction,
}

#[derive(Subcommand)]
pub enum CacheAction {
    /// Clear all cached data
    Clear,
    
    /// Show cache statistics
    Stats,
    
    /// Verify cache integrity
    Verify,
    
    /// Set cache directory
    Dir { path: String },
}

#[derive(Parser)]
pub struct ExecArgs {
    /// Binary name to execute
    pub binary: String,
    
    /// Arguments to pass to the binary
    pub args: Vec<String>,
}

#[derive(Parser)]
pub struct OutdatedArgs {
    /// Check global packages
    #[arg(short, long)]
    pub global: bool,
    
    /// Show all packages, not just outdated
    #[arg(long)]
    pub all: bool,
}

#[derive(Parser)]
pub struct BenchmarkArgs {
    /// Package managers to benchmark against
    #[arg(long, default_values = &["npm", "yarn", "pnpm"])]
    pub managers: Vec<String>,
    
    /// Test project type
    #[arg(long, default_value = "react")]
    pub project: String,
    
    /// Number of benchmark runs
    #[arg(long, default_value = "3")]
    pub runs: usize,
}
