use anyhow::{Result, Context};
use std::path::{Path, PathBuf};
use tracing::{debug, info};

use crate::utils::FsUtils;

/// Package linker for creating symlinks and hardlinks
#[derive(Debug, Clone)]
pub struct Linker {
    install_dir: PathBuf,
}

impl Linker {
    pub fn new(install_dir: PathBuf) -> Self {
        Self { install_dir }
    }
    
    /// Link an extracted package to the install directory
    pub async fn link(
        &self,
        extracted_path: &Path,
        name: &str,
        version: &str,
    ) -> Result<()> {
        let target_path = self.get_install_path(name);

        info!("Linking {}@{}", name, version);

        // Remove existing installation if it exists
        if target_path.exists() {
            FsUtils::remove_dir_all(&target_path).await?;
        }

        // Ensure parent directory exists
        if let Some(parent) = target_path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }

        // Copy the extracted package to node_modules (not symlink)
        FsUtils::copy_dir_all(extracted_path, &target_path).await
            .context("Failed to copy package to node_modules")?;

        // Link binaries AFTER copying to final location
        self.link_binaries(&target_path, name).await?;

        debug!("Linked {}@{} to {}", name, version, target_path.display());
        Ok(())
    }
    
    /// Link package binaries to a bin directory
    pub async fn link_binaries(&self, package_path: &Path, name: &str) -> Result<()> {
        let package_json_path = package_path.join("package.json");
        if !package_json_path.exists() {
            return Ok(());
        }
        
        // Read package.json to find binaries
        let package_json_content = tokio::fs::read_to_string(&package_json_path).await?;
        let package_json: serde_json::Value = serde_json::from_str(&package_json_content)?;
        
        let bin_dir = self.install_dir.join(".bin");
        FsUtils::create_dir_all(&bin_dir).await?;
        
        // Handle "bin" field
        if let Some(bin) = package_json.get("bin") {
            match bin {
                serde_json::Value::String(bin_path) => {
                    // Single binary with package name
                    self.link_binary(package_path, &bin_dir, name, bin_path).await?;
                }
                serde_json::Value::Object(bin_map) => {
                    // Multiple binaries
                    for (bin_name, bin_path) in bin_map {
                        if let Some(bin_path_str) = bin_path.as_str() {
                            self.link_binary(package_path, &bin_dir, bin_name, bin_path_str).await?;
                        }
                    }
                }
                _ => {}
            }
        }
        
        Ok(())
    }
    
    /// Link a single binary
    async fn link_binary(
        &self,
        package_path: &Path,
        bin_dir: &Path,
        bin_name: &str,
        bin_path: &str,
    ) -> Result<()> {
        let source_path = package_path.join(bin_path);

        // Handle different binary name formats for Windows
        let target_name = if cfg!(windows) {
            if bin_name.ends_with(".exe") || bin_name.ends_with(".cmd") || bin_name.ends_with(".bat") {
                bin_name.to_string()
            } else {
                format!("{}.cmd", bin_name)
            }
        } else {
            bin_name.to_string()
        };

        let target_path = bin_dir.join(&target_name);

        if source_path.exists() {
            // Remove existing binary link
            if target_path.exists() {
                tokio::fs::remove_file(&target_path).await
                    .context("Failed to remove existing binary")?;
            }

            // On Windows, create a batch file wrapper instead of symlink
            #[cfg(windows)]
            {
                self.create_windows_binary_wrapper(&source_path, &target_path).await?;
            }

            // On Unix, create symlink
            #[cfg(unix)]
            {
                FsUtils::create_symlink(&source_path, &target_path).await
                    .context("Failed to create binary symlink")?;
                FsUtils::make_executable(&target_path).await?;
            }

            debug!("Linked binary {} -> {}", target_name, source_path.display());
        }

        Ok(())
    }

    /// Create a Windows batch file wrapper for a binary
    #[cfg(windows)]
    async fn create_windows_binary_wrapper(&self, source_path: &Path, target_path: &Path) -> Result<()> {
        let source_abs = source_path.canonicalize()
            .context("Failed to get absolute path for binary")?;

        let wrapper_content = format!(
            r#"@echo off
node "{}" %*
"#,
            source_abs.display()
        );

        tokio::fs::write(target_path, wrapper_content).await
            .context("Failed to write binary wrapper")?;

        Ok(())
    }

    /// Get the installation path for a package
    fn get_install_path(&self, name: &str) -> PathBuf {
        if name.starts_with('@') {
            // Scoped package
            let parts: Vec<&str> = name.splitn(2, '/').collect();
            if parts.len() == 2 {
                self.install_dir
                    .join("node_modules")
                    .join(parts[0])
                    .join(parts[1])
            } else {
                self.install_dir.join("node_modules").join(name)
            }
        } else {
            self.install_dir.join("node_modules").join(name)
        }
    }
    
    /// Unlink a package
    pub async fn unlink(&self, name: &str) -> Result<()> {
        let target_path = self.get_install_path(name);
        
        if target_path.exists() {
            FsUtils::remove_dir_all(&target_path).await?;
            debug!("Unlinked package: {}", name);
        }
        
        // Remove binaries
        self.unlink_binaries(name).await?;
        
        Ok(())
    }
    
    /// Unlink package binaries
    async fn unlink_binaries(&self, _name: &str) -> Result<()> {
        // TODO: Implement binary unlinking
        // This would require tracking which binaries belong to which package
        Ok(())
    }
}
