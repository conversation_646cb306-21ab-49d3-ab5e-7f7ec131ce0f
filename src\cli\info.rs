use anyhow::Result;
use clap::Args;
use tracing::info;

use crate::registry::Registry;
use crate::ui::UI;

/// Show detailed information about a package
#[derive(Debug, Args)]
pub struct InfoArgs {
    /// Package name to get information about
    pub package: String,

    /// Show specific version information
    #[arg(short, long)]
    pub version: Option<String>,

    /// Show all versions
    #[arg(long)]
    pub versions: bool,

    /// Show dependencies
    #[arg(long)]
    pub dependencies: bool,

    /// Output format (json, table)
    #[arg(long, default_value = "table")]
    pub format: String,
}

pub async fn info(args: InfoArgs) -> Result<()> {
    let ui = UI::new();
    let registry = Registry::new();
    
    ui.info(&format!("📋 Getting information for '{}'...", args.package));
    
    let spinner = ui.create_spinner("Fetching package metadata");
    
    // Get package metadata
    let metadata = registry.get_package_metadata(&args.package).await?;
    
    spinner.finish_with_message("✓ Package information retrieved");
    
    if args.format == "json" {
        // JSON output
        let json_output = serde_json::to_string_pretty(&metadata)?;
        println!("{}", json_output);
        return Ok(());
    }
    
    // Table format (default)
    println!();
    println!("📦 Package Information");
    println!("═══════════════════════");
    println!();
    
    println!("Name:         {}", metadata.name);
    println!("Description:  {}", metadata.description);
    println!("Latest:       {}", metadata.latest_version);
    println!("Homepage:     {}", metadata.homepage.unwrap_or_else(|| "Not specified".to_string()));
    println!("Repository:   {}", metadata.repository.unwrap_or_else(|| "Not specified".to_string()));
    println!("License:      {}", metadata.license.unwrap_or_else(|| "Not specified".to_string()));
    
    if let Some(author) = &metadata.author {
        println!("Author:       {}", author);
    }
    
    if !metadata.keywords.is_empty() {
        println!("Keywords:     {}", metadata.keywords.join(", "));
    }
    
    println!();
    
    if args.versions {
        println!("📋 Available Versions");
        println!("═══════════════════════");
        println!();
        
        let mut versions: Vec<_> = metadata.versions.keys().collect();
        versions.sort_by(|a, b| {
            // Try to parse as semver for proper sorting
            match (semver::Version::parse(a), semver::Version::parse(b)) {
                (Ok(va), Ok(vb)) => vb.cmp(&va), // Reverse order (newest first)
                _ => b.cmp(a), // Fallback to string comparison
            }
        });
        
        for (i, version) in versions.iter().enumerate() {
            if i >= 20 { // Limit to 20 versions
                println!("... and {} more versions", versions.len() - 20);
                break;
            }
            
            let marker = if *version == &metadata.latest_version {
                " (latest)"
            } else {
                ""
            };
            
            println!("  {}{}", version, marker);
        }
        println!();
    }
    
    if args.dependencies {
        if let Some(version_info) = metadata.versions.get(&metadata.latest_version) {
            if !version_info.dependencies.is_empty() {
                println!("📦 Dependencies ({})", metadata.latest_version);
                println!("═══════════════════════");
                println!();
                
                for (name, spec) in &version_info.dependencies {
                    println!("  {} {}", name, spec);
                }
                println!();
            }
            
            if !version_info.peer_dependencies.is_empty() {
                println!("🔗 Peer Dependencies");
                println!("═══════════════════════");
                println!();
                
                for (name, spec) in &version_info.peer_dependencies {
                    println!("  {} {}", name, spec);
                }
                println!();
            }
            
            if !version_info.optional_dependencies.is_empty() {
                println!("📋 Optional Dependencies");
                println!("═══════════════════════");
                println!();
                
                for (name, spec) in &version_info.optional_dependencies {
                    println!("  {} {}", name, spec);
                }
                println!();
            }
        }
    }
    
    // Show dist-tags
    println!("🏷️  Distribution Tags");
    println!("═══════════════════════");
    println!();
    println!("  latest: {}", metadata.latest_version);
    
    // Show download stats if available
    println!();
    ui.success(&format!("Use 'nx install {}' to install this package", args.package));
    
    Ok(())
}
