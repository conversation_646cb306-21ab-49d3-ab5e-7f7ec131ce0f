use std::time::{Duration, Instant};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_duration: Duration,
    pub metadata_fetch_duration: Duration,
    pub download_duration: Duration,
    pub extraction_duration: Duration,
    pub linking_duration: Duration,
    pub packages_installed: usize,
    pub total_bytes_downloaded: u64,
    pub cache_hits: usize,
    pub cache_misses: usize,
    pub concurrent_downloads: usize,
    pub average_download_speed: f64, // bytes per second
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            total_duration: Duration::from_secs(0),
            metadata_fetch_duration: Duration::from_secs(0),
            download_duration: Duration::from_secs(0),
            extraction_duration: Duration::from_secs(0),
            linking_duration: Duration::from_secs(0),
            packages_installed: 0,
            total_bytes_downloaded: 0,
            cache_hits: 0,
            cache_misses: 0,
            concurrent_downloads: 0,
            average_download_speed: 0.0,
        }
    }

    pub fn packages_per_second(&self) -> f64 {
        if self.total_duration.as_secs_f64() > 0.0 {
            self.packages_installed as f64 / self.total_duration.as_secs_f64()
        } else {
            0.0
        }
    }

    pub fn cache_hit_rate(&self) -> f64 {
        let total = self.cache_hits + self.cache_misses;
        if total > 0 {
            self.cache_hits as f64 / total as f64 * 100.0
        } else {
            0.0
        }
    }

    pub fn format_speed(&self) -> String {
        if self.average_download_speed > 1_000_000.0 {
            format!("{:.2} MB/s", self.average_download_speed / 1_000_000.0)
        } else if self.average_download_speed > 1_000.0 {
            format!("{:.2} KB/s", self.average_download_speed / 1_000.0)
        } else {
            format!("{:.0} B/s", self.average_download_speed)
        }
    }

    pub fn format_total_size(&self) -> String {
        if self.total_bytes_downloaded > 1_000_000 {
            format!("{:.2} MB", self.total_bytes_downloaded as f64 / 1_000_000.0)
        } else if self.total_bytes_downloaded > 1_000 {
            format!("{:.2} KB", self.total_bytes_downloaded as f64 / 1_000.0)
        } else {
            format!("{} B", self.total_bytes_downloaded)
        }
    }
}

#[derive(Debug)]
pub struct PerformanceTracker {
    start_time: Instant,
    phase_times: HashMap<String, Instant>,
    metrics: PerformanceMetrics,
}

impl PerformanceTracker {
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
            phase_times: HashMap::new(),
            metrics: PerformanceMetrics::new(),
        }
    }

    pub fn start_phase(&mut self, phase: &str) {
        self.phase_times.insert(phase.to_string(), Instant::now());
    }

    pub fn end_phase(&mut self, phase: &str) -> Duration {
        if let Some(start_time) = self.phase_times.remove(phase) {
            let duration = start_time.elapsed();
            match phase {
                "metadata_fetch" => self.metrics.metadata_fetch_duration = duration,
                "download" => self.metrics.download_duration = duration,
                "extraction" => self.metrics.extraction_duration = duration,
                "linking" => self.metrics.linking_duration = duration,
                _ => {}
            }
            duration
        } else {
            Duration::from_secs(0)
        }
    }

    pub fn record_download(&mut self, bytes: u64, duration: Duration) {
        self.metrics.total_bytes_downloaded += bytes;
        if duration.as_secs_f64() > 0.0 {
            let speed = bytes as f64 / duration.as_secs_f64();
            // Update average speed (simple moving average)
            if self.metrics.average_download_speed == 0.0 {
                self.metrics.average_download_speed = speed;
            } else {
                self.metrics.average_download_speed = 
                    (self.metrics.average_download_speed + speed) / 2.0;
            }
        }
    }

    pub fn record_cache_hit(&mut self) {
        self.metrics.cache_hits += 1;
    }

    pub fn record_cache_miss(&mut self) {
        self.metrics.cache_misses += 1;
    }

    pub fn set_packages_installed(&mut self, count: usize) {
        self.metrics.packages_installed = count;
    }

    pub fn set_concurrent_downloads(&mut self, count: usize) {
        self.metrics.concurrent_downloads = count;
    }

    pub fn finish(&mut self) -> PerformanceMetrics {
        self.metrics.total_duration = self.start_time.elapsed();
        self.metrics.clone()
    }

    pub fn get_metrics(&self) -> &PerformanceMetrics {
        &self.metrics
    }
}

impl Default for PerformanceTracker {
    fn default() -> Self {
        Self::new()
    }
}
