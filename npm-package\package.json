{"name": "nx-package-manager", "version": "0.1.0", "description": "Ultra-fast npm package manager written in Rust", "main": "index.js", "bin": {"nx": "bin/nx.js"}, "scripts": {"postinstall": "node install.js", "test": "node test.js"}, "keywords": ["package-manager", "npm", "rust", "fast", "performance", "cli"], "author": "Nx Package Manager Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nx-package-manager/nx.git"}, "bugs": {"url": "https://github.com/nx-package-manager/nx/issues"}, "homepage": "https://github.com/nx-package-manager/nx#readme", "engines": {"node": ">=16.0.0"}, "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"], "files": ["bin/", "install.js", "index.js", "README.md"]}