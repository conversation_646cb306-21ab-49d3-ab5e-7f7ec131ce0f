{"name": "test-real-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "node index.js", "start": "node index.js", "build": "echo \"Add build script here\"", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"lodash": "^1.0.0", "cors": "latest", "ejs": "latest"}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "keywords": [], "author": "", "license": "ISC", "homepage": null, "repository": null, "bugs": null, "bin": null, "engines": {}, "os": [], "cpu": [], "private": false, "workspaces": null}