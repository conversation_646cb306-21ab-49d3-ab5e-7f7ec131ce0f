{"name": "test-real-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "build": "echo \"Add build script here\"", "test": "echo \"Error: no test specified\" && exit 1", "dev": "node index.js"}, "dependencies": {"https": "latest", "cors": "latest", "lodash": "^1.0.0", "ejs": "latest"}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "keywords": [], "author": "", "license": "ISC", "homepage": null, "repository": null, "bugs": null, "bin": null, "engines": {}, "os": [], "cpu": [], "private": false, "workspaces": null}