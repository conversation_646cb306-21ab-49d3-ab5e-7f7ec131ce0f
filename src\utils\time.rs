use chrono::{DateTime, Utc, Duration};
use std::time::{SystemTime, UNIX_EPOCH};

/// Time utilities
pub struct TimeUtils;

impl TimeUtils {
    /// Get current timestamp as seconds since epoch
    pub fn now_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// Get current time as ISO 8601 string
    pub fn now_iso8601() -> String {
        Utc::now().to_rfc3339()
    }
    
    /// Convert timestamp to ISO 8601 string
    pub fn timestamp_to_iso8601(timestamp: u64) -> String {
        DateTime::from_timestamp(timestamp as i64, 0)
            .unwrap_or_else(|| Utc::now())
            .to_rfc3339()
    }
    
    /// Check if a timestamp is older than the specified number of days
    pub fn is_older_than_days(timestamp: u64, days: u64) -> bool {
        let now = Self::now_timestamp();
        let age_seconds = now.saturating_sub(timestamp);
        let age_days = age_seconds / (24 * 60 * 60);
        age_days > days
    }
    
    /// Format duration in a human-readable way
    pub fn format_duration(duration: std::time::Duration) -> String {
        let total_secs = duration.as_secs();
        let millis = duration.subsec_millis();
        
        if total_secs >= 3600 {
            let hours = total_secs / 3600;
            let mins = (total_secs % 3600) / 60;
            let secs = total_secs % 60;
            format!("{}h {}m {}s", hours, mins, secs)
        } else if total_secs >= 60 {
            let mins = total_secs / 60;
            let secs = total_secs % 60;
            format!("{}m {}s", mins, secs)
        } else if total_secs > 0 {
            format!("{}.{:03}s", total_secs, millis)
        } else {
            format!("{}ms", millis)
        }
    }
    
    /// Get age of a file in days
    pub fn file_age_days(modified: SystemTime) -> u64 {
        let now = SystemTime::now();
        if let Ok(duration) = now.duration_since(modified) {
            duration.as_secs() / (24 * 60 * 60)
        } else {
            0
        }
    }
    
    /// Create a timeout future
    pub async fn timeout<F, T>(duration: std::time::Duration, future: F) -> Result<T, tokio::time::error::Elapsed>
    where
        F: std::future::Future<Output = T>,
    {
        tokio::time::timeout(duration, future).await
    }
}
