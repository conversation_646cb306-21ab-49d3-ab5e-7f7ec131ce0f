use anyhow::{Result, Context};
use std::collections::HashMap;
use semver::{Version, VersionReq};

/// Smart version resolver for package dependencies
#[derive(Debug, Clone)]
pub struct VersionResolver {
    /// Cache of resolved versions
    version_cache: HashMap<String, Version>,
}

impl VersionResolver {
    pub fn new() -> Self {
        Self {
            version_cache: HashMap::new(),
        }
    }

    /// Resolve the best version for a package given a version requirement
    pub fn resolve_version(
        &mut self,
        package_name: &str,
        version_req: &str,
        available_versions: &[String],
    ) -> Result<String> {
        let cache_key = format!("{}@{}", package_name, version_req);
        
        // Check cache first
        if let Some(cached_version) = self.version_cache.get(&cache_key) {
            return Ok(cached_version.to_string());
        }

        // Parse version requirement
        let req = VersionReq::parse(version_req)
            .context(format!("Invalid version requirement: {}", version_req))?;

        // Parse and sort available versions
        let mut versions: Vec<Version> = available_versions
            .iter()
            .filter_map(|v| Version::parse(v).ok())
            .collect();
        
        versions.sort_by(|a, b| b.cmp(a)); // Sort in descending order (newest first)

        // Find the best matching version
        for version in &versions {
            if req.matches(version) {
                self.version_cache.insert(cache_key, version.clone());
                return Ok(version.to_string());
            }
        }

        Err(anyhow::anyhow!(
            "No version of {} satisfies requirement {}",
            package_name,
            version_req
        ))
    }

    /// Resolve conflicts between different version requirements
    pub fn resolve_conflicts(
        &mut self,
        package_name: &str,
        requirements: &[String],
        available_versions: &[String],
    ) -> Result<String> {
        if requirements.is_empty() {
            return Err(anyhow::anyhow!("No requirements provided"));
        }

        if requirements.len() == 1 {
            return self.resolve_version(package_name, &requirements[0], available_versions);
        }

        // Parse all requirements
        let reqs: Result<Vec<VersionReq>, _> = requirements
            .iter()
            .map(|r| VersionReq::parse(r))
            .collect();

        let reqs = reqs.context("Failed to parse version requirements")?;

        // Parse and sort available versions
        let mut versions: Vec<Version> = available_versions
            .iter()
            .filter_map(|v| Version::parse(v).ok())
            .collect();
        
        versions.sort_by(|a, b| b.cmp(a)); // Sort in descending order

        // Find a version that satisfies all requirements
        for version in &versions {
            if reqs.iter().all(|req| req.matches(version)) {
                return Ok(version.to_string());
            }
        }

        Err(anyhow::anyhow!(
            "No version of {} satisfies all requirements: {:?}",
            package_name,
            requirements
        ))
    }

    /// Get the latest stable version (non-prerelease)
    pub fn get_latest_stable(&self, available_versions: &[String]) -> Result<String> {
        let mut versions: Vec<Version> = available_versions
            .iter()
            .filter_map(|v| Version::parse(v).ok())
            .filter(|v| v.pre.is_empty()) // Filter out prerelease versions
            .collect();

        if versions.is_empty() {
            return Err(anyhow::anyhow!("No stable versions available"));
        }

        versions.sort_by(|a, b| b.cmp(a)); // Sort in descending order
        Ok(versions[0].to_string())
    }

    /// Check if a version satisfies a requirement
    pub fn satisfies(&self, version: &str, requirement: &str) -> Result<bool> {
        let version = Version::parse(version)
            .context(format!("Invalid version: {}", version))?;
        
        let req = VersionReq::parse(requirement)
            .context(format!("Invalid requirement: {}", requirement))?;

        Ok(req.matches(&version))
    }

    /// Generate a caret range for a version (^1.2.3)
    pub fn generate_caret_range(&self, version: &str) -> Result<String> {
        let version = Version::parse(version)
            .context(format!("Invalid version: {}", version))?;
        
        Ok(format!("^{}", version))
    }

    /// Generate a tilde range for a version (~1.2.3)
    pub fn generate_tilde_range(&self, version: &str) -> Result<String> {
        let version = Version::parse(version)
            .context(format!("Invalid version: {}", version))?;
        
        Ok(format!("~{}", version))
    }

    /// Generate an exact range for a version (1.2.3)
    pub fn generate_exact_range(&self, version: &str) -> Result<String> {
        let version = Version::parse(version)
            .context(format!("Invalid version: {}", version))?;
        
        Ok(version.to_string())
    }

    /// Clear the version cache
    pub fn clear_cache(&mut self) {
        self.version_cache.clear();
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> (usize, usize) {
        (self.version_cache.len(), self.version_cache.capacity())
    }
}

impl Default for VersionResolver {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_resolve_version() {
        let mut resolver = VersionResolver::new();
        let available = vec!["1.0.0".to_string(), "1.1.0".to_string(), "2.0.0".to_string()];
        
        let result = resolver.resolve_version("test", "^1.0.0", &available).unwrap();
        assert_eq!(result, "1.1.0");
    }

    #[test]
    fn test_resolve_conflicts() {
        let mut resolver = VersionResolver::new();
        let available = vec!["1.0.0".to_string(), "1.1.0".to_string(), "2.0.0".to_string()];
        let requirements = vec!["^1.0.0".to_string(), ">=1.1.0".to_string()];
        
        let result = resolver.resolve_conflicts("test", &requirements, &available).unwrap();
        assert_eq!(result, "1.1.0");
    }

    #[test]
    fn test_latest_stable() {
        let resolver = VersionResolver::new();
        let available = vec![
            "1.0.0".to_string(), 
            "1.1.0".to_string(), 
            "2.0.0-beta.1".to_string(),
            "2.0.0".to_string()
        ];
        
        let result = resolver.get_latest_stable(&available).unwrap();
        assert_eq!(result, "2.0.0");
    }
}
