use indicatif::{ProgressBar, ProgressStyle};
use std::time::Duration;

/// Progress bar utilities
pub struct ProgressUtils;

impl ProgressUtils {
    /// Create a download progress bar
    pub fn download_progress(total_bytes: u64) -> ProgressBar {
        let pb = ProgressBar::new(total_bytes);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes} ({bytes_per_sec}, {eta})")
                .unwrap()
                .progress_chars("#>-")
        );
        pb
    }
    
    /// Create an installation progress bar
    pub fn install_progress(total_packages: u64) -> ProgressBar {
        let pb = ProgressBar::new(total_packages);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {pos}/{len} packages ({per_sec}, {eta})")
                .unwrap()
                .progress_chars("#>-")
        );
        pb
    }
    
    /// Create a spinner for indeterminate progress
    pub fn spinner(message: &str) -> ProgressBar {
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.blue} {msg}")
                .unwrap()
        );
        pb.set_message(message.to_string());
        pb.enable_steady_tick(Duration::from_millis(100));
        pb
    }
    
    /// Create a simple progress bar
    pub fn simple_progress(total: u64, message: &str) -> ProgressBar {
        let pb = ProgressBar::new(total);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} {msg} [{wide_bar:.cyan/blue}] {pos}/{len}")
                .unwrap()
                .progress_chars("#>-")
        );
        pb.set_message(message.to_string());
        pb
    }
}
