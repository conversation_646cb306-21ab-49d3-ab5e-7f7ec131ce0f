use anyhow::Result;
use clap::Args;
use std::collections::HashMap;
use std::path::Path;
use tracing::info;

use crate::installer::{Installer, PackageInstallInfo};
use crate::package::PackageJson;
use crate::registry::Registry;
use crate::resolver::Resolver;
use crate::ui::UI;

/// Update packages to their latest versions
#[derive(Debug, Args)]
pub struct UpdateArgs {
    /// Specific packages to update
    packages: Vec<String>,
    
    /// Update all packages
    #[arg(short, long)]
    all: bool,
    
    /// Save exact versions (no ^ or ~)
    #[arg(long)]
    save_exact: bool,
    
    /// Update dev dependencies
    #[arg(long)]
    dev: bool,
    
    /// Dry run - show what would be updated without actually updating
    #[arg(long)]
    dry_run: bool,
}

pub async fn update(args: UpdateArgs) -> Result<()> {
    let ui = UI::new();
    let registry = Registry::new();
    let resolver = Resolver::new(registry.clone());
    
    // Check if package.json exists
    if !Path::new("package.json").exists() {
        ui.error("No package.json found in current directory");
        return Ok(());
    }
    
    ui.info("🔄 Checking for package updates...");
    
    let spinner = ui.create_spinner("Analyzing current packages");
    
    // Read current package.json
    let mut package_json = PackageJson::read("package.json").await?;
    
    let mut packages_to_update = Vec::new();
    let mut updated_dependencies = HashMap::new();
    let mut updated_dev_dependencies = HashMap::new();
    
    // Determine which packages to update
    if args.all || args.packages.is_empty() {
        // Update all dependencies
        for (name, _spec) in &package_json.dependencies {
            packages_to_update.push(name.clone());
        }
        
        if args.dev {
            for (name, _spec) in &package_json.dev_dependencies {
                packages_to_update.push(name.clone());
            }
        }
    } else {
        // Update specific packages
        packages_to_update = args.packages.clone();
    }
    
    if packages_to_update.is_empty() {
        spinner.finish_with_message("✓ No packages to update");
        ui.info("No packages specified for update");
        return Ok(());
    }
    
    spinner.finish_with_message("✓ Package analysis completed");
    
    ui.info(&format!("Checking {} package(s) for updates...", packages_to_update.len()));
    
    let mut install_packages = Vec::new();
    let mut update_count = 0;
    
    // Check each package for updates
    for package_name in &packages_to_update {
        let check_spinner = ui.create_spinner(&format!("Checking {}", package_name));
        
        match registry.get_package_metadata(package_name).await {
            Ok(metadata) => {
                let latest_version = metadata.latest_version.clone();
                let is_dev_dep = package_json.dev_dependencies.contains_key(package_name);
                let is_regular_dep = package_json.dependencies.contains_key(package_name);
                
                if !is_dev_dep && !is_regular_dep {
                    check_spinner.finish_with_message(&format!("⚠️  {} not found in dependencies", package_name));
                    continue;
                }
                
                // Get current version spec
                let current_spec = if is_regular_dep {
                    package_json.dependencies.get(package_name).unwrap()
                } else {
                    package_json.dev_dependencies.get(package_name).unwrap()
                };
                
                // Check if package is installed and get current version
                let node_modules_path = format!("node_modules/{}", package_name);
                let current_version = if Path::new(&node_modules_path).exists() {
                    let installed_package_path = format!("{}/package.json", node_modules_path);
                    if let Ok(installed_package) = PackageJson::read(&installed_package_path).await {
                        Some(installed_package.version)
                    } else {
                        None
                    }
                } else {
                    None
                };
                
                // Determine if update is needed
                let needs_update = match &current_version {
                    Some(current) => current != &latest_version,
                    None => true, // Package not installed
                };
                
                if needs_update {
                    // Find the best version info for the latest version
                    if let Some(version_info) = metadata.versions.get(&latest_version) {
                        let install_info = PackageInstallInfo {
                            name: package_name.clone(),
                            version: latest_version.clone(),
                            tarball_url: version_info.dist.tarball.clone(),
                            integrity: version_info.dist.integrity.clone(),
                        };
                        
                        install_packages.push(install_info);
                        
                        // Update package.json specs
                        let new_spec = if args.save_exact {
                            latest_version.clone()
                        } else {
                            format!("^{}", latest_version)
                        };
                        
                        if is_regular_dep {
                            updated_dependencies.insert(package_name.clone(), new_spec);
                        } else {
                            updated_dev_dependencies.insert(package_name.clone(), new_spec);
                        }
                        
                        update_count += 1;
                        
                        let update_msg = match &current_version {
                            Some(current) => format!("📦 {} {} → {}", package_name, current, latest_version),
                            None => format!("📦 {} → {} (new install)", package_name, latest_version),
                        };
                        
                        check_spinner.finish_with_message(&update_msg);
                    } else {
                        check_spinner.finish_with_message(&format!("⚠️  No version info for {}", package_name));
                    }
                } else {
                    check_spinner.finish_with_message(&format!("✓ {} is up to date", package_name));
                }
            }
            Err(e) => {
                check_spinner.finish_with_message(&format!("❌ Failed to check {}: {}", package_name, e));
            }
        }
    }
    
    if update_count == 0 {
        ui.success("All packages are already up to date! 🎉");
        return Ok(());
    }
    
    if args.dry_run {
        ui.info(&format!("Dry run: {} package(s) would be updated", update_count));
        return Ok(());
    }
    
    ui.info(&format!("Updating {} package(s)...", update_count));
    
    // Install updated packages
    if !install_packages.is_empty() {
        let cache_dir = std::env::current_dir()?.join(".nx-cache");
        let install_dir = std::env::current_dir()?;
        let installer = Installer::new(cache_dir, install_dir, 50);
        
        installer.install_packages(install_packages, &ui).await?;
    }
    
    // Update package.json
    for (name, spec) in updated_dependencies {
        package_json.dependencies.insert(name, spec);
    }
    
    for (name, spec) in updated_dev_dependencies {
        package_json.dev_dependencies.insert(name, spec);
    }
    
    // Write updated package.json
    package_json.write("package.json").await?;
    
    ui.success(&format!("Successfully updated {} package(s)! 🎉", update_count));
    
    Ok(())
}
