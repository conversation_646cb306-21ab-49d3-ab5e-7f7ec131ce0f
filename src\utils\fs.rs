use anyhow::Result;
use std::path::{Path, PathBuf};
use tokio::fs;
use tracing::debug;

/// Ultra-fast file system utilities optimized for package management
pub struct FsUtils;

impl FsUtils {
    /// Recursively remove directory with optimized performance
    pub async fn remove_dir_all<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        if !path.exists() {
            return Ok(());
        }

        debug!("Removing directory: {}", path.display());
        
        // Use tokio's optimized remove_dir_all
        fs::remove_dir_all(path).await?;
        Ok(())
    }

    /// Copy file with progress tracking
    pub async fn copy_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> Result<u64> {
        let from = from.as_ref();
        let to = to.as_ref();
        
        // Create parent directory if needed
        if let Some(parent) = to.parent() {
            fs::create_dir_all(parent).await?;
        }

        let bytes_copied = fs::copy(from, to).await?;
        debug!("Copied {} bytes from {} to {}", bytes_copied, from.display(), to.display());
        Ok(bytes_copied)
    }

    /// Create directory with all parents
    pub async fn create_dir_all<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        if !path.exists() {
            fs::create_dir_all(path).await?;
            debug!("Created directory: {}", path.display());
        }
        Ok(())
    }

    /// Check if path exists
    pub async fn exists<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().exists()
    }

    /// Get file size
    pub async fn file_size<P: AsRef<Path>>(path: P) -> Result<u64> {
        let metadata = fs::metadata(path).await?;
        Ok(metadata.len())
    }

    /// Get directory size recursively
    pub async fn dir_size<P: AsRef<Path>>(path: P) -> Result<u64> {
        let path = path.as_ref().to_path_buf();
        tokio::task::spawn_blocking(move || {
            Self::dir_size_sync(&path)
        }).await?
    }

    fn dir_size_sync(path: &std::path::Path) -> Result<u64> {
        let mut total_size = 0;

        for entry in std::fs::read_dir(path)? {
            let entry = entry?;
            let metadata = entry.metadata()?;

            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                total_size += Self::dir_size_sync(&entry.path())?;
            }
        }

        Ok(total_size)
    }

    /// Create symlink (cross-platform)
    pub async fn create_symlink<P: AsRef<Path>, Q: AsRef<Path>>(
        original: P,
        link: Q,
    ) -> Result<()> {
        let original = original.as_ref();
        let link = link.as_ref();

        // Create parent directory if needed
        if let Some(parent) = link.parent() {
            fs::create_dir_all(parent).await?;
        }

        #[cfg(unix)]
        {
            use std::os::unix::fs::symlink;
            tokio::task::spawn_blocking(move || {
                symlink(original, link)
            }).await??;
        }

        #[cfg(windows)]
        {
            // On Windows, copy the file instead of creating symlink
            fs::copy(original, link).await?;
        }

        debug!("Created symlink from {} to {}", original.display(), link.display());
        Ok(())
    }

    /// Make file executable (Unix only)
    #[cfg(unix)]
    pub async fn make_executable<P: AsRef<Path>>(path: P) -> Result<()> {
        use std::os::unix::fs::PermissionsExt;
        
        let path = path.as_ref();
        let metadata = fs::metadata(path).await?;
        let mut permissions = metadata.permissions();
        
        // Add execute permission for owner, group, and others
        permissions.set_mode(permissions.mode() | 0o111);
        fs::set_permissions(path, permissions).await?;
        
        debug!("Made file executable: {}", path.display());
        Ok(())
    }

    #[cfg(windows)]
    pub async fn make_executable<P: AsRef<Path>>(_path: P) -> Result<()> {
        // On Windows, files are executable by default if they have .exe extension
        Ok(())
    }

    /// Find files matching a pattern
    pub async fn find_files<P: AsRef<Path>>(
        dir: P,
        pattern: &str,
    ) -> Result<Vec<PathBuf>> {
        let dir = dir.as_ref();
        let mut files = Vec::new();
        let regex = regex::Regex::new(pattern)?;

        let mut entries = fs::read_dir(dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if path.is_file() {
                if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                    if regex.is_match(filename) {
                        files.push(path);
                    }
                }
            } else if path.is_dir() {
                // Recursively search subdirectories
                let mut subfiles = Self::find_files(&path, pattern).await?;
                files.append(&mut subfiles);
            }
        }

        Ok(files)
    }

    /// Clean up empty directories
    pub async fn cleanup_empty_dirs<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref().to_path_buf();
        tokio::task::spawn_blocking(move || {
            Self::cleanup_empty_dirs_sync(&path)
        }).await?
    }

    fn cleanup_empty_dirs_sync(path: &std::path::Path) -> Result<()> {
        if !path.is_dir() {
            return Ok(());
        }

        let mut _has_files = false;

        for entry in std::fs::read_dir(path)? {
            let entry = entry?;
            let entry_path = entry.path();

            if entry_path.is_dir() {
                // Recursively clean subdirectories
                Self::cleanup_empty_dirs_sync(&entry_path)?;

                // Check if directory is now empty
                if std::fs::read_dir(&entry_path)?.next().is_none() {
                    std::fs::remove_dir(&entry_path)?;
                    debug!("Removed empty directory: {}", entry_path.display());
                } else {
                    _has_files = true;
                }
            } else {
                _has_files = true;
            }
        }

        Ok(())
    }

    /// Get temporary directory for package operations
    pub fn get_temp_dir() -> PathBuf {
        std::env::temp_dir().join("nx-temp")
    }

    /// Ensure directory is writable
    pub async fn ensure_writable<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        
        // Try to create a test file
        let test_file = path.join(".nx-write-test");
        match fs::write(&test_file, b"test").await {
            Ok(_) => {
                // Clean up test file
                let _ = fs::remove_file(&test_file).await;
                Ok(())
            }
            Err(e) => Err(anyhow::anyhow!("Directory {} is not writable: {}", path.display(), e)),
        }
    }
}

/// Get the size of a path (file or directory)
pub async fn get_path_size<P: AsRef<Path>>(path: P) -> Result<u64> {
    let path = path.as_ref();
    let metadata = fs::metadata(path).await?;
    
    if metadata.is_file() {
        Ok(metadata.len())
    } else if metadata.is_dir() {
        FsUtils::dir_size(path).await
    } else {
        Ok(0)
    }
}

/// Check if a path is a package directory (contains package.json)
pub fn is_package_dir<P: AsRef<Path>>(path: P) -> bool {
    path.as_ref().join("package.json").exists()
}

/// Get the package name from a package.json file
pub async fn get_package_name<P: AsRef<Path>>(package_json_path: P) -> Result<Option<String>> {
    let content = fs::read_to_string(package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;
    
    Ok(package.get("name")
        .and_then(|v| v.as_str())
        .map(|s| s.to_string()))
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_create_and_remove_dir() {
        let temp_dir = TempDir::new().unwrap();
        let test_path = temp_dir.path().join("test_dir");
        
        FsUtils::create_dir_all(&test_path).await.unwrap();
        assert!(test_path.exists());
        
        FsUtils::remove_dir_all(&test_path).await.unwrap();
        assert!(!test_path.exists());
    }

    #[tokio::test]
    async fn test_file_operations() {
        let temp_dir = TempDir::new().unwrap();
        let source = temp_dir.path().join("source.txt");
        let dest = temp_dir.path().join("dest.txt");
        
        fs::write(&source, b"test content").await.unwrap();
        
        let bytes_copied = FsUtils::copy_file(&source, &dest).await.unwrap();
        assert_eq!(bytes_copied, 12);
        assert!(dest.exists());
        
        let size = FsUtils::file_size(&dest).await.unwrap();
        assert_eq!(size, 12);
    }
}
