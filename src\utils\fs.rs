use anyhow::{Result, Context};
use std::path::{Path, PathBuf};
use tokio::fs;
use tracing::{debug, warn};

/// File system utilities
pub struct FsUtils;

impl FsUtils {
    /// Create a directory and all parent directories
    pub async fn create_dir_all<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        fs::create_dir_all(path).await
            .with_context(|| format!("Failed to create directory: {}", path.display()))?;
        debug!("Created directory: {}", path.display());
        Ok(())
    }
    
    /// Remove a directory and all its contents
    pub async fn remove_dir_all<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        if path.exists() {
            fs::remove_dir_all(path).await
                .with_context(|| format!("Failed to remove directory: {}", path.display()))?;
            debug!("Removed directory: {}", path.display());
        }
        Ok(())
    }
    
    /// Copy a file from source to destination
    pub async fn copy_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> Result<()> {
        let from = from.as_ref();
        let to = to.as_ref();
        
        // Ensure destination directory exists
        if let Some(parent) = to.parent() {
            Self::create_dir_all(parent).await?;
        }
        
        fs::copy(from, to).await
            .with_context(|| format!("Failed to copy {} to {}", from.display(), to.display()))?;
        
        debug!("Copied {} to {}", from.display(), to.display());
        Ok(())
    }

    /// Copy a directory and all its contents recursively using synchronous operations
    pub async fn copy_dir_all<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> Result<()> {
        let from_path = from.as_ref().to_path_buf();
        let to_path = to.as_ref().to_path_buf();

        // Clone paths for debug message
        let from_display = from_path.display().to_string();
        let to_display = to_path.display().to_string();

        // Use blocking task to avoid async recursion issues
        tokio::task::spawn_blocking(move || {
            Self::copy_dir_all_sync(&from_path, &to_path)
        }).await??;

        debug!("Copied directory {} to {}", from_display, to_display);
        Ok(())
    }

    /// Synchronous recursive directory copy
    fn copy_dir_all_sync(from: &Path, to: &Path) -> Result<()> {
        use std::fs;

        // Create destination directory
        fs::create_dir_all(to)?;

        // Read source directory
        for entry in fs::read_dir(from)? {
            let entry = entry?;
            let entry_path = entry.path();
            let file_name = entry.file_name();
            let dest_path = to.join(&file_name);

            if entry_path.is_dir() {
                // Recursively copy subdirectory
                Self::copy_dir_all_sync(&entry_path, &dest_path)?;
            } else {
                // Copy file
                fs::copy(&entry_path, &dest_path)?;
            }
        }

        Ok(())
    }

    /// Create a symlink
    pub async fn create_symlink<P: AsRef<Path>, Q: AsRef<Path>>(original: P, link: Q) -> Result<()> {
        let original = original.as_ref();
        let link = link.as_ref();
        
        // Ensure link directory exists
        if let Some(parent) = link.parent() {
            Self::create_dir_all(parent).await?;
        }
        
        #[cfg(unix)]
        {
            tokio::fs::symlink(original, link).await
                .with_context(|| format!("Failed to create symlink {} -> {}", link.display(), original.display()))?;
        }
        
        #[cfg(windows)]
        {
            if original.is_dir() {
                tokio::fs::symlink_dir(original, link).await
                    .with_context(|| format!("Failed to create directory symlink {} -> {}", link.display(), original.display()))?;
            } else {
                tokio::fs::symlink_file(original, link).await
                    .with_context(|| format!("Failed to create file symlink {} -> {}", link.display(), original.display()))?;
            }
        }
        
        debug!("Created symlink {} -> {}", link.display(), original.display());
        Ok(())
    }
    
    /// Create a hard link
    pub async fn create_hardlink<P: AsRef<Path>, Q: AsRef<Path>>(original: P, link: Q) -> Result<()> {
        let original = original.as_ref();
        let link = link.as_ref();
        
        // Ensure link directory exists
        if let Some(parent) = link.parent() {
            Self::create_dir_all(parent).await?;
        }
        
        fs::hard_link(original, link).await
            .with_context(|| format!("Failed to create hard link {} -> {}", link.display(), original.display()))?;
        
        debug!("Created hard link {} -> {}", link.display(), original.display());
        Ok(())
    }
    
    /// Get the size of a file or directory
    pub async fn get_size<P: AsRef<Path>>(path: P) -> Result<u64> {
        let path = path.as_ref();
        
        if path.is_file() {
            let metadata = fs::metadata(path).await?;
            Ok(metadata.len())
        } else if path.is_dir() {
            Self::get_dir_size(path).await
        } else {
            Ok(0)
        }
    }
    
    /// Get the total size of a directory
    pub async fn get_dir_size<P: AsRef<Path>>(path: P) -> Result<u64> {
        let path = path.as_ref();
        Self::get_dir_size_recursive(path).await
    }

    /// Recursive helper for directory size calculation
    fn get_dir_size_recursive(path: &Path) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<u64>> + Send + '_>> {
        Box::pin(async move {
            let mut total_size = 0;

            let mut entries = fs::read_dir(path).await?;
            while let Some(entry) = entries.next_entry().await? {
                let entry_path = entry.path();
                if entry_path.is_file() {
                    let metadata = entry.metadata().await?;
                    total_size += metadata.len();
                } else if entry_path.is_dir() {
                    total_size += Self::get_dir_size_recursive(&entry_path).await?;
                }
            }

            Ok(total_size)
        })
    }
    
    /// Check if a path exists
    pub async fn exists<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref().exists()
    }
    
    /// Make a file executable
    #[cfg(unix)]
    pub async fn make_executable<P: AsRef<Path>>(path: P) -> Result<()> {
        use std::os::unix::fs::PermissionsExt;
        
        let path = path.as_ref();
        let metadata = fs::metadata(path).await?;
        let mut permissions = metadata.permissions();
        permissions.set_mode(permissions.mode() | 0o111);
        fs::set_permissions(path, permissions).await?;
        
        debug!("Made executable: {}", path.display());
        Ok(())
    }
    
    #[cfg(windows)]
    pub async fn make_executable<P: AsRef<Path>>(_path: P) -> Result<()> {
        // On Windows, executability is determined by file extension
        Ok(())
    }
}
