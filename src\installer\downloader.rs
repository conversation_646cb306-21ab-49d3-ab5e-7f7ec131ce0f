use anyhow::{Result, Context};
use indicatif::{ProgressBar, MultiProgress};
use reqwest::Client;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;
use tokio::sync::Semaphore;
use tokio::io::AsyncWriteExt;
use tracing::{debug, info, warn, error};
use futures::stream::{self, StreamExt};
use sha2::{Sha256, Digest};
use std::time::{Duration, Instant};

use crate::utils::{HashUtils, FsUtils};
use crate::ui::{ProgressUtils, SpinnerFactory, UI};

/// Package downloader with parallel download support
#[derive(Debug)]
pub struct Downloader {
    client: Client,
    cache_dir: PathBuf,
    semaphore: Arc<Semaphore>,
    multi_progress: Arc<MultiProgress>,
}

impl Downloader {
    pub fn new(cache_dir: PathBuf, max_concurrent: usize) -> Self {
        let client = Client::builder()
            .user_agent("nx/0.1.0")
            .gzip(true)
            .timeout(std::time::Duration::from_secs(30)) // Reasonable timeout
            .connect_timeout(std::time::Duration::from_secs(10)) // Reasonable connection timeout
            .pool_max_idle_per_host(50) // Reasonable connection pool
            .pool_idle_timeout(std::time::Duration::from_secs(60))
            .tcp_keepalive(Some(std::time::Duration::from_secs(60)))
            .tcp_nodelay(true) // Disable Nagle's algorithm for lower latency
            // Remove HTTP/2 configurations for better compatibility
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            cache_dir,
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
            multi_progress: Arc::new(MultiProgress::new()),
        }
    }
    
    /// Download a package tarball with progress tracking and integrity verification
    pub async fn download(
        &self,
        name: &str,
        version: &str,
        url: &str,
        expected_integrity: Option<&str>,
        ui: &UI,
    ) -> Result<PathBuf> {
        let cache_path = self.get_cache_path(name, version);

        // Check if already cached and valid
        if cache_path.exists() {
            if let Some(integrity) = expected_integrity {
                if self.verify_cached_file(&cache_path, integrity).await.unwrap_or(false) {
                    debug!("Package {}@{} already cached and verified", name, version);
                    return Ok(cache_path);
                } else {
                    warn!("Cached file for {}@{} failed integrity check, re-downloading", name, version);
                    let _ = fs::remove_file(&cache_path).await;
                }
            } else {
                debug!("Using cached tarball for {}@{}", name, version);
                return Ok(cache_path);
            }
        }

        // Acquire semaphore permit for concurrent downloads
        let _permit = self.semaphore.acquire().await?;

        info!("Downloading {}@{} from {}", name, version, url);

        // Get content length for progress bar
        let head_response = self.client.head(url).send().await?;
        let content_length = head_response
            .headers()
            .get("content-length")
            .and_then(|ct_len| ct_len.to_str().ok())
            .and_then(|ct_len| ct_len.parse::<u64>().ok())
            .unwrap_or(0);

        // Create beautiful progress bar
        let progress_bar = ui.create_download_progress(content_length, name);

        // Download with retry logic and progress tracking
        let mut attempts = 0;
        let max_attempts = 3;

        let response = loop {
            attempts += 1;

            match self.client.get(url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        break response;
                    } else if attempts >= max_attempts {
                        progress_bar.finish_with_message("Failed");
                        return Err(anyhow::anyhow!("Download failed with status: {} after {} attempts", response.status(), attempts));
                    } else {
                        warn!("Download attempt {} failed with status: {}, retrying...", attempts, response.status());
                        tokio::time::sleep(tokio::time::Duration::from_millis(1000 * attempts as u64)).await;
                    }
                }
                Err(e) => {
                    if attempts >= max_attempts {
                        progress_bar.finish_with_message("Failed");
                        return Err(anyhow::anyhow!("Download failed after {} attempts: {}", attempts, e));
                    } else {
                        warn!("Download attempt {} failed: {}, retrying...", attempts, e);
                        tokio::time::sleep(tokio::time::Duration::from_millis(1000 * attempts as u64)).await;
                    }
                }
            }
        };

        // Get content length for progress tracking
        let total_size = response.content_length().unwrap_or(0);
        progress_bar.set_length(total_size);

        // Stream the response and update progress
        let mut data = Vec::new();
        let mut stream = response.bytes_stream();

        use futures_util::StreamExt;
        while let Some(chunk) = stream.next().await {
            let chunk = chunk.context("Failed to read chunk")?;
            data.extend_from_slice(&chunk);
            progress_bar.inc(chunk.len() as u64);
        }

        progress_bar.finish_with_message("Downloaded");

        // Verify integrity if provided
        if let Some(integrity) = expected_integrity {
            if !HashUtils::verify_integrity(&data, integrity)? {
                return Err(anyhow::anyhow!("Integrity check failed for {}@{}", name, version));
            }
        }

        // Ensure cache directory exists
        if let Some(parent) = cache_path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }

        // Write to cache
        fs::write(&cache_path, &data).await
            .context("Failed to write to cache")?;

        debug!("Cached {}@{} at {}", name, version, cache_path.display());
        Ok(cache_path)
    }

    /// Verify cached file integrity
    async fn verify_cached_file(&self, path: &Path, expected_integrity: &str) -> Result<bool> {
        let data = fs::read(path).await?;
        Ok(HashUtils::verify_integrity(&data, expected_integrity)?)
    }
    
    /// Download multiple packages in parallel with beautiful UI
    pub async fn download_multiple(
        &self,
        downloads: Vec<DownloadRequest>,
        ui: &UI,
    ) -> Result<Vec<DownloadResult>> {
        info!("Starting parallel download of {} packages", downloads.len());

        let mut handles = Vec::new();

        for request in downloads {
            let downloader = self.clone();
            let ui_clone = ui.clone();
            let handle = tokio::spawn(async move {
                let start_time = std::time::Instant::now();
                match downloader.download(
                    &request.name,
                    &request.version,
                    &request.url,
                    request.integrity.as_deref(),
                    &ui_clone,
                ).await {
                    Ok(path) => DownloadResult {
                        name: request.name,
                        version: request.version,
                        path: Some(path),
                        error: None,
                        duration: start_time.elapsed(),
                    },
                    Err(e) => DownloadResult {
                        name: request.name,
                        version: request.version,
                        path: None,
                        error: Some(e.to_string()),
                        duration: start_time.elapsed(),
                    },
                }
            });
            handles.push(handle);
        }

        // Wait for all downloads to complete
        let mut results = Vec::new();
        for handle in handles {
            results.push(handle.await?);
        }

        let successful = results.iter().filter(|r| r.error.is_none()).count();
        let failed = results.len() - successful;

        info!("Parallel download completed: {} successful, {} failed", successful, failed);

        if failed > 0 {
            warn!("Some downloads failed:");
            for result in &results {
                if let Some(error) = &result.error {
                    warn!("  {}@{}: {}", result.name, result.version, error);
                }
            }
        }

        Ok(results)
    }

    /// Get download statistics
    pub fn get_stats(&self) -> DownloadStats {
        DownloadStats {
            max_concurrent: self.semaphore.available_permits(),
            cache_dir: self.cache_dir.clone(),
        }
    }

    /// Get the cache path for a package
    fn get_cache_path(&self, name: &str, version: &str) -> PathBuf {
        let normalized_name = crate::utils::Utils::normalize_package_name(name);
        self.cache_dir
            .join("tarballs")
            .join(&normalized_name)
            .join(format!("{}-{}.tgz", normalized_name, version))
    }
}

impl Clone for Downloader {
    fn clone(&self) -> Self {
        Self {
            client: self.client.clone(),
            cache_dir: self.cache_dir.clone(),
            semaphore: Arc::clone(&self.semaphore),
            multi_progress: Arc::clone(&self.multi_progress),
        }
    }
}

/// Download request information
#[derive(Debug, Clone)]
pub struct DownloadRequest {
    pub name: String,
    pub version: String,
    pub url: String,
    pub integrity: Option<String>,
}

/// Download result information
#[derive(Debug, Clone)]
pub struct DownloadResult {
    pub name: String,
    pub version: String,
    pub path: Option<PathBuf>,
    pub error: Option<String>,
    pub duration: std::time::Duration,
}

/// Download statistics
#[derive(Debug, Clone)]
pub struct DownloadStats {
    pub max_concurrent: usize,
    pub cache_dir: PathBuf,
}
