use anyhow::{anyhow, Result};
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::time::Instant;
use tracing::{info, warn};

use crate::cli::UninstallArgs;
use crate::ui::UI;
use crate::utils::fs::FsUtils;

/// Execute nx uninstall command
pub async fn execute(args: UninstallArgs, ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    
    if args.packages.is_empty() {
        return Err(anyhow!("No packages specified for uninstall"));
    }

    ui.step(&format!("🗑️  Uninstalling {} packages...", args.packages.len()));

    let node_modules_path = if args.global {
        dirs::home_dir()
            .unwrap_or_default()
            .join(".nx")
            .join("global")
            .join("node_modules")
    } else {
        PathBuf::from("node_modules")
    };

    if !node_modules_path.exists() {
        ui.warning("node_modules directory not found");
        return Ok(());
    }

    // Read current package.json to understand dependencies
    let (mut dependencies, mut dev_dependencies) = if !args.global {
        read_package_json_dependencies().await?
    } else {
        (HashMap::new(), HashMap::new())
    };

    let mut removed_packages = Vec::new();
    let mut failed_packages = Vec::new();

    // Remove each specified package
    for package_name in &args.packages {
        let package_path = node_modules_path.join(package_name);
        
        if !package_path.exists() {
            warn!("Package {} not found", package_name);
            failed_packages.push(package_name.clone());
            continue;
        }

        if args.dry_run {
            ui.info(&format!("Would remove: {}", package_name));
            removed_packages.push(package_name.clone());
        } else {
            match FsUtils::remove_dir_all(&package_path).await {
                Ok(_) => {
                    ui.success(&format!("Removed {}", package_name));
                    removed_packages.push(package_name.clone());
                    
                    // Remove from package.json dependencies
                    if !args.no_save && !args.global {
                        dependencies.remove(package_name);
                        dev_dependencies.remove(package_name);
                    }
                }
                Err(e) => {
                    warn!("Failed to remove {}: {}", package_name, e);
                    failed_packages.push(package_name.clone());
                }
            }
        }
    }

    // Find and remove orphaned dependencies
    if !args.dry_run && !args.global {
        let orphaned = find_orphaned_dependencies(&node_modules_path, &dependencies, &dev_dependencies).await?;
        
        if !orphaned.is_empty() {
            ui.step(&format!("🧹 Removing {} orphaned dependencies...", orphaned.len()));
            
            for orphan in &orphaned {
                let orphan_path = node_modules_path.join(orphan);
                match FsUtils::remove_dir_all(&orphan_path).await {
                    Ok(_) => {
                        ui.info(&format!("Removed orphaned dependency: {}", orphan));
                        removed_packages.push(orphan.clone());
                    }
                    Err(e) => {
                        warn!("Failed to remove orphaned dependency {}: {}", orphan, e);
                    }
                }
            }
        }
    }

    // Update package.json
    if !args.no_save && !args.global && !args.dry_run {
        update_package_json(&dependencies, &dev_dependencies).await?;
    }

    // Clean up empty .bin directory
    let bin_dir = node_modules_path.join(".bin");
    if bin_dir.exists() {
        cleanup_bin_directory(&bin_dir, &removed_packages).await?;
    }

    // Clean up empty directories
    FsUtils::cleanup_empty_dirs(&node_modules_path).await?;

    let total_time = start_time.elapsed();
    
    if removed_packages.is_empty() {
        ui.warning("No packages were removed");
    } else {
        ui.success(&format!(
            "✅ Removed {} packages in {:.3}s",
            removed_packages.len(),
            total_time.as_secs_f64()
        ));
    }

    if !failed_packages.is_empty() {
        ui.warning(&format!("Failed to remove {} packages: {}", 
                           failed_packages.len(), 
                           failed_packages.join(", ")));
    }

    Ok(())
}

async fn read_package_json_dependencies() -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    let package_json_path = PathBuf::from("package.json");
    if !package_json_path.exists() {
        return Ok((HashMap::new(), HashMap::new()));
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;

    let dependencies = package["dependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    let dev_dependencies = package["devDependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok((dependencies, dev_dependencies))
}

async fn find_orphaned_dependencies(
    node_modules_path: &PathBuf,
    dependencies: &HashMap<String, String>,
    dev_dependencies: &HashMap<String, String>,
) -> Result<Vec<String>> {
    let mut required_packages = HashSet::new();
    
    // Add direct dependencies
    for name in dependencies.keys() {
        required_packages.insert(name.clone());
    }
    for name in dev_dependencies.keys() {
        required_packages.insert(name.clone());
    }

    // Recursively find all required dependencies
    let mut to_check = required_packages.clone();
    let mut checked = HashSet::new();

    while let Some(package_name) = to_check.iter().next().cloned() {
        to_check.remove(&package_name);
        if checked.contains(&package_name) {
            continue;
        }
        checked.insert(package_name.clone());

        // Read package.json of this dependency
        let package_path = node_modules_path.join(&package_name).join("package.json");
        if let Ok(content) = tokio::fs::read_to_string(&package_path).await {
            if let Ok(package_json) = serde_json::from_str::<serde_json::Value>(&content) {
                // Add its dependencies to required set
                if let Some(deps) = package_json["dependencies"].as_object() {
                    for dep_name in deps.keys() {
                        if !checked.contains(dep_name) {
                            required_packages.insert(dep_name.clone());
                            to_check.insert(dep_name.clone());
                        }
                    }
                }
            }
        }
    }

    // Find installed packages that are not required
    let mut orphaned = Vec::new();
    let mut entries = tokio::fs::read_dir(node_modules_path).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let entry_name = entry.file_name().to_string_lossy().to_string();
        
        // Skip special directories
        if entry_name.starts_with('.') {
            continue;
        }
        
        // Handle scoped packages
        if entry_name.starts_with('@') {
            let mut scope_entries = tokio::fs::read_dir(entry.path()).await?;
            while let Some(scope_entry) = scope_entries.next_entry().await? {
                let scoped_name = format!("{}/{}", entry_name, scope_entry.file_name().to_string_lossy());
                if !required_packages.contains(&scoped_name) {
                    orphaned.push(scoped_name);
                }
            }
        } else if !required_packages.contains(&entry_name) {
            orphaned.push(entry_name);
        }
    }

    Ok(orphaned)
}

async fn update_package_json(
    dependencies: &HashMap<String, String>,
    dev_dependencies: &HashMap<String, String>,
) -> Result<()> {
    let package_json_path = PathBuf::from("package.json");
    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let mut package: serde_json::Value = serde_json::from_str(&content)?;

    // Update dependencies
    if dependencies.is_empty() {
        package["dependencies"] = serde_json::Value::Object(serde_json::Map::new());
    } else {
        let deps_obj: serde_json::Map<String, serde_json::Value> = dependencies
            .iter()
            .map(|(k, v)| (k.clone(), serde_json::Value::String(v.clone())))
            .collect();
        package["dependencies"] = serde_json::Value::Object(deps_obj);
    }

    // Update devDependencies
    if dev_dependencies.is_empty() {
        package["devDependencies"] = serde_json::Value::Object(serde_json::Map::new());
    } else {
        let dev_deps_obj: serde_json::Map<String, serde_json::Value> = dev_dependencies
            .iter()
            .map(|(k, v)| (k.clone(), serde_json::Value::String(v.clone())))
            .collect();
        package["devDependencies"] = serde_json::Value::Object(dev_deps_obj);
    }

    let updated_content = serde_json::to_string_pretty(&package)?;
    tokio::fs::write(&package_json_path, updated_content).await?;

    Ok(())
}

async fn cleanup_bin_directory(bin_dir: &PathBuf, removed_packages: &[String]) -> Result<()> {
    let mut entries = tokio::fs::read_dir(bin_dir).await?;
    let mut bins_to_remove = Vec::new();

    while let Some(entry) = entries.next_entry().await? {
        let bin_name = entry.file_name().to_string_lossy().to_string();
        
        // Check if this binary belongs to a removed package
        for package_name in removed_packages {
            // This is a simplified check - in practice, you'd need to track which binaries belong to which packages
            if bin_name.contains(package_name) || bin_name == *package_name {
                bins_to_remove.push(entry.path());
                break;
            }
        }
    }

    for bin_path in bins_to_remove {
        if let Err(e) = tokio::fs::remove_file(&bin_path).await {
            warn!("Failed to remove binary {}: {}", bin_path.display(), e);
        }
    }

    // Remove .bin directory if empty
    if let Ok(mut entries) = tokio::fs::read_dir(bin_dir).await {
        if entries.next_entry().await?.is_none() {
            let _ = tokio::fs::remove_dir(bin_dir).await;
        }
    }

    Ok(())
}
