use anyhow::{Result, Context};
use reqwest::{Client, Response};
use serde::de::DeserializeOwned;
use std::time::Duration;
use tracing::{debug, warn, error};
use url::Url;

use super::types::*;

/// HTTP client wrapper for registry communication
#[derive(Debug, Clone)]
pub struct RegistryClient {
    client: Client,
    base_url: Url,
    auth_token: Option<String>,
    timeout: Duration,
    retry_attempts: u32,
}

impl RegistryClient {
    pub fn new(config: RegistryConfig) -> Result<Self> {
        let client = Client::builder()
            .user_agent("nx/0.1.0")
            .gzip(true)
            .timeout(Duration::from_secs(config.timeout))
            .build()
            .context("Failed to create HTTP client")?;
        
        let base_url = Url::parse(&config.url)
            .context("Invalid registry URL")?;
        
        let auth_token = config.auth.map(|auth| auth.token);
        
        Ok(Self {
            client,
            base_url,
            auth_token,
            timeout: Duration::from_secs(config.timeout),
            retry_attempts: config.retry_attempts,
        })
    }
    
    /// Make a GET request to the registry
    pub async fn get<T>(&self, path: &str) -> Result<T>
    where
        T: DeserializeOwned,
    {
        let url = self.base_url.join(path)
            .context("Failed to construct request URL")?;

        debug!("Making GET request to: {}", url);

        // Retry logic with exponential backoff
        let mut last_error = None;
        for attempt in 1..=self.retry_attempts {
            let mut request = self.client.get(url.clone());

            // Add authentication if available
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }

            // Add npm-specific headers
            request = request
                .header("Accept", "application/vnd.npm.install-v1+json; q=1.0, application/json; q=0.8, */*")
                .header("User-Agent", "nx/0.1.0");

            match request.send().await {
                Ok(response) => {
                    let status = response.status();
                    if status.is_success() {
                        return self.parse_response(response).await;
                    } else if status.as_u16() == 404 {
                        return Err(anyhow::anyhow!("Package not found: {}", path));
                    } else if status.as_u16() == 401 || status.as_u16() == 403 {
                        return Err(anyhow::anyhow!("Authentication failed for: {}", url));
                    } else {
                        let error = anyhow::anyhow!("HTTP {} from {}", status, url);
                        warn!("Request failed (attempt {}): {}", attempt, error);
                        last_error = Some(error);
                    }
                }
                Err(e) => {
                    let error = anyhow::anyhow!("Request error: {}", e);
                    warn!("Request failed (attempt {}): {}", attempt, error);
                    last_error = Some(error);
                }
            }

            // Wait before retry (exponential backoff)
            if attempt < self.retry_attempts {
                let delay = Duration::from_millis(100 * (1 << (attempt - 1)));
                debug!("Waiting {:?} before retry", delay);
                tokio::time::sleep(delay).await;
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("All retry attempts failed")))
    }
    
    /// Parse JSON response
    async fn parse_response<T>(&self, response: Response) -> Result<T>
    where
        T: DeserializeOwned,
    {
        let text = response.text().await
            .context("Failed to read response body")?;
        
        serde_json::from_str(&text)
            .context("Failed to parse JSON response")
    }
    
    /// Download a file (like a tarball) with retry logic
    pub async fn download(&self, url: &str) -> Result<bytes::Bytes> {
        debug!("Downloading file from: {}", url);

        // Retry logic for downloads
        let mut last_error = None;
        for attempt in 1..=self.retry_attempts {
            let mut request = self.client.get(url);

            // Add authentication if available
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }

            match request.send().await {
                Ok(response) => {
                    let status = response.status();
                    if status.is_success() {
                        return response.bytes().await
                            .context("Failed to read download content");
                    } else {
                        let error = anyhow::anyhow!("Download failed with status: {}", status);
                        warn!("Download failed (attempt {}): {}", attempt, error);
                        last_error = Some(error);
                    }
                }
                Err(e) => {
                    let error = anyhow::anyhow!("Download error: {}", e);
                    warn!("Download failed (attempt {}): {}", attempt, error);
                    last_error = Some(error);
                }
            }

            // Wait before retry
            if attempt < self.retry_attempts {
                let delay = Duration::from_millis(200 * attempt as u64);
                debug!("Waiting {:?} before download retry", delay);
                tokio::time::sleep(delay).await;
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("All download attempts failed")))
    }
}
