# Performance Test Script for nx vs npm
# Tests installation speed and compares performance

Write-Host "🚀 nx Package Manager Performance Test" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Test packages
$testPackages = @("lodash", "axios", "express", "react", "vue")

# Results storage
$results = @()

function Test-PackageInstaller {
    param(
        [string]$installer,
        [string]$package,
        [string]$command
    )
    
    Write-Host "`n📦 Testing $installer with $package..." -ForegroundColor Yellow
    
    # Clean up
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
    }
    if (Test-Path "package-lock.json") {
        Remove-Item package-lock.json -ErrorAction SilentlyContinue
    }
    
    # Measure installation time
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        Invoke-Expression $command | Out-Null
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        Write-Host "✅ $installer installed $package in $([math]::Round($duration, 3))s" -ForegroundColor Green
        
        return @{
            Installer = $installer
            Package = $package
            Duration = $duration
            Success = $true
        }
    }
    catch {
        $stopwatch.Stop()
        Write-Host "❌ $installer failed to install $package" -ForegroundColor Red
        
        return @{
            Installer = $installer
            Package = $package
            Duration = -1
            Success = $false
        }
    }
}

# Test each package with both installers
foreach ($package in $testPackages) {
    # Test nx
    $nxResult = Test-PackageInstaller -installer "nx" -package $package -command ".\target\release\nx.exe install $package"
    $results += $nxResult
    
    Start-Sleep -Seconds 2
    
    # Test npm (if available)
    if (Get-Command npm -ErrorAction SilentlyContinue) {
        $npmResult = Test-PackageInstaller -installer "npm" -package $package -command "npm install $package --silent"
        $results += $npmResult
    }
    
    Start-Sleep -Seconds 2
}

# Display results
Write-Host "`n📊 Performance Results" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

$nxResults = $results | Where-Object { $_.Installer -eq "nx" -and $_.Success }
$npmResults = $results | Where-Object { $_.Installer -eq "npm" -and $_.Success }

if ($nxResults.Count -gt 0) {
    $nxAverage = ($nxResults | Measure-Object -Property Duration -Average).Average
    Write-Host "nx average: $([math]::Round($nxAverage, 3))s" -ForegroundColor Green
}

if ($npmResults.Count -gt 0) {
    $npmAverage = ($npmResults | Measure-Object -Property Duration -Average).Average
    Write-Host "npm average: $([math]::Round($npmAverage, 3))s" -ForegroundColor Blue
    
    if ($nxResults.Count -gt 0) {
        $speedup = $npmAverage / $nxAverage
        Write-Host "🚀 nx is $([math]::Round($speedup, 1))x faster than npm!" -ForegroundColor Magenta
    }
}

# Detailed results table
Write-Host "`n📋 Detailed Results:" -ForegroundColor Cyan
$results | Format-Table -AutoSize

Write-Host "`n✨ Performance test completed!" -ForegroundColor Green
