# Contributing to nx

Thank you for your interest in contributing to nx! This document provides guidelines for contributing to the project.

## Getting Started

### Prerequisites

- Rust 1.75 or later
- Git
- Node.js (for testing npm compatibility)

### Setting up the Development Environment

1. Clone the repository:
   ```bash
   git clone https://github.com/nx-package-manager/nx.git
   cd nx
   ```

2. Build the project:
   ```bash
   cargo build
   ```

3. Run tests:
   ```bash
   cargo test
   ```

4. Run the development version:
   ```bash
   cargo run -- --help
   ```

## Development Guidelines

### Code Style

- Follow Rust standard formatting with `cargo fmt`
- Use `cargo clippy` to catch common mistakes
- Write clear, self-documenting code
- Add comments for complex logic

### Testing

- Write unit tests for new functionality
- Test npm compatibility thoroughly
- Run performance benchmarks for changes affecting speed
- Test on multiple platforms (Windows, macOS, Linux)

### Performance

- nx is designed for ultra-fast performance
- Profile changes that might affect installation speed
- Maintain sub-3 second installation times for most packages
- Optimize for concurrent downloads and memory usage

## Submitting Changes

### Pull Request Process

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `cargo test`
6. Format code: `cargo fmt`
7. Check with clippy: `cargo clippy`
8. Commit your changes: `git commit -m 'Add amazing feature'`
9. Push to your fork: `git push origin feature/amazing-feature`
10. Open a Pull Request

### Commit Messages

- Use clear, descriptive commit messages
- Start with a verb in present tense
- Keep the first line under 50 characters
- Add detailed description if needed

Example:
```
Add zero-copy cache operations for faster installs

Implement memory-mapped file access for cached packages,
reducing installation time by 40% for cache hits.
```

## Reporting Issues

### Bug Reports

When reporting bugs, please include:

- nx version (`nx --version`)
- Operating system and version
- Node.js version (if relevant)
- Steps to reproduce the issue
- Expected vs actual behavior
- Error messages or logs

### Feature Requests

For feature requests, please:

- Describe the use case
- Explain why it would be valuable
- Consider npm compatibility implications
- Suggest implementation approach if possible

## Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain a welcoming environment

## Questions?

- Open an issue for questions about contributing
- Check existing issues and discussions first
- Be patient and respectful when asking for help

Thank you for contributing to nx! 🚀
