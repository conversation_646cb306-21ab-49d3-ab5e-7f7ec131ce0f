use anyhow::{anyhow, Result};
use semver::{Version, VersionReq};
use std::cmp::Ordering;

/// Ultra-fast semantic version utilities
pub struct SemverUtils;

impl SemverUtils {
    /// Parse a version string into a semver Version
    pub fn parse_version(version: &str) -> Result<Version> {
        // Clean up common version prefixes
        let clean_version = version.trim_start_matches('v').trim_start_matches('=');
        
        Version::parse(clean_version)
            .map_err(|e| anyhow!("Invalid version '{}': {}", version, e))
    }

    /// Parse a version requirement string
    pub fn parse_requirement(req: &str) -> Result<VersionReq> {
        // Handle special cases
        match req.trim() {
            "latest" | "*" => return Ok(VersionReq::parse("*")?),
            "" => return Ok(VersionReq::parse("*")?),
            _ => {}
        }

        VersionReq::parse(req)
            .map_err(|e| anyhow!("Invalid version requirement '{}': {}", req, e))
    }

    /// Find the best matching version from a list
    pub fn find_best_match(versions: &[String], requirement: &str) -> Result<Option<String>> {
        if versions.is_empty() {
            return Ok(None);
        }

        let req = Self::parse_requirement(requirement)?;
        
        // Parse and filter matching versions
        let mut matching_versions: Vec<(Version, String)> = versions
            .iter()
            .filter_map(|v| {
                Self::parse_version(v).ok().map(|parsed| (parsed, v.clone()))
            })
            .filter(|(version, _)| req.matches(version))
            .collect();

        if matching_versions.is_empty() {
            return Ok(None);
        }

        // Sort by version (descending - highest first)
        matching_versions.sort_by(|a, b| b.0.cmp(&a.0));

        Ok(Some(matching_versions[0].1.clone()))
    }

    /// Get the latest version from a list
    pub fn get_latest_version(versions: &[String]) -> Option<String> {
        let mut parsed_versions: Vec<(Version, String)> = versions
            .iter()
            .filter_map(|v| {
                Self::parse_version(v).ok().map(|parsed| (parsed, v.clone()))
            })
            .collect();

        if parsed_versions.is_empty() {
            return None;
        }

        parsed_versions.sort_by(|a, b| b.0.cmp(&a.0));
        Some(parsed_versions[0].1.clone())
    }

    /// Check if a version satisfies a requirement
    pub fn satisfies(version: &str, requirement: &str) -> Result<bool> {
        let version = Self::parse_version(version)?;
        let req = Self::parse_requirement(requirement)?;
        Ok(req.matches(&version))
    }

    /// Compare two versions
    pub fn compare_versions(a: &str, b: &str) -> Result<Ordering> {
        let version_a = Self::parse_version(a)?;
        let version_b = Self::parse_version(b)?;
        Ok(version_a.cmp(&version_b))
    }

    /// Generate a caret range (^1.2.3 -> >=1.2.3 <2.0.0)
    pub fn to_caret_range(version: &str) -> Result<String> {
        let v = Self::parse_version(version)?;
        Ok(format!("^{}", v))
    }

    /// Generate a tilde range (~1.2.3 -> >=1.2.3 <1.3.0)
    pub fn to_tilde_range(version: &str) -> Result<String> {
        let v = Self::parse_version(version)?;
        Ok(format!("~{}", v))
    }

    /// Check if a version is a prerelease
    pub fn is_prerelease(version: &str) -> Result<bool> {
        let v = Self::parse_version(version)?;
        Ok(!v.pre.is_empty())
    }

    /// Get the major version number
    pub fn get_major(version: &str) -> Result<u64> {
        let v = Self::parse_version(version)?;
        Ok(v.major)
    }

    /// Get the minor version number
    pub fn get_minor(version: &str) -> Result<u64> {
        let v = Self::parse_version(version)?;
        Ok(v.minor)
    }

    /// Get the patch version number
    pub fn get_patch(version: &str) -> Result<u64> {
        let v = Self::parse_version(version)?;
        Ok(v.patch)
    }

    /// Increment major version
    pub fn increment_major(version: &str) -> Result<String> {
        let mut v = Self::parse_version(version)?;
        v.major += 1;
        v.minor = 0;
        v.patch = 0;
        v.pre = semver::Prerelease::EMPTY;
        v.build = semver::BuildMetadata::EMPTY;
        Ok(v.to_string())
    }

    /// Increment minor version
    pub fn increment_minor(version: &str) -> Result<String> {
        let mut v = Self::parse_version(version)?;
        v.minor += 1;
        v.patch = 0;
        v.pre = semver::Prerelease::EMPTY;
        v.build = semver::BuildMetadata::EMPTY;
        Ok(v.to_string())
    }

    /// Increment patch version
    pub fn increment_patch(version: &str) -> Result<String> {
        let mut v = Self::parse_version(version)?;
        v.patch += 1;
        v.pre = semver::Prerelease::EMPTY;
        v.build = semver::BuildMetadata::EMPTY;
        Ok(v.to_string())
    }

    /// Resolve version conflicts by finding a compatible version
    pub fn resolve_conflict(versions: &[String], requirements: &[String]) -> Result<Option<String>> {
        if requirements.is_empty() {
            return Self::get_latest_version(versions).map(Some).ok_or_else(|| anyhow!("No versions available"));
        }

        // Parse all requirements
        let reqs: Result<Vec<VersionReq>> = requirements
            .iter()
            .map(|r| Self::parse_requirement(r))
            .collect();
        let reqs = reqs?;

        // Find versions that satisfy all requirements
        let mut compatible_versions: Vec<(Version, String)> = versions
            .iter()
            .filter_map(|v| {
                Self::parse_version(v).ok().map(|parsed| (parsed, v.clone()))
            })
            .filter(|(version, _)| {
                reqs.iter().all(|req| req.matches(version))
            })
            .collect();

        if compatible_versions.is_empty() {
            return Ok(None);
        }

        // Sort by version (descending - highest first)
        compatible_versions.sort_by(|a, b| b.0.cmp(&a.0));

        Ok(Some(compatible_versions[0].1.clone()))
    }

    /// Check if two version ranges overlap
    pub fn ranges_overlap(req1: &str, req2: &str) -> Result<bool> {
        let r1 = Self::parse_requirement(req1)?;
        let r2 = Self::parse_requirement(req2)?;
        
        // This is a simplified check - in practice, you'd need more sophisticated logic
        // For now, we'll assume they overlap if they're not mutually exclusive
        
        // Generate some test versions to check overlap
        let test_versions = vec![
            "0.0.1", "0.1.0", "0.9.9", "1.0.0", "1.0.1", "1.1.0", "1.9.9",
            "2.0.0", "2.1.0", "3.0.0", "10.0.0", "100.0.0"
        ];
        
        for version_str in &test_versions {
            if let Ok(version) = Self::parse_version(version_str) {
                if r1.matches(&version) && r2.matches(&version) {
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }

    /// Normalize a version string (remove prefixes, clean up format)
    pub fn normalize_version(version: &str) -> Result<String> {
        let v = Self::parse_version(version)?;
        Ok(v.to_string())
    }

    /// Check if a version string is valid
    pub fn is_valid_version(version: &str) -> bool {
        Self::parse_version(version).is_ok()
    }

    /// Check if a requirement string is valid
    pub fn is_valid_requirement(requirement: &str) -> bool {
        Self::parse_requirement(requirement).is_ok()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_version() {
        assert!(SemverUtils::parse_version("1.2.3").is_ok());
        assert!(SemverUtils::parse_version("v1.2.3").is_ok());
        assert!(SemverUtils::parse_version("=1.2.3").is_ok());
        assert!(SemverUtils::parse_version("invalid").is_err());
    }

    #[test]
    fn test_find_best_match() {
        let versions = vec![
            "1.0.0".to_string(),
            "1.1.0".to_string(),
            "1.2.0".to_string(),
            "2.0.0".to_string(),
        ];

        let result = SemverUtils::find_best_match(&versions, "^1.0.0").unwrap();
        assert_eq!(result, Some("1.2.0".to_string()));

        let result = SemverUtils::find_best_match(&versions, "~1.1.0").unwrap();
        assert_eq!(result, Some("1.1.0".to_string()));
    }

    #[test]
    fn test_compare_versions() {
        assert_eq!(
            SemverUtils::compare_versions("1.0.0", "2.0.0").unwrap(),
            Ordering::Less
        );
        assert_eq!(
            SemverUtils::compare_versions("2.0.0", "1.0.0").unwrap(),
            Ordering::Greater
        );
        assert_eq!(
            SemverUtils::compare_versions("1.0.0", "1.0.0").unwrap(),
            Ordering::Equal
        );
    }

    #[test]
    fn test_satisfies() {
        assert!(SemverUtils::satisfies("1.2.3", "^1.0.0").unwrap());
        assert!(SemverUtils::satisfies("1.1.0", "~1.1.0").unwrap());
        assert!(!SemverUtils::satisfies("2.0.0", "^1.0.0").unwrap());
    }

    #[test]
    fn test_increment_versions() {
        assert_eq!(SemverUtils::increment_major("1.2.3").unwrap(), "2.0.0");
        assert_eq!(SemverUtils::increment_minor("1.2.3").unwrap(), "1.3.0");
        assert_eq!(SemverUtils::increment_patch("1.2.3").unwrap(), "1.2.4");
    }

    #[test]
    fn test_resolve_conflict() {
        let versions = vec![
            "1.0.0".to_string(),
            "1.1.0".to_string(),
            "1.2.0".to_string(),
            "2.0.0".to_string(),
        ];
        let requirements = vec!["^1.0.0".to_string(), "~1.2.0".to_string()];

        let result = SemverUtils::resolve_conflict(&versions, &requirements).unwrap();
        assert_eq!(result, Some("1.2.0".to_string()));
    }
}
