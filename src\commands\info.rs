use anyhow::{anyhow, Result};
use colored::*;
use std::sync::Arc;

use crate::cli::InfoArgs;
use crate::types::RegistryConfig;
use crate::ui::{PackageInfo, UI};
use crate::utils::registry::{RegistryClient, DownloadStats};
use crate::utils::format_bytes;

/// Execute nx info command
pub async fn execute(args: InfoArgs, ui: &UI) -> Result<()> {
    ui.step(&format!("📋 Getting information for package: {}", args.package));

    let registry_config = RegistryConfig::default();
    let registry = Arc::new(RegistryClient::new(registry_config)?);

    // Get package metadata
    let metadata = registry.get_package_metadata(&args.package).await?;

    if args.versions {
        show_all_versions(&metadata, ui)?;
        return Ok(());
    }

    let version = args.version.as_deref().unwrap_or(&metadata.latest);
    let version_metadata = metadata.versions.get(version)
        .ok_or_else(|| anyhow!("Version {} not found for package {}", version, args.package))?;

    // Get download statistics
    let download_stats = registry.get_download_stats(&args.package, "last-month")
        .await
        .unwrap_or(DownloadStats {
            downloads: 0,
            period: "last-month".to_string(),
            package: args.package.clone(),
        });

    // Create package info
    let package_info = PackageInfo {
        name: metadata.name.clone(),
        version: version.to_string(),
        dependencies: version_metadata.dependencies.len(),
        size: format_bytes(version_metadata.dist.unpacked_size.unwrap_or(0)),
        license: version_metadata.license.clone().unwrap_or_else(|| "Unknown".to_string()),
        description: version_metadata.description.clone(),
    };

    // Display package information
    ui.show_package_info(&package_info);

    // Show additional details
    println!();
    show_detailed_info(&metadata, version_metadata, &download_stats, &args, ui)?;

    if args.dependencies {
        show_dependencies(version_metadata, ui)?;
    }

    Ok(())
}

fn show_all_versions(metadata: &crate::types::PackageMetadata, _ui: &UI) -> Result<()> {
    let mut versions: Vec<_> = metadata.versions.keys().collect();
    versions.sort_by(|a, b| {
        use crate::utils::semver::SemverUtils;
        match (SemverUtils::parse_version(a), SemverUtils::parse_version(b)) {
            (Ok(va), Ok(vb)) => vb.cmp(&va), // Descending order
            _ => b.cmp(a),
        }
    });

    println!("\n📦 Available versions for {}:", metadata.name);
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ {} │", "All Versions".bright_cyan().bold());
    println!("├─────────────────────────────────────────────────────────────┤");

    for (i, version) in versions.iter().enumerate() {
        let is_latest = version == &&metadata.latest;
        let version_display = if is_latest {
            format!("{} (latest)", version).bright_green().bold()
        } else {
            version.bright_white()
        };

        println!("│ {:<59} │", version_display);
        
        if i >= 19 && versions.len() > 20 {
            println!("│ {:<59} │", format!("... and {} more versions", versions.len() - 20).bright_yellow());
            break;
        }
    }

    println!("└─────────────────────────────────────────────────────────────┘");
    Ok(())
}

fn show_detailed_info(
    metadata: &crate::types::PackageMetadata,
    version_metadata: &crate::types::VersionMetadata,
    download_stats: &DownloadStats,
    _args: &InfoArgs,
    _ui: &UI,
) -> Result<()> {
    use colored::*;

    println!("📊 {}", "Package Statistics".bright_cyan().bold());
    println!("┌─────────────────────┬───────────────────────────────────────┤");
    
    // Downloads
    println!("│ {} │ {:<37} │", 
             "Downloads (month)".bright_white().bold(), 
             format!("{}", download_stats.downloads).bright_green());

    // File count
    if let Some(file_count) = version_metadata.dist.file_count {
        println!("│ {} │ {:<37} │", 
                 "Files".bright_white().bold(), 
                 format!("{}", file_count).bright_blue());
    }

    // Unpacked size
    if let Some(size) = version_metadata.dist.unpacked_size {
        println!("│ {} │ {:<37} │", 
                 "Unpacked Size".bright_white().bold(), 
                 format_bytes(size).bright_yellow());
    }

    // Dependencies count
    let total_deps = version_metadata.dependencies.len() + 
                    version_metadata.dev_dependencies.len() + 
                    version_metadata.peer_dependencies.len() + 
                    version_metadata.optional_dependencies.len();
    
    println!("│ {} │ {:<37} │", 
             "Total Dependencies".bright_white().bold(), 
             format!("{}", total_deps).bright_magenta());

    println!("└─────────────────────┴───────────────────────────────────────┘");

    // Repository information
    if let Some(repository) = &version_metadata.repository {
        println!("\n🔗 {}", "Repository".bright_cyan().bold());
        println!("┌─────────────────────┬───────────────────────────────────────┤");
        println!("│ {} │ {:<37} │", 
                 "URL".bright_white().bold(), 
                 repository.url.bright_blue());
        if let Some(repo_type) = &repository.repo_type {
            println!("│ {} │ {:<37} │", 
                     "Type".bright_white().bold(), 
                     repo_type.bright_green());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    // Maintainers
    if let Some(maintainers) = &metadata.maintainers {
        if !maintainers.is_empty() {
            println!("\n👥 {}", "Maintainers".bright_cyan().bold());
            println!("┌─────────────────────────────────────────────────────────────┐");
            for maintainer in maintainers.iter().take(5) {
                let display = if let Some(email) = &maintainer.email {
                    format!("{} <{}>", maintainer.name, email)
                } else {
                    maintainer.name.clone()
                };
                println!("│ {:<59} │", display.bright_white());
            }
            if maintainers.len() > 5 {
                println!("│ {:<59} │", format!("... and {} more", maintainers.len() - 5).bright_yellow());
            }
            println!("└─────────────────────────────────────────────────────────────┘");
        }
    }

    // Keywords
    if let Some(keywords) = &version_metadata.keywords {
        if !keywords.is_empty() {
            println!("\n🏷️  {}", "Keywords".bright_cyan().bold());
            println!("┌─────────────────────────────────────────────────────────────┐");
            let keywords_str = keywords.join(", ");
            // Wrap keywords if too long
            let wrapped = if keywords_str.len() > 57 {
                format!("{}...", &keywords_str[..54])
            } else {
                keywords_str
            };
            println!("│ {:<59} │", wrapped.bright_white());
            println!("└─────────────────────────────────────────────────────────────┘");
        }
    }

    // Engines
    if let Some(engines) = &version_metadata.engines {
        if !engines.is_empty() {
            println!("\n⚙️  {}", "Engines".bright_cyan().bold());
            println!("┌─────────────────────┬───────────────────────────────────────┤");
            for (engine, version) in engines {
                println!("│ {} │ {:<37} │", 
                         format!("{}", engine).bright_white().bold(), 
                         version.bright_green());
            }
            println!("└─────────────────────┴───────────────────────────────────────┘");
        }
    }

    // Binaries
    if let Some(bin) = &version_metadata.bin {
        if !bin.is_empty() {
            println!("\n🔧 {}", "Binaries".bright_cyan().bold());
            println!("┌─────────────────────┬───────────────────────────────────────┤");
            for (name, path) in bin {
                println!("│ {} │ {:<37} │", 
                         name.bright_white().bold(), 
                         path.bright_blue());
            }
            println!("└─────────────────────┴───────────────────────────────────────┘");
        }
    }

    Ok(())
}

fn show_dependencies(version_metadata: &crate::types::VersionMetadata, _ui: &UI) -> Result<()> {
    use colored::*;

    println!("\n📦 {}", "Dependencies".bright_cyan().bold());

    // Regular dependencies
    if !version_metadata.dependencies.is_empty() {
        println!("\n🔗 {} ({})", "Dependencies".bright_green().bold(), version_metadata.dependencies.len());
        println!("┌─────────────────────┬───────────────────────────────────────┤");
        for (name, version) in &version_metadata.dependencies {
            println!("│ {} │ {:<37} │", 
                     format!("{}", name).bright_white().bold(), 
                     version.bright_blue());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    // Dev dependencies
    if !version_metadata.dev_dependencies.is_empty() {
        println!("\n🛠️  {} ({})", "Dev Dependencies".bright_yellow().bold(), version_metadata.dev_dependencies.len());
        println!("┌─────────────────────┬───────────────────────────────────────┤");
        for (name, version) in &version_metadata.dev_dependencies {
            println!("│ {} │ {:<37} │", 
                     format!("{}", name).bright_white().bold(), 
                     version.bright_blue());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    // Peer dependencies
    if !version_metadata.peer_dependencies.is_empty() {
        println!("\n🤝 {} ({})", "Peer Dependencies".bright_magenta().bold(), version_metadata.peer_dependencies.len());
        println!("┌─────────────────────┬───────────────────────────────────────┤");
        for (name, version) in &version_metadata.peer_dependencies {
            println!("│ {} │ {:<37} │", 
                     format!("{}", name).bright_white().bold(), 
                     version.bright_blue());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    // Optional dependencies
    if !version_metadata.optional_dependencies.is_empty() {
        println!("\n🔧 {} ({})", "Optional Dependencies".bright_cyan().bold(), version_metadata.optional_dependencies.len());
        println!("┌─────────────────────┬───────────────────────────────────────┤");
        for (name, version) in &version_metadata.optional_dependencies {
            println!("│ {} │ {:<37} │", 
                     format!("{}", name).bright_white().bold(), 
                     version.bright_blue());
        }
        println!("└─────────────────────┴───────────────────────────────────────┘");
    }

    if version_metadata.dependencies.is_empty() && 
       version_metadata.dev_dependencies.is_empty() && 
       version_metadata.peer_dependencies.is_empty() && 
       version_metadata.optional_dependencies.is_empty() {
        println!("No dependencies found.");
    }

    Ok(())
}
