use anyhow::{anyhow, Result};
use blake3::Hasher;
use lru::LruCache;
use memmap2::Mmap;
use std::collections::HashMap;
use std::fs::{File, OpenOptions};
use std::io::Write;
use std::num::NonZeroUsize;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::fs;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::types::{CacheEntry, CacheMetadata};

pub struct UltraFastCache {
    cache_dir: PathBuf,
    memory_cache: Arc<RwLock<LruCache<String, Arc<Vec<u8>>>>>,
    metadata_cache: Arc<RwLock<LruCache<String, CacheMetadata>>>,
    mmap_cache: Arc<RwLock<HashMap<String, Arc<Mmap>>>>,
    max_memory_size: usize,
    max_disk_size: u64,
}

impl UltraFastCache {
    pub fn new(cache_dir: PathBuf) -> Result<Self> {
        std::fs::create_dir_all(&cache_dir)?;
        
        Ok(Self {
            cache_dir,
            memory_cache: Arc::new(RwLock::new(LruCache::new(
                NonZeroUsize::new(1000).unwrap()
            ))),
            metadata_cache: Arc::new(RwLock::new(LruCache::new(
                NonZeroUsize::new(10000).unwrap()
            ))),
            mmap_cache: Arc::new(RwLock::new(HashMap::new())),
            max_memory_size: 512 * 1024 * 1024, // 512MB
            max_disk_size: 5 * 1024 * 1024 * 1024, // 5GB
        })
    }

    /// Ultra-fast package storage with memory mapping
    pub async fn store_package(&self, key: &str, data: &[u8]) -> Result<()> {
        let hash = self.compute_hash(data);
        let file_path = self.get_cache_path(key);
        
        // Create directory if needed
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent).await?;
        }

        // Write to disk
        fs::write(&file_path, data).await?;

        // Store metadata
        let metadata = CacheMetadata {
            created_at: chrono::Utc::now(),
            accessed_at: chrono::Utc::now(),
            size: data.len() as u64,
            hash,
        };

        {
            let mut cache = self.metadata_cache.write().await;
            cache.put(key.to_string(), metadata);
        }

        // Store in memory cache if small enough
        if data.len() <= 1024 * 1024 { // 1MB threshold
            let mut memory_cache = self.memory_cache.write().await;
            memory_cache.put(key.to_string(), Arc::new(data.to_vec()));
        }

        debug!("Cached package {} ({} bytes)", key, data.len());
        Ok(())
    }

    /// Ultra-fast package retrieval with memory mapping
    pub async fn get_package(&self, key: &str) -> Result<Vec<u8>> {
        // Check memory cache first (fastest)
        {
            let mut memory_cache = self.memory_cache.write().await;
            if let Some(data) = memory_cache.get(key) {
                debug!("Memory cache hit for {}", key);
                return Ok((**data).clone());
            }
        }

        // Check memory-mapped cache
        {
            let mmap_cache = self.mmap_cache.read().await;
            if let Some(mmap) = mmap_cache.get(key) {
                debug!("Memory-mapped cache hit for {}", key);
                return Ok(mmap.as_ref().to_vec());
            }
        }

        // Load from disk with memory mapping
        let file_path = self.get_cache_path(key);
        if !file_path.exists() {
            return Err(anyhow!("Package {} not found in cache", key));
        }

        let file = File::open(&file_path)?;
        let mmap = unsafe { Mmap::map(&file)? };
        let data = mmap.as_ref().to_vec();

        // Cache the memory map for future use
        {
            let mut mmap_cache = self.mmap_cache.write().await;
            mmap_cache.insert(key.to_string(), Arc::new(mmap));
        }

        // Update access time
        {
            let mut metadata_cache = self.metadata_cache.write().await;
            if let Some(metadata) = metadata_cache.get_mut(key) {
                metadata.accessed_at = chrono::Utc::now();
            }
        }

        debug!("Disk cache hit for {} ({} bytes)", key, data.len());
        Ok(data)
    }

    /// Check if package exists in cache
    pub async fn has_package(&self, key: &str) -> bool {
        // Check memory cache
        {
            let memory_cache = self.memory_cache.read().await;
            if memory_cache.contains(key) {
                return true;
            }
        }

        // Check disk cache
        let file_path = self.get_cache_path(key);
        file_path.exists()
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        let memory_cache = self.memory_cache.read().await;
        let metadata_cache = self.metadata_cache.read().await;
        let mmap_cache = self.mmap_cache.read().await;

        let memory_size = memory_cache.len();
        let disk_entries = metadata_cache.len();
        let mmap_entries = mmap_cache.len();

        let total_disk_size = self.calculate_disk_usage().await.unwrap_or(0);

        CacheStats {
            memory_entries: memory_size,
            disk_entries,
            mmap_entries,
            total_disk_size,
            hit_rate: 0.0, // TODO: Implement hit rate tracking
        }
    }

    /// Clear all cache data
    pub async fn clear(&self) -> Result<u64> {
        let mut cleared_size = 0u64;

        // Clear memory caches
        {
            let mut memory_cache = self.memory_cache.write().await;
            memory_cache.clear();
        }
        {
            let mut metadata_cache = self.metadata_cache.write().await;
            metadata_cache.clear();
        }
        {
            let mut mmap_cache = self.mmap_cache.write().await;
            mmap_cache.clear();
        }

        // Clear disk cache
        if self.cache_dir.exists() {
            cleared_size = self.calculate_disk_usage().await.unwrap_or(0);
            fs::remove_dir_all(&self.cache_dir).await?;
            fs::create_dir_all(&self.cache_dir).await?;
        }

        info!("Cleared cache ({} bytes)", cleared_size);
        Ok(cleared_size)
    }

    /// Clean up old cache entries (LRU eviction)
    pub async fn cleanup(&self) -> Result<u64> {
        let mut cleaned_size = 0u64;
        let cutoff_time = SystemTime::now() - Duration::from_secs(30 * 24 * 60 * 60); // 30 days

        // Get all cache files
        let mut entries = Vec::new();
        self.collect_cache_entries(&self.cache_dir, &mut entries).await?;

        // Sort by access time
        entries.sort_by_key(|entry| entry.metadata.accessed_at);

        // Remove old entries
        for entry in entries {
            if entry.metadata.accessed_at.timestamp() < cutoff_time.duration_since(UNIX_EPOCH)?.as_secs() as i64 {
                let file_path = self.get_cache_path(&entry.key);
                if file_path.exists() {
                    cleaned_size += entry.metadata.size;
                    fs::remove_file(&file_path).await?;
                }
            }
        }

        info!("Cleaned up {} bytes from cache", cleaned_size);
        Ok(cleaned_size)
    }

    /// Verify cache integrity
    pub async fn verify(&self) -> Result<VerificationResult> {
        let mut total_files = 0;
        let mut corrupted_files = 0;
        let mut total_size = 0;

        let metadata_cache = self.metadata_cache.read().await;
        
        for (key, metadata) in metadata_cache.iter() {
            total_files += 1;
            total_size += metadata.size;

            let file_path = self.get_cache_path(key);
            if file_path.exists() {
                if let Ok(data) = fs::read(&file_path).await {
                    let computed_hash = self.compute_hash(&data);
                    if computed_hash != metadata.hash {
                        corrupted_files += 1;
                        warn!("Corrupted cache file: {}", key);
                    }
                } else {
                    corrupted_files += 1;
                }
            } else {
                corrupted_files += 1;
            }
        }

        Ok(VerificationResult {
            total_files,
            corrupted_files,
            total_size,
        })
    }

    fn get_cache_path(&self, key: &str) -> PathBuf {
        let hash = blake3::hash(key.as_bytes());
        let hash_str = hex::encode(hash.as_bytes());
        
        // Create subdirectories based on hash prefix for better filesystem performance
        let subdir1 = &hash_str[0..2];
        let subdir2 = &hash_str[2..4];
        
        self.cache_dir
            .join("packages")
            .join(subdir1)
            .join(subdir2)
            .join(format!("{}.tgz", hash_str))
    }

    fn compute_hash(&self, data: &[u8]) -> String {
        let mut hasher = Hasher::new();
        hasher.update(data);
        hex::encode(hasher.finalize().as_bytes())
    }

    async fn calculate_disk_usage(&self) -> Result<u64> {
        let mut total_size = 0u64;
        let mut entries = Vec::new();
        self.collect_cache_entries(&self.cache_dir, &mut entries).await?;
        
        for entry in entries {
            total_size += entry.metadata.size;
        }
        
        Ok(total_size)
    }

    fn collect_cache_entries<'a>(&'a self, dir: &'a Path, entries: &'a mut Vec<CacheEntry>) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<()>> + 'a>> {
        Box::pin(async move {
        let mut read_dir = fs::read_dir(dir).await?;
        
        while let Some(entry) = read_dir.next_entry().await? {
            let path = entry.path();
            
            if path.is_dir() {
                self.collect_cache_entries(&path, entries).await?;
            } else if path.extension().and_then(|s| s.to_str()) == Some("tgz") {
                if let Ok(metadata) = entry.metadata().await {
                    let cache_entry = CacheEntry {
                        key: path.file_stem()
                            .and_then(|s| s.to_str())
                            .unwrap_or("")
                            .to_string(),
                        data: Vec::new(), // Don't load data for collection
                        metadata: CacheMetadata {
                            created_at: chrono::DateTime::from(metadata.created().unwrap_or(SystemTime::now())),
                            accessed_at: chrono::DateTime::from(metadata.accessed().unwrap_or(SystemTime::now())),
                            size: metadata.len(),
                            hash: String::new(),
                        },
                    };
                    entries.push(cache_entry);
                }
            }
        }

        Ok(())
        })
    }
}

#[derive(Debug)]
pub struct CacheStats {
    pub memory_entries: usize,
    pub disk_entries: usize,
    pub mmap_entries: usize,
    pub total_disk_size: u64,
    pub hit_rate: f64,
}

#[derive(Debug)]
pub struct VerificationResult {
    pub total_files: usize,
    pub corrupted_files: usize,
    pub total_size: u64,
}
