# Multi-stage build for nx package manager
FROM rust:1.75-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Cargo files
COPY Cargo.toml Cargo.lock ./

# Copy source code
COPY src ./src

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN useradd -r -s /bin/false nx

# Copy binary from builder stage
COPY --from=builder /app/target/release/nx /usr/local/bin/nx

# Set permissions
RUN chmod +x /usr/local/bin/nx

# Switch to non-root user
USER nx

# Set working directory
WORKDIR /workspace

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD nx --version || exit 1

# Set entrypoint
ENTRYPOINT ["nx"]

# Default command
CMD ["--help"]

# Labels
LABEL org.opencontainers.image.title="nx"
LABEL org.opencontainers.image.description="Ultra-fast npm package manager written in Rust"
LABEL org.opencontainers.image.source="https://github.com/nx-package-manager/nx"
LABEL org.opencontainers.image.licenses="MIT"
