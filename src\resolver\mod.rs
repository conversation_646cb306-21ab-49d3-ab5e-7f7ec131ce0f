pub mod graph;
pub mod semver;
pub mod types;
pub mod engine;
pub mod version;

use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use futures::stream::{self, StreamExt};
use tracing::{info, debug, warn};

pub use graph::*;
pub use semver::*;
pub use types::*;
pub use engine::*;
pub use version::*;

/// Ultra-fast dependency resolver with aggressive caching and parallel processing
#[derive(Debug)]
pub struct Resolver {
    registry: crate::registry::Registry,
    metadata_cache: Arc<RwLock<HashMap<String, crate::registry::PackageMetadata>>>,
}

impl Resolver {
    pub fn new(registry: crate::registry::Registry) -> Self {
        Self {
            registry,
            metadata_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Ultra-fast dependency resolution with aggressive caching and batching
    pub async fn resolve_dependencies(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<DependencyGraph> {
        let start_time = std::time::Instant::now();
        info!("🚀 Ultra-fast resolving {} dependencies", dependencies.len());

        let mut graph = DependencyGraph::new();
        
        // Fast-path: if no dependencies, return immediately
        if dependencies.is_empty() {
            debug!("No dependencies to resolve");
            return Ok(graph);
        }

        // Batch resolve all dependencies in parallel with aggressive concurrency
        let resolved_packages = self.batch_resolve_packages(dependencies).await?;
        
        // Build dependency graph from resolved packages
        for (name, version_spec) in dependencies {
            if let Some(resolved_package) = resolved_packages.get(name) {
                let node = DependencyNode {
                    name: name.clone(),
                    version: resolved_package.version.clone(),
                    dependencies: resolved_package.dependencies.clone(),
                    dev_dependencies: resolved_package.dev_dependencies.clone(),
                    peer_dependencies: resolved_package.peer_dependencies.clone(),
                    optional_dependencies: resolved_package.optional_dependencies.clone(),
                };
                
                graph.add_node(node);
            } else {
                warn!("Failed to resolve package: {}", name);
            }
        }

        let duration = start_time.elapsed();
        info!("✅ Resolved {} dependencies in {:.2}s", dependencies.len(), duration.as_secs_f64());
        
        Ok(graph)
    }
    
    /// Batch resolve packages with ultra-fast parallel processing
    async fn batch_resolve_packages(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<HashMap<String, ResolvedPackage>> {
        let mut resolved = HashMap::new();
        
        // Create futures for all packages with maximum concurrency
        let package_futures: Vec<_> = dependencies.iter()
            .map(|(name, version_spec)| {
                let name = name.clone();
                let version_spec = version_spec.clone();
                async move {
                    match self.resolve_single_package(&name, &version_spec).await {
                        Ok(package) => Some((name, package)),
                        Err(e) => {
                            warn!("Failed to resolve {}: {}", name, e);
                            None
                        }
                    }
                }
            })
            .collect();

        // Execute all futures concurrently with MAXIMUM parallelism for sub-2 second resolution
        let results = stream::iter(package_futures)
            .buffer_unordered(500) // ULTRA-HIGH concurrency - 500 simultaneous requests
            .collect::<Vec<_>>()
            .await;

        // Collect successful results
        for result in results {
            if let Some((name, package)) = result {
                resolved.insert(name, package);
            }
        }

        Ok(resolved)
    }
    
    /// Resolve a single package with caching
    async fn resolve_single_package(
        &self,
        name: &str,
        version_spec: &str,
    ) -> Result<ResolvedPackage> {
        // Get package metadata with caching
        let metadata = self.get_cached_metadata(name).await?;
        
        // Find the best version that satisfies the spec
        let version = self.find_best_version(&metadata, version_spec)?;
        
        // Get version-specific information
        let version_info = metadata.versions.get(&version)
            .ok_or_else(|| anyhow::anyhow!("Version {} not found for {}", version, name))?;
        
        Ok(ResolvedPackage {
            name: name.to_string(),
            version,
            dependencies: version_info.dependencies.clone(),
            dev_dependencies: version_info.dev_dependencies.clone(),
            peer_dependencies: version_info.peer_dependencies.clone(),
            optional_dependencies: version_info.optional_dependencies.clone(),
            tarball_url: version_info.dist.tarball.clone(),
            integrity: version_info.dist.integrity.clone(),
        })
    }
    
    /// Find the best version that satisfies a version specification
    fn find_best_version(
        &self,
        metadata: &crate::registry::PackageMetadata,
        version_spec: &str,
    ) -> Result<String> {
        // For now, use latest version (can be improved with proper semver matching)
        if version_spec == "latest" || version_spec.starts_with('^') || version_spec.starts_with('~') {
            Ok(metadata.latest_version.clone())
        } else {
            // Try exact version first
            if metadata.versions.contains_key(version_spec) {
                Ok(version_spec.to_string())
            } else {
                // Fallback to latest
                Ok(metadata.latest_version.clone())
            }
        }
    }
    
    /// Get package metadata with ultra-aggressive caching for sub-second lookups
    async fn get_cached_metadata(&self, name: &str) -> Result<crate::registry::PackageMetadata> {
        // Fast path: check cache first with read lock
        {
            let cache = self.metadata_cache.read().await;
            if let Some(metadata) = cache.get(name) {
                debug!("Cache hit for package: {}", name);
                return Ok(metadata.clone());
            }
        }

        debug!("Cache miss for package: {}, fetching from registry", name);

        // Fetch from registry if not cached
        let fetch_start = std::time::Instant::now();
        let metadata = self.registry.get_package_metadata(name).await?;
        let fetch_duration = fetch_start.elapsed();

        debug!("Fetched {} metadata in {:.3}s", name, fetch_duration.as_secs_f64());

        // Cache the result with write lock
        {
            let mut cache = self.metadata_cache.write().await;
            cache.insert(name.to_string(), metadata.clone());
        }

        Ok(metadata)
    }
}
