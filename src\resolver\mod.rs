pub mod graph;
pub mod semver;
pub mod types;
pub mod engine;
pub mod version;

use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use futures::stream::{self, StreamExt};
use tracing::{info, debug, warn};

pub use graph::*;
pub use semver::*;
pub use types::*;
pub use engine::*;
pub use version::*;

/// Dependency resolver for npm packages with caching and parallel processing
#[derive(Debug)]
pub struct Resolver {
    registry: crate::registry::Registry,
    metadata_cache: Arc<RwLock<HashMap<String, crate::registry::PackageMetadata>>>,
}

impl Resolver {
    pub fn new(registry: crate::registry::Registry) -> Self {
        Self {
            registry,
            metadata_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Resolve dependencies from a package manifest with ultra-fast parallel processing
    pub async fn resolve_dependencies(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<DependencyGraph> {
        let mut graph = DependencyGraph::new();
        let mut semver_resolver = SemverResolver::new();

        info!("Resolving {} dependencies in parallel", dependencies.len());

        // Parallel metadata fetching with caching
        let metadata_futures: Vec<_> = dependencies.keys()
            .map(|name| self.get_cached_metadata(name))
            .collect();

        let metadata_results = stream::iter(metadata_futures)
            .buffer_unordered(50) // Process up to 50 packages concurrently
            .collect::<Vec<_>>()
            .await;

        // Build version resolver from cached metadata
        for (name, result) in dependencies.keys().zip(metadata_results.iter()) {
            if let Ok(package_metadata) = result {
                let versions: Vec<::semver::Version> = package_metadata.versions
                    .keys()
                    .filter_map(|v| ::semver::Version::parse(v).ok())
                    .collect();
                semver_resolver.add_versions(name, versions);
            }
        }

        // Fast dependency resolution
        for (name, version_spec) in dependencies {
            let resolved_version = self.resolve_version(&semver_resolver, name, version_spec).await?;
            graph.add_dependency(name.clone(), resolved_version.clone());

            // Recursively resolve dependencies of this package
            if let Ok(version_metadata) = self.registry.get_version_metadata(name, &resolved_version).await {
                for (dep_name, dep_spec) in &version_metadata.dependencies {
                    graph.add_edge(name.clone(), dep_name.clone(), DependencyType::Production);

                    // Add to graph if not already present
                    if !graph.nodes.contains_key(dep_name) {
                        let dep_resolved = self.resolve_version(&semver_resolver, dep_name, dep_spec).await?;
                        graph.add_dependency(dep_name.clone(), dep_resolved);
                    }
                }

                // Handle peer dependencies
                for (peer_name, peer_spec) in &version_metadata.peer_dependencies {
                    graph.add_edge(name.clone(), peer_name.clone(), DependencyType::Peer);

                    if !graph.nodes.contains_key(peer_name) {
                        let peer_resolved = self.resolve_version(&semver_resolver, peer_name, peer_spec).await?;
                        graph.add_dependency(peer_name.clone(), peer_resolved);
                    }
                }
            }
        }

        // Check for cycles
        let cycles = graph.detect_cycles();
        if !cycles.is_empty() {
            warn!("Detected {} circular dependencies", cycles.len());
            for cycle in &cycles {
                warn!("Circular dependency: {}", cycle.join(" -> "));
            }
        }

        info!("Resolved dependency graph with {} packages", graph.nodes.len());
        Ok(graph)
    }

    /// Resolve a specific version from a version specification
    async fn resolve_version(
        &self,
        semver_resolver: &SemverResolver,
        name: &str,
        version_spec: &str
    ) -> Result<String> {
        debug!("Resolving {}@{}", name, version_spec);

        match semver_resolver.resolve_version(name, version_spec)? {
            Some(version) => {
                debug!("Resolved {}@{} -> {}", name, version_spec, version);
                Ok(version.to_string())
            }
            None => {
                warn!("No compatible version found for {}@{}", name, version_spec);
                Err(anyhow::anyhow!("No compatible version found for {}@{}", name, version_spec))
            }
        }
    }

    /// Get package metadata with caching for ultra-fast lookups
    async fn get_cached_metadata(&self, name: &str) -> Result<crate::registry::PackageMetadata> {
        // Check cache first
        {
            let cache = self.metadata_cache.read().await;
            if let Some(metadata) = cache.get(name) {
                debug!("Cache hit for package: {}", name);
                return Ok(metadata.clone());
            }
        }

        // Fetch from registry if not cached
        debug!("Fetching metadata for package: {}", name);
        let metadata = self.registry.get_package_metadata(name).await?;

        // Cache the result
        {
            let mut cache = self.metadata_cache.write().await;
            cache.insert(name.to_string(), metadata.clone());
        }

        Ok(metadata)
    }
}
