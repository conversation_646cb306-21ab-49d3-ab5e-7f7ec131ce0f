pub mod graph;
pub mod semver;
pub mod types;
pub mod engine;
pub mod version;

use anyhow::Result;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use futures::stream::{self, StreamExt};
use tracing::{info, debug, warn};

pub use graph::*;
pub use semver::*;
pub use types::*;
pub use engine::*;
pub use version::*;

/// Ultra-fast dependency resolver with aggressive caching and parallel processing
#[derive(Debug)]
pub struct Resolver {
    registry: crate::registry::Registry,
    metadata_cache: Arc<RwLock<HashMap<String, crate::registry::PackageMetadata>>>,
}

impl Resolver {
    pub fn new(registry: crate::registry::Registry) -> Self {
        Self {
            registry,
            metadata_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Complete dependency resolution with transitive dependencies like npm
    pub async fn resolve_dependencies(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<DependencyGraph> {
        let start_time = std::time::Instant::now();
        info!("🚀 Resolving {} root dependencies with full transitive resolution", dependencies.len());

        let mut graph = DependencyGraph::new();

        // Fast-path: if no dependencies, return immediately
        if dependencies.is_empty() {
            debug!("No dependencies to resolve");
            return Ok(graph);
        }

        // Recursively resolve all dependencies and transitive dependencies
        let mut resolved_packages = HashMap::new();
        let mut pending_packages = dependencies.clone();
        let mut processed_packages = HashSet::new();

        while !pending_packages.is_empty() {
            debug!("Resolving batch of {} packages", pending_packages.len());

            // Batch resolve current level of dependencies
            let batch_resolved = self.batch_resolve_packages(&pending_packages).await?;

            // Add resolved packages to our collection and find new dependencies
            let mut next_pending = HashMap::new();
            for (name, package) in batch_resolved {
                if !processed_packages.contains(&name) {
                    resolved_packages.insert(name.clone(), package.clone());
                    processed_packages.insert(name.clone());

                    // Add package to dependency graph
                    let node = DependencyNode {
                        name: name.clone(),
                        version: package.version.clone(),
                        dependencies: package.dependencies.clone(),
                        dev_dependencies: package.dev_dependencies.clone(),
                        peer_dependencies: package.peer_dependencies.clone(),
                        optional_dependencies: package.optional_dependencies.clone(),
                    };
                    graph.add_node(node);

                    // Find new transitive dependencies to resolve
                    for (dep_name, dep_version) in &package.dependencies {
                        if !processed_packages.contains(dep_name) && !next_pending.contains_key(dep_name) {
                            next_pending.insert(dep_name.clone(), dep_version.clone());
                        }
                    }

                    // Also include peer dependencies for complete resolution
                    for (dep_name, dep_version) in &package.peer_dependencies {
                        if !processed_packages.contains(dep_name) && !next_pending.contains_key(dep_name) {
                            next_pending.insert(dep_name.clone(), dep_version.clone());
                        }
                    }
                }
            }


            pending_packages = next_pending;
        }

        let duration = start_time.elapsed();
        info!("✅ Resolved {} total packages (including transitive) in {:.2}s",
              resolved_packages.len(), duration.as_secs_f64());

        Ok(graph)
    }
    
    /// Batch resolve packages with ultra-fast parallel processing
    async fn batch_resolve_packages(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<HashMap<String, ResolvedPackage>> {
        let mut resolved = HashMap::new();
        
        // Create futures for all packages with maximum concurrency
        let package_futures: Vec<_> = dependencies.iter()
            .map(|(name, version_spec)| {
                let name = name.clone();
                let version_spec = version_spec.clone();
                async move {
                    match self.resolve_single_package(&name, &version_spec).await {
                        Ok(package) => Some((name, package)),
                        Err(e) => {
                            warn!("Failed to resolve {}: {}", name, e);
                            None
                        }
                    }
                }
            })
            .collect();

        // Execute all futures concurrently with MAXIMUM parallelism for sub-2 second resolution
        let results = stream::iter(package_futures)
            .buffer_unordered(500) // ULTRA-HIGH concurrency - 500 simultaneous requests
            .collect::<Vec<_>>()
            .await;

        // Collect successful results
        for result in results {
            if let Some((name, package)) = result {
                resolved.insert(name, package);
            }
        }

        Ok(resolved)
    }
    
    /// Resolve a single package with caching
    async fn resolve_single_package(
        &self,
        name: &str,
        version_spec: &str,
    ) -> Result<ResolvedPackage> {
        // Get package metadata with caching
        let metadata = self.get_cached_metadata(name).await?;
        
        // Find the best version that satisfies the spec
        let version = self.find_best_version(&metadata, version_spec)?;
        
        // Get version-specific information
        let version_info = metadata.versions.get(&version)
            .ok_or_else(|| anyhow::anyhow!("Version {} not found for {}", version, name))?;
        
        Ok(ResolvedPackage {
            name: name.to_string(),
            version,
            dependencies: version_info.dependencies.clone(),
            dev_dependencies: version_info.dev_dependencies.clone(),
            peer_dependencies: version_info.peer_dependencies.clone(),
            optional_dependencies: version_info.optional_dependencies.clone(),
            tarball_url: version_info.dist.tarball.clone(),
            integrity: version_info.dist.integrity.clone(),
        })
    }
    
    /// Find the best version that satisfies a version specification
    fn find_best_version(
        &self,
        metadata: &crate::registry::PackageMetadata,
        version_spec: &str,
    ) -> Result<String> {
        // For now, use latest version (can be improved with proper semver matching)
        if version_spec == "latest" || version_spec.starts_with('^') || version_spec.starts_with('~') {
            Ok(metadata.latest_version.clone())
        } else {
            // Try exact version first
            if metadata.versions.contains_key(version_spec) {
                Ok(version_spec.to_string())
            } else {
                // Fallback to latest
                Ok(metadata.latest_version.clone())
            }
        }
    }
    
    /// Get package metadata with ultra-aggressive caching for sub-second lookups
    async fn get_cached_metadata(&self, name: &str) -> Result<crate::registry::PackageMetadata> {
        // Fast path: check cache first with read lock
        {
            let cache = self.metadata_cache.read().await;
            if let Some(metadata) = cache.get(name) {
                debug!("Cache hit for package: {}", name);
                return Ok(metadata.clone());
            }
        }

        debug!("Cache miss for package: {}, fetching from registry", name);

        // Fetch from registry if not cached
        let fetch_start = std::time::Instant::now();
        let metadata = self.registry.get_package_metadata(name).await?;
        let fetch_duration = fetch_start.elapsed();

        debug!("Fetched {} metadata in {:.3}s", name, fetch_duration.as_secs_f64());

        // Cache the result with write lock
        {
            let mut cache = self.metadata_cache.write().await;
            cache.insert(name.to_string(), metadata.clone());
        }

        Ok(metadata)
    }
}
