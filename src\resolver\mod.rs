pub mod graph;
pub mod semver;
pub mod types;
pub mod engine;
pub mod version;

use anyhow::Result;
use std::collections::HashMap;
use tracing::{info, debug, warn};

pub use graph::*;
pub use semver::*;
pub use types::*;
pub use engine::*;
pub use version::*;

/// Dependency resolver for npm packages
#[derive(Debug)]
pub struct Resolver {
    registry: crate::registry::Registry,
}

impl Resolver {
    pub fn new(registry: crate::registry::Registry) -> Self {
        Self { registry }
    }
    
    /// Resolve dependencies from a package manifest
    pub async fn resolve_dependencies(
        &self,
        dependencies: &HashMap<String, String>,
    ) -> Result<DependencyGraph> {
        let mut graph = DependencyGraph::new();
        let mut semver_resolver = SemverResolver::new();

        info!("Resolving {} dependencies", dependencies.len());

        // First pass: collect all available versions for each package
        for (name, _version_spec) in dependencies {
            let package_metadata = self.registry.get_package_metadata(name).await?;
            let versions: Vec<::semver::Version> = package_metadata.versions
                .keys()
                .filter_map(|v| ::semver::Version::parse(v).ok())
                .collect();

            semver_resolver.add_versions(name, versions);
        }

        // Second pass: resolve each dependency
        for (name, version_spec) in dependencies {
            let resolved_version = self.resolve_version(&semver_resolver, name, version_spec).await?;
            graph.add_dependency(name.clone(), resolved_version.clone());

            // Recursively resolve dependencies of this package
            if let Ok(version_metadata) = self.registry.get_version_metadata(name, &resolved_version).await {
                for (dep_name, dep_spec) in &version_metadata.dependencies {
                    graph.add_edge(name.clone(), dep_name.clone(), DependencyType::Production);

                    // Add to graph if not already present
                    if !graph.nodes.contains_key(dep_name) {
                        let dep_resolved = self.resolve_version(&semver_resolver, dep_name, dep_spec).await?;
                        graph.add_dependency(dep_name.clone(), dep_resolved);
                    }
                }

                // Handle peer dependencies
                for (peer_name, peer_spec) in &version_metadata.peer_dependencies {
                    graph.add_edge(name.clone(), peer_name.clone(), DependencyType::Peer);

                    if !graph.nodes.contains_key(peer_name) {
                        let peer_resolved = self.resolve_version(&semver_resolver, peer_name, peer_spec).await?;
                        graph.add_dependency(peer_name.clone(), peer_resolved);
                    }
                }
            }
        }

        // Check for cycles
        let cycles = graph.detect_cycles();
        if !cycles.is_empty() {
            warn!("Detected {} circular dependencies", cycles.len());
            for cycle in &cycles {
                warn!("Circular dependency: {}", cycle.join(" -> "));
            }
        }

        info!("Resolved dependency graph with {} packages", graph.nodes.len());
        Ok(graph)
    }

    /// Resolve a specific version from a version specification
    async fn resolve_version(
        &self,
        semver_resolver: &SemverResolver,
        name: &str,
        version_spec: &str
    ) -> Result<String> {
        debug!("Resolving {}@{}", name, version_spec);

        match semver_resolver.resolve_version(name, version_spec)? {
            Some(version) => {
                debug!("Resolved {}@{} -> {}", name, version_spec, version);
                Ok(version.to_string())
            }
            None => {
                warn!("No compatible version found for {}@{}", name, version_spec);
                Err(anyhow::anyhow!("No compatible version found for {}@{}", name, version_spec))
            }
        }
    }
}
