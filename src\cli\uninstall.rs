use anyhow::{Result, Context};
use colored::*;
use tracing::{info, warn};

pub async fn execute(packages: Vec<String>, dev: bool, global: bool) -> Result<()> {
    if packages.is_empty() {
        return Err(anyhow::anyhow!("No packages specified for removal"));
    }
    
    info!("Uninstalling {} package{}", packages.len(), if packages.len() == 1 { "" } else { "s" });
    
    println!("{} Removing {} package{}...", 
        "→".blue().bold(),
        packages.len(),
        if packages.len() == 1 { "" } else { "s" }
    );
    
    for package in &packages {
        println!("  {} {}", "→".blue(), package.cyan());
    }
    
    // TODO: Implement actual package removal
    // For now, just show a placeholder
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    // Update package.json if it exists
    if std::path::Path::new("package.json").exists() && !global {
        update_package_json(&packages, dev).await?;
    }
    
    println!("{} Removed {} package{}", 
        "✓".green().bold(),
        packages.len(),
        if packages.len() == 1 { "" } else { "s" }
    );
    
    Ok(())
}

async fn update_package_json(packages: &[String], dev: bool) -> Result<()> {
    use serde_json::Value;
    use std::fs;
    
    let content = fs::read_to_string("package.json")
        .context("Failed to read package.json")?;
    
    let mut package_json: Value = serde_json::from_str(&content)
        .context("Failed to parse package.json")?;
    
    let deps_key = if dev { "devDependencies" } else { "dependencies" };
    
    // Remove packages from dependencies
    if let Some(deps) = package_json.get_mut(deps_key) {
        if let Some(deps_obj) = deps.as_object_mut() {
            for package in packages {
                deps_obj.remove(package);
            }
        }
    }
    
    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write("package.json", updated_content)
        .context("Failed to write package.json")?;
    
    println!("{} Updated {}", "✓".green().bold(), "package.json".cyan());
    
    Ok(())
}
