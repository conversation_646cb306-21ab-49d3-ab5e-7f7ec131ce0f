use anyhow::{Result, Context};
use std::collections::{HashMap, HashSet, VecDeque};
use tracing::{info, debug, warn, error};

use super::{DependencyGraph, DependencyNode, DependencyEdge, DependencyType, SemverResolver};
use crate::registry::RegistryManager;

/// Advanced dependency resolution engine
#[derive(Debug)]
pub struct ResolutionEngine {
    registry: RegistryManager,
    semver_resolver: SemverResolver,
    max_depth: usize,
    allow_cycles: bool,
}

impl ResolutionEngine {
    pub fn new(registry: RegistryManager) -> Self {
        Self {
            registry,
            semver_resolver: SemverResolver::new(),
            max_depth: 100,
            allow_cycles: false,
        }
    }
    
    /// Set maximum resolution depth to prevent infinite recursion
    pub fn with_max_depth(mut self, depth: usize) -> Self {
        self.max_depth = depth;
        self
    }
    
    /// Allow circular dependencies (not recommended)
    pub fn with_cycles_allowed(mut self, allow: bool) -> Self {
        self.allow_cycles = allow;
        self
    }
    
    /// Resolve dependencies with advanced conflict resolution
    pub async fn resolve(
        &mut self,
        root_dependencies: HashMap<String, String>,
        dev_dependencies: HashMap<String, String>,
    ) -> Result<ResolutionResult> {
        info!("Starting dependency resolution for {} production + {} dev dependencies", 
            root_dependencies.len(), dev_dependencies.len());
        
        let mut graph = DependencyGraph::new();
        let mut resolution_queue = VecDeque::new();
        let mut resolved_packages = HashMap::new();
        let mut conflicts = Vec::new();
        
        // Add root dependencies to queue
        for (name, spec) in &root_dependencies {
            resolution_queue.push_back(ResolutionTask {
                name: name.clone(),
                version_spec: spec.clone(),
                dependency_type: DependencyType::Production,
                depth: 0,
                parent: None,
            });
        }
        
        // Add dev dependencies to queue
        for (name, spec) in &dev_dependencies {
            resolution_queue.push_back(ResolutionTask {
                name: name.clone(),
                version_spec: spec.clone(),
                dependency_type: DependencyType::Development,
                depth: 0,
                parent: None,
            });
        }
        
        // Process resolution queue
        while let Some(task) = resolution_queue.pop_front() {
            if task.depth > self.max_depth {
                warn!("Maximum resolution depth reached for {}", task.name);
                continue;
            }
            
            match self.resolve_package(&task, &mut resolved_packages).await {
                Ok(resolution) => {
                    // Add to graph
                    if !graph.nodes.contains_key(&task.name) {
                        graph.add_dependency(task.name.clone(), resolution.resolved_version.clone());
                    }
                    
                    // Add edge from parent if exists
                    if let Some(parent) = &task.parent {
                        graph.add_edge(parent.clone(), task.name.clone(), task.dependency_type.clone());
                    }
                    
                    // Queue dependencies
                    for (dep_name, dep_spec) in &resolution.dependencies {
                        resolution_queue.push_back(ResolutionTask {
                            name: dep_name.clone(),
                            version_spec: dep_spec.clone(),
                            dependency_type: DependencyType::Production,
                            depth: task.depth + 1,
                            parent: Some(task.name.clone()),
                        });
                    }
                    
                    // Queue peer dependencies
                    for (peer_name, peer_spec) in &resolution.peer_dependencies {
                        resolution_queue.push_back(ResolutionTask {
                            name: peer_name.clone(),
                            version_spec: peer_spec.clone(),
                            dependency_type: DependencyType::Peer,
                            depth: task.depth + 1,
                            parent: Some(task.name.clone()),
                        });
                    }
                }
                Err(e) => {
                    error!("Failed to resolve {}: {}", task.name, e);
                    conflicts.push(ResolutionConflict {
                        package: task.name.clone(),
                        requested_spec: task.version_spec.clone(),
                        error: e.to_string(),
                        parent: task.parent.clone(),
                    });
                }
            }
        }
        
        // Detect and handle cycles
        let cycles = graph.detect_cycles();
        if !cycles.is_empty() && !self.allow_cycles {
            return Err(anyhow::anyhow!("Circular dependencies detected: {:?}", cycles));
        }
        
        // Optimize the graph
        graph.optimize()?;
        
        // Get installation order
        let install_order = graph.topological_sort()?;
        
        info!("Resolution completed: {} packages, {} conflicts", 
            graph.nodes.len(), conflicts.len());
        
        Ok(ResolutionResult {
            graph,
            install_order,
            conflicts,
            cycles,
            resolved_packages,
        })
    }
    
    /// Resolve a single package
    async fn resolve_package(
        &mut self,
        task: &ResolutionTask,
        resolved_packages: &mut HashMap<String, PackageResolution>,
    ) -> Result<PackageResolution> {
        // Check if already resolved
        if let Some(existing) = resolved_packages.get(&task.name) {
            // Check if version specs are compatible
            if self.semver_resolver.are_compatible(&existing.requested_spec, &task.version_spec)? {
                return Ok(existing.clone());
            } else {
                return Err(anyhow::anyhow!(
                    "Version conflict for {}: {} vs {}", 
                    task.name, existing.requested_spec, task.version_spec
                ));
            }
        }
        
        debug!("Resolving package: {}@{}", task.name, task.version_spec);
        
        // Get package metadata
        let package_metadata = self.registry.get_package_metadata(&task.name).await
            .context("Failed to fetch package metadata")?;
        
        // Add available versions to semver resolver
        let versions: Vec<::semver::Version> = package_metadata.versions
            .keys()
            .filter_map(|v| ::semver::Version::parse(v).ok())
            .collect();
        
        self.semver_resolver.add_versions(&task.name, versions);
        
        // Resolve version
        let resolved_version = self.semver_resolver
            .resolve_version(&task.name, &task.version_spec)?
            .context("No compatible version found")?;
        
        // Get version metadata
        let version_metadata = self.registry
            .get_version_metadata(&task.name, &resolved_version.to_string()).await
            .context("Failed to fetch version metadata")?;
        
        let resolution = PackageResolution {
            name: task.name.clone(),
            requested_spec: task.version_spec.clone(),
            resolved_version: resolved_version.to_string(),
            dependencies: version_metadata.dependencies.clone(),
            peer_dependencies: version_metadata.peer_dependencies.clone(),
            metadata: version_metadata,
        };
        
        resolved_packages.insert(task.name.clone(), resolution.clone());
        
        debug!("Resolved {}@{} -> {}", task.name, task.version_spec, resolved_version);
        Ok(resolution)
    }
}

/// Task for dependency resolution
#[derive(Debug, Clone)]
struct ResolutionTask {
    name: String,
    version_spec: String,
    dependency_type: DependencyType,
    depth: usize,
    parent: Option<String>,
}

/// Result of resolving a single package
#[derive(Debug, Clone)]
pub struct PackageResolution {
    pub name: String,
    pub requested_spec: String,
    pub resolved_version: String,
    pub dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub metadata: crate::registry::VersionMetadata,
}

/// Complete resolution result
#[derive(Debug)]
pub struct ResolutionResult {
    pub graph: DependencyGraph,
    pub install_order: Vec<String>,
    pub conflicts: Vec<ResolutionConflict>,
    pub cycles: Vec<Vec<String>>,
    pub resolved_packages: HashMap<String, PackageResolution>,
}

/// Resolution conflict information
#[derive(Debug, Clone)]
pub struct ResolutionConflict {
    pub package: String,
    pub requested_spec: String,
    pub error: String,
    pub parent: Option<String>,
}

impl ResolutionResult {
    /// Get packages that need to be installed
    pub fn get_install_packages(&self) -> Vec<InstallPackage> {
        self.install_order
            .iter()
            .filter_map(|name| {
                self.resolved_packages.get(name).map(|resolution| InstallPackage {
                    name: name.clone(),
                    version: resolution.resolved_version.clone(),
                    tarball_url: resolution.metadata.dist.tarball.clone(),
                    integrity: resolution.metadata.dist.integrity.clone(),
                })
            })
            .collect()
    }
    
    /// Check if resolution was successful
    pub fn is_successful(&self) -> bool {
        self.conflicts.is_empty() && (self.cycles.is_empty() || self.cycles.iter().all(|c| c.is_empty()))
    }
}

/// Package ready for installation
#[derive(Debug, Clone)]
pub struct InstallPackage {
    pub name: String,
    pub version: String,
    pub tarball_url: String,
    pub integrity: Option<String>,
}
