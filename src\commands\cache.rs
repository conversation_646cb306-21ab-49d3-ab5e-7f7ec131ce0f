use anyhow::Result;
use colored::*;
use std::path::PathBuf;
use crate::cache::UltraFastCache;
use crate::cli::{CacheArgs, CacheAction};
use crate::ui::UI;
use crate::utils::format_bytes;

/// Execute nx cache command
pub async fn execute(args: CacheArgs, ui: &UI) -> Result<()> {
    let cache_dir = dirs::cache_dir()
        .unwrap_or_else(|| PathBuf::from("."))
        .join("nx");
    
    let cache = UltraFastCache::new(cache_dir)?;

    match args.action {
        CacheAction::Clear => {
            ui.step("🗑️  Clearing cache...");
            let cleared_size = cache.clear().await?;
            ui.success(&format!("✅ Cleared {} from cache", format_bytes(cleared_size)));
        }
        CacheAction::Stats => {
            ui.step("📊 Getting cache statistics...");
            let stats = cache.get_stats().await;
            
            println!("\n📊 Cache Statistics");
            println!("┌─────────────────────┬───────────────────────────────────────┐");
            println!("│ {} │ {:<37} │", "Memory Entries".bright_white().bold(), stats.memory_entries);
            println!("│ {} │ {:<37} │", "Disk Entries".bright_white().bold(), stats.disk_entries);
            println!("│ {} │ {:<37} │", "Memory Mapped".bright_white().bold(), stats.mmap_entries);
            println!("│ {} │ {:<37} │", "Total Disk Size".bright_white().bold(), format_bytes(stats.total_disk_size));
            println!("└─────────────────────┴───────────────────────────────────────┘");
        }
        CacheAction::Verify => {
            ui.step("🔍 Verifying cache integrity...");
            let result = cache.verify().await?;
            
            if result.corrupted_files == 0 {
                ui.success(&format!("✅ Cache verified: {} files OK", result.total_files));
            } else {
                ui.warning(&format!("⚠️  Found {} corrupted files out of {}", 
                                   result.corrupted_files, result.total_files));
            }
        }
        CacheAction::Dir { path } => {
            ui.info(&format!("Cache directory set to: {}", path));
            // TODO: Implement cache directory change
        }
    }

    Ok(())
}
