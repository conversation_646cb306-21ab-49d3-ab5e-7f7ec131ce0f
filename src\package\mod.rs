use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

/// Represents a package.json file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageJson {
    pub name: String,
    pub version: String,
    #[serde(default)]
    pub description: String,
    #[serde(default)]
    pub main: String,
    #[serde(default)]
    pub scripts: HashMap<String, String>,
    #[serde(default)]
    pub dependencies: HashMap<String, String>,
    #[serde(default, rename = "devDependencies")]
    pub dev_dependencies: HashMap<String, String>,
    #[serde(default, rename = "peerDependencies")]
    pub peer_dependencies: HashMap<String, String>,
    #[serde(default, rename = "optionalDependencies")]
    pub optional_dependencies: HashMap<String, String>,
    #[serde(default)]
    pub keywords: Vec<String>,
    #[serde(default)]
    pub author: String,
    #[serde(default)]
    pub license: String,
    #[serde(default)]
    pub homepage: Option<String>,
    #[serde(default)]
    pub repository: Option<String>,
    #[serde(default)]
    pub bugs: Option<String>,
    #[serde(default)]
    pub bin: Option<serde_json::Value>,
    #[serde(default)]
    pub engines: HashMap<String, String>,
    #[serde(default)]
    pub os: Vec<String>,
    #[serde(default)]
    pub cpu: Vec<String>,
    #[serde(default)]
    pub private: bool,
    #[serde(default)]
    pub workspaces: Option<serde_json::Value>,
    #[serde(flatten)]
    pub other: HashMap<String, serde_json::Value>,
}

impl PackageJson {
    /// Read package.json from a file
    pub async fn read<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref()).await
            .context("Failed to read package.json")?;
        
        let package_json: PackageJson = serde_json::from_str(&content)
            .context("Failed to parse package.json")?;
        
        Ok(package_json)
    }
    
    /// Write package.json to a file
    pub async fn write<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)
            .context("Failed to serialize package.json")?;
        
        fs::write(path.as_ref(), content).await
            .context("Failed to write package.json")?;
        
        Ok(())
    }
    
    /// Create a new basic package.json
    pub fn new(name: String) -> Self {
        let mut scripts = HashMap::new();
        scripts.insert("test".to_string(), "echo \"Error: no test specified\" && exit 1".to_string());
        scripts.insert("start".to_string(), "node index.js".to_string());
        scripts.insert("dev".to_string(), "node index.js".to_string());
        
        Self {
            name,
            version: "1.0.0".to_string(),
            description: String::new(),
            main: "index.js".to_string(),
            scripts,
            dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            optional_dependencies: HashMap::new(),
            keywords: Vec::new(),
            author: String::new(),
            license: "ISC".to_string(),
            homepage: None,
            repository: None,
            bugs: None,
            bin: None,
            engines: HashMap::new(),
            os: Vec::new(),
            cpu: Vec::new(),
            private: false,
            workspaces: None,
            other: HashMap::new(),
        }
    }
    
    /// Add a dependency
    pub fn add_dependency(&mut self, name: String, version: String, dep_type: DependencyType) {
        match dep_type {
            DependencyType::Regular => {
                self.dependencies.insert(name, version);
            }
            DependencyType::Dev => {
                self.dev_dependencies.insert(name, version);
            }
            DependencyType::Peer => {
                self.peer_dependencies.insert(name, version);
            }
            DependencyType::Optional => {
                self.optional_dependencies.insert(name, version);
            }
        }
    }
    
    /// Remove a dependency
    pub fn remove_dependency(&mut self, name: &str) -> bool {
        let removed_regular = self.dependencies.remove(name).is_some();
        let removed_dev = self.dev_dependencies.remove(name).is_some();
        let removed_peer = self.peer_dependencies.remove(name).is_some();
        let removed_optional = self.optional_dependencies.remove(name).is_some();
        
        removed_regular || removed_dev || removed_peer || removed_optional
    }
    
    /// Get all dependencies (regular + dev)
    pub fn all_dependencies(&self) -> HashMap<String, String> {
        let mut all_deps = self.dependencies.clone();
        all_deps.extend(self.dev_dependencies.clone());
        all_deps
    }
    
    /// Check if package exists in any dependency type
    pub fn has_dependency(&self, name: &str) -> bool {
        self.dependencies.contains_key(name) ||
        self.dev_dependencies.contains_key(name) ||
        self.peer_dependencies.contains_key(name) ||
        self.optional_dependencies.contains_key(name)
    }
    
    /// Get binary entries
    pub fn get_binaries(&self) -> HashMap<String, String> {
        let mut binaries = HashMap::new();
        
        if let Some(bin) = &self.bin {
            match bin {
                serde_json::Value::String(path) => {
                    // Single binary with package name
                    binaries.insert(self.name.clone(), path.clone());
                }
                serde_json::Value::Object(map) => {
                    // Multiple binaries
                    for (name, path) in map {
                        if let Some(path_str) = path.as_str() {
                            binaries.insert(name.clone(), path_str.to_string());
                        }
                    }
                }
                _ => {}
            }
        }
        
        binaries
    }
}

/// Dependency types
#[derive(Debug, Clone, Copy)]
pub enum DependencyType {
    Regular,
    Dev,
    Peer,
    Optional,
}

impl Default for PackageJson {
    fn default() -> Self {
        Self::new("my-package".to_string())
    }
}
