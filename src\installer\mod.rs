pub mod downloader;
pub mod extractor;
pub mod linker;
pub mod download_manager;
pub mod streaming_extractor;

use anyhow::{Result, Context};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::Semaphore;
use futures::stream::{self, StreamExt};
use tracing::{info, debug, warn};

pub use downloader::*;
pub use extractor::*;
pub use linker::*;
pub use download_manager::*;
pub use streaming_extractor::*;

#[derive(Debug, Clone)]
pub struct PackageInstallInfo {
    pub name: String,
    pub version: String,
    pub tarball_url: String,
    pub integrity: Option<String>,
}

/// Package installer that handles downloading, extracting, and linking
#[derive(Debug)]
pub struct Installer {
    cache_dir: PathBuf,
    install_dir: PathBuf,
    downloader: Downloader,
    extractor: Extractor,
    streaming_extractor: StreamingExtractor,
    linker: Linker,
    semaphore: Semaphore,
    memory_cache: crate::cache::MemoryMappedCache,
}

impl Installer {
    pub fn new(cache_dir: PathBuf, install_dir: PathBuf, max_concurrent: usize) -> Self {
        let downloader = Downloader::new(cache_dir.clone(), max_concurrent);
        let extractor = Extractor::new(install_dir.clone());
        let streaming_extractor = StreamingExtractor::new(install_dir.clone());
        let linker = Linker::new(install_dir.clone());
        let semaphore = Semaphore::new(max_concurrent);
        let memory_cache = crate::cache::MemoryMappedCache::new(cache_dir.clone());

        Self {
            cache_dir,
            install_dir,
            downloader,
            extractor,
            streaming_extractor,
            linker,
            semaphore,
            memory_cache,
        }
    }
    
    /// Install a single package
    pub async fn install_package(
        &self,
        name: &str,
        version: &str,
        tarball_url: &str,
        integrity: Option<&str>,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        let _permit = self.semaphore.acquire().await?;
        
        info!("Installing {}@{}", name, version);
        
        // Download the package
        let tarball_path = self.downloader
            .download(name, version, tarball_url, integrity, ui)
            .await?;

        // Extract directly to final node_modules location
        let final_path = self.extractor
            .extract(&tarball_path, name, version, ui)
            .await?;

        // Link binaries after extraction
        self.linker
            .link_binaries(&final_path, name)
            .await?;
        
        debug!("Successfully installed {}@{}", name, version);
        Ok(())
    }
    
    /// Install multiple packages in parallel with beautiful UI
    pub async fn install_packages(
        &self,
        packages: Vec<PackageInstallInfo>,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        // Fast-path: check cache first for lightning speed
        let packages_len = packages.len();
        let (cached_packages, uncached_packages): (Vec<_>, Vec<_>) = packages
            .into_iter()
            .partition(|pkg| self.is_package_cached(&pkg.name, &pkg.version));

        // Install cached packages instantly
        for package in cached_packages {
            self.install_from_cache(&package).await?;
        }

        // Only download uncached packages
        if uncached_packages.is_empty() {
            return Ok(());
        }
        ui.step(&format!("Installing {} packages in parallel", uncached_packages.len()));

        // Convert to download requests
        let download_requests: Vec<crate::installer::downloader::DownloadRequest> = uncached_packages
            .iter()
            .map(|pkg| crate::installer::downloader::DownloadRequest {
                name: pkg.name.clone(),
                version: pkg.version.clone(),
                url: pkg.tarball_url.clone(),
                integrity: pkg.integrity.clone(),
            })
            .collect();

        // Download all packages in parallel with beautiful UI
        let download_results = self.downloader.download_multiple(download_requests, ui).await?;

        // Create installation progress bar
        let install_progress = ui.create_install_progress(uncached_packages.len() as u64);

        // Process successful downloads
        let mut install_handles = Vec::new();

        for (package, result) in uncached_packages.iter().zip(download_results.iter()) {
            if let Some(tarball_path) = &result.path {
                let installer = self.clone();
                let package = package.clone();
                let tarball_path = tarball_path.clone();
                let ui_clone = ui.clone();

                let handle = tokio::spawn(async move {
                    // Extract directly to final node_modules location
                    let final_path = installer.extractor
                        .extract(&tarball_path, &package.name, &package.version, &ui_clone)
                        .await?;

                    // Link binaries after extraction
                    installer.linker
                        .link_binaries(&final_path, &package.name)
                        .await?;

                    Ok::<(), anyhow::Error>(())
                });
                install_handles.push(handle);
            } else if let Some(error) = &result.error {
                ui.warning(&format!("Skipping installation of {}@{} due to download error: {}",
                    result.name, result.version, error));
            }
        }

        // Wait for all installations to complete
        for (i, handle) in install_handles.into_iter().enumerate() {
            handle.await??;
            install_progress.inc(1);
            install_progress.set_message(format!("Installed {}/{} packages", i + 1, packages_len));
        }

        let successful = download_results.iter().filter(|r| r.error.is_none()).count();
        let failed = download_results.len() - successful;

        install_progress.finish_with_message(format!("✓ Installed {} packages", successful));

        if failed > 0 {
            ui.warning(&format!("Installation completed: {} successful, {} failed", successful, failed));
        } else {
            ui.complete(&format!("Successfully installed {} packages", successful));
        }

        Ok(())
    }

    /// Install a single package using streaming pipeline (download + extract + link in parallel)
    pub async fn install_package_streaming(
        &self,
        package: PackageInstallInfo,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        let _permit = self.semaphore.acquire().await?;

        // Check cache first for instant installation
        if self.is_package_cached(&package.name, &package.version) {
            return self.install_from_cache(&package).await;
        }

        info!("Streaming install: {}@{}", package.name, package.version);

        // Download with streaming extraction
        let tarball_path = self.downloader
            .download(&package.name, &package.version, &package.tarball_url,
                     package.integrity.as_deref(), ui)
            .await?;

        // Extract directly to final location (no temporary directories)
        let final_path = self.extractor
            .extract(&tarball_path, &package.name, &package.version, ui)
            .await?;

        // Link binaries
        self.linker
            .link_binaries(&final_path, &package.name)
            .await?;

        debug!("Streaming install completed: {}@{}", package.name, package.version);
        Ok(())
    }

    /// Ultra-fast installation using memory-mapped cache (zero-copy)
    pub async fn install_package_ultra_fast(
        &self,
        package: PackageInstallInfo,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        let _permit = self.semaphore.acquire().await?;

        info!("Ultra-fast install: {}@{}", package.name, package.version);

        // Try memory-mapped cache first (zero-copy, instant)
        if let Some(mmap) = self.memory_cache.get_mapped_package(&package.name, &package.version).await? {
            // Extract from memory-mapped cache (zero-copy)
            let final_path = self.streaming_extractor
                .extract_from_mmap(mmap, &package.name, &package.version, ui)
                .await?;

            // Link binaries
            self.linker
                .link_binaries(&final_path, &package.name)
                .await?;

            debug!("Ultra-fast install completed (zero-copy): {}@{}", package.name, package.version);
            return Ok(());
        }

        // Download and cache for future zero-copy access
        let tarball_path = self.downloader
            .download(&package.name, &package.version, &package.tarball_url,
                     package.integrity.as_deref(), ui)
            .await?;

        // Read and cache in memory-mapped format
        let data = tokio::fs::read(&tarball_path).await?;
        self.memory_cache.store_package(&package.name, &package.version, &data).await?;

        // Extract using streaming extractor
        let final_path = self.streaming_extractor
            .extract_streaming(&data, &package.name, &package.version, ui)
            .await?;

        // Link binaries
        self.linker
            .link_binaries(&final_path, &package.name)
            .await?;

        debug!("Ultra-fast install completed: {}@{}", package.name, package.version);
        Ok(())
    }

    /// Clean up temporary files and extracted folders
    pub async fn cleanup(&self) -> Result<()> {
        let extracted_dir = self.install_dir.join("extracted");

        if extracted_dir.exists() {
            tokio::fs::remove_dir_all(&extracted_dir).await
                .context("Failed to clean up extracted directory")?;
        }

        Ok(())
    }

    /// Check if a package is already cached
    fn is_package_cached(&self, name: &str, version: &str) -> bool {
        let cache_path = self.cache_dir.join(format!("{}-{}.tgz", name, version));
        cache_path.exists()
    }

    /// Install package from cache (lightning fast)
    async fn install_from_cache(&self, package: &PackageInstallInfo) -> Result<()> {
        let cache_path = self.cache_dir.join(format!("{}-{}.tgz", package.name, package.version));

        // Extract directly from cache to final location
        let final_path = self.extractor
            .extract(&cache_path, &package.name, &package.version, &crate::ui::UI::new())
            .await?;

        // Link binaries after extraction
        self.linker
            .link_binaries(&final_path, &package.name)
            .await?;

        Ok(())
    }
}

impl Clone for Installer {
    fn clone(&self) -> Self {
        Self {
            cache_dir: self.cache_dir.clone(),
            install_dir: self.install_dir.clone(),
            downloader: self.downloader.clone(),
            extractor: self.extractor.clone(),
            streaming_extractor: self.streaming_extractor.clone(),
            linker: self.linker.clone(),
            semaphore: Semaphore::new(8), // Use a reasonable default for cloned instances
            memory_cache: self.memory_cache.clone(),
        }
    }
}


