pub mod downloader;
pub mod extractor;
pub mod linker;
pub mod download_manager;

use anyhow::{Result, Context};
use std::path::PathBuf;
use tokio::sync::Semaphore;
use tracing::{info, debug, warn};

pub use downloader::*;
pub use extractor::*;
pub use linker::*;
pub use download_manager::*;

#[derive(Debug, <PERSON>lone)]
pub struct PackageInstallInfo {
    pub name: String,
    pub version: String,
    pub tarball_url: String,
    pub integrity: Option<String>,
}

/// Package installer that handles downloading, extracting, and linking
#[derive(Debug)]
pub struct Installer {
    cache_dir: PathBuf,
    install_dir: PathBuf,
    downloader: Downloader,
    extractor: Extractor,
    linker: Linker,
    semaphore: Semaphore,
}

impl Installer {
    pub fn new(cache_dir: PathBuf, install_dir: PathBuf, max_concurrent: usize) -> Self {
        let downloader = Downloader::new(cache_dir.clone(), max_concurrent);
        let extractor = Extractor::new(install_dir.clone());
        let linker = Linker::new(install_dir.clone());
        let semaphore = Semaphore::new(max_concurrent);

        Self {
            cache_dir,
            install_dir,
            downloader,
            extractor,
            linker,
            semaphore,
        }
    }
    
    /// Install a single package
    pub async fn install_package(
        &self,
        name: &str,
        version: &str,
        tarball_url: &str,
        integrity: Option<&str>,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        let _permit = self.semaphore.acquire().await?;
        
        info!("Installing {}@{}", name, version);
        
        // Download the package
        let tarball_path = self.downloader
            .download(name, version, tarball_url, integrity, ui)
            .await?;

        // Extract directly to final node_modules location
        let final_path = self.extractor
            .extract(&tarball_path, name, version, ui)
            .await?;

        // Link binaries after extraction
        self.linker
            .link_binaries(&final_path, name)
            .await?;
        
        debug!("Successfully installed {}@{}", name, version);
        Ok(())
    }
    
    /// Install multiple packages in parallel with beautiful UI
    pub async fn install_packages(
        &self,
        packages: Vec<PackageInstallInfo>,
        ui: &crate::ui::UI,
    ) -> Result<()> {
        // Fast-path: check cache first for lightning speed
        let packages_len = packages.len();
        let (cached_packages, uncached_packages): (Vec<_>, Vec<_>) = packages
            .into_iter()
            .partition(|pkg| self.is_package_cached(&pkg.name, &pkg.version));

        // Install cached packages instantly
        for package in cached_packages {
            self.install_from_cache(&package).await?;
        }

        // Only download uncached packages
        if uncached_packages.is_empty() {
            return Ok(());
        }
        ui.step(&format!("Installing {} packages in parallel", uncached_packages.len()));

        // Convert to download requests
        let download_requests: Vec<crate::installer::downloader::DownloadRequest> = uncached_packages
            .iter()
            .map(|pkg| crate::installer::downloader::DownloadRequest {
                name: pkg.name.clone(),
                version: pkg.version.clone(),
                url: pkg.tarball_url.clone(),
                integrity: pkg.integrity.clone(),
            })
            .collect();

        // Download all packages in parallel with beautiful UI
        let download_results = self.downloader.download_multiple(download_requests, ui).await?;

        // Create installation progress bar
        let install_progress = ui.create_install_progress(uncached_packages.len() as u64);

        // Process successful downloads
        let mut install_handles = Vec::new();

        for (package, result) in uncached_packages.iter().zip(download_results.iter()) {
            if let Some(tarball_path) = &result.path {
                let installer = self.clone();
                let package = package.clone();
                let tarball_path = tarball_path.clone();
                let ui_clone = ui.clone();

                let handle = tokio::spawn(async move {
                    // Extract directly to final node_modules location
                    let final_path = installer.extractor
                        .extract(&tarball_path, &package.name, &package.version, &ui_clone)
                        .await?;

                    // Link binaries after extraction
                    installer.linker
                        .link_binaries(&final_path, &package.name)
                        .await?;

                    Ok::<(), anyhow::Error>(())
                });
                install_handles.push(handle);
            } else if let Some(error) = &result.error {
                ui.warning(&format!("Skipping installation of {}@{} due to download error: {}",
                    result.name, result.version, error));
            }
        }

        // Wait for all installations to complete
        for (i, handle) in install_handles.into_iter().enumerate() {
            handle.await??;
            install_progress.inc(1);
            install_progress.set_message(format!("Installed {}/{} packages", i + 1, packages_len));
        }

        let successful = download_results.iter().filter(|r| r.error.is_none()).count();
        let failed = download_results.len() - successful;

        install_progress.finish_with_message(format!("✓ Installed {} packages", successful));

        if failed > 0 {
            ui.warning(&format!("Installation completed: {} successful, {} failed", successful, failed));
        } else {
            ui.complete(&format!("Successfully installed {} packages", successful));
        }

        Ok(())
    }
    
    /// Clean up temporary files and extracted folders
    pub async fn cleanup(&self) -> Result<()> {
        let extracted_dir = self.install_dir.join("extracted");

        if extracted_dir.exists() {
            tokio::fs::remove_dir_all(&extracted_dir).await
                .context("Failed to clean up extracted directory")?;
        }

        Ok(())
    }

    /// Check if a package is already cached
    fn is_package_cached(&self, name: &str, version: &str) -> bool {
        let cache_path = self.cache_dir.join(format!("{}-{}.tgz", name, version));
        cache_path.exists()
    }

    /// Install package from cache (lightning fast)
    async fn install_from_cache(&self, package: &PackageInstallInfo) -> Result<()> {
        let cache_path = self.cache_dir.join(format!("{}-{}.tgz", package.name, package.version));

        // Extract directly from cache to final location
        let final_path = self.extractor
            .extract(&cache_path, &package.name, &package.version, &crate::ui::UI::new())
            .await?;

        // Link binaries after extraction
        self.linker
            .link_binaries(&final_path, &package.name)
            .await?;

        Ok(())
    }
}

impl Clone for Installer {
    fn clone(&self) -> Self {
        Self {
            cache_dir: self.cache_dir.clone(),
            install_dir: self.install_dir.clone(),
            downloader: self.downloader.clone(),
            extractor: self.extractor.clone(),
            linker: self.linker.clone(),
            semaphore: Semaphore::new(8), // Use a reasonable default for cloned instances
        }
    }
}


