use anyhow::{Result, Context};
use indicatif::{MultiProgress, ProgressBar};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tracing::{info, warn, debug};

use super::downloader::{Downloader, DownloadRequest, DownloadResult};
use crate::ui::ProgressUtils;

/// Download manager that orchestrates parallel downloads with advanced features
#[derive(Debug)]
pub struct DownloadManager {
    downloader: Downloader,
    multi_progress: Arc<MultiProgress>,
    max_concurrent: usize,
    bandwidth_limit: Option<u64>, // bytes per second
    retry_attempts: u32,
}

impl DownloadManager {
    pub fn new(downloader: Downloader, max_concurrent: usize) -> Self {
        Self {
            downloader,
            multi_progress: Arc::new(MultiProgress::new()),
            max_concurrent,
            bandwidth_limit: None,
            retry_attempts: 3,
        }
    }
    
    /// Set bandwidth limit in bytes per second
    pub fn with_bandwidth_limit(mut self, limit: u64) -> Self {
        self.bandwidth_limit = Some(limit);
        self
    }
    
    /// Set retry attempts for failed downloads
    pub fn with_retry_attempts(mut self, attempts: u32) -> Self {
        self.retry_attempts = attempts;
        self
    }
    
    /// Download packages with advanced orchestration
    pub async fn download_packages(
        &self,
        requests: Vec<DownloadRequest>,
        ui: &crate::ui::UI,
    ) -> Result<DownloadBatch> {
        let start_time = Instant::now();
        info!("Starting download batch of {} packages", requests.len());
        
        // Create overall progress bar
        let overall_progress = self.multi_progress.add(
            ProgressUtils::install_progress(requests.len() as u64)
        );
        overall_progress.set_message("Downloading packages");
        
        // Group downloads by priority (smaller packages first for better UX)
        let mut prioritized_requests = self.prioritize_downloads(requests).await;
        
        // Execute downloads in batches
        let mut all_results = Vec::new();
        let semaphore = Arc::new(Semaphore::new(self.max_concurrent));
        
        while !prioritized_requests.is_empty() {
            let batch_size = std::cmp::min(self.max_concurrent, prioritized_requests.len());
            let current_batch: Vec<_> = prioritized_requests.drain(0..batch_size).collect();
            
            let batch_results = self.download_batch(
                current_batch,
                Arc::clone(&semaphore),
                &overall_progress,
                ui,
            ).await?;
            
            all_results.extend(batch_results);
        }
        
        overall_progress.finish_with_message("Downloads completed");
        
        let duration = start_time.elapsed();
        let successful = all_results.iter().filter(|r| r.error.is_none()).count();
        let failed = all_results.len() - successful;
        
        info!("Download batch completed in {:.2}s: {} successful, {} failed", 
            duration.as_secs_f64(), successful, failed);
        
        Ok(DownloadBatch {
            results: all_results,
            duration,
            successful_count: successful,
            failed_count: failed,
            total_bytes: 0, // TODO: Calculate from results
        })
    }
    
    /// Download a single batch of packages
    async fn download_batch(
        &self,
        requests: Vec<DownloadRequest>,
        semaphore: Arc<Semaphore>,
        overall_progress: &ProgressBar,
        ui: &crate::ui::UI,
    ) -> Result<Vec<DownloadResult>> {
        let mut handles = Vec::new();
        
        for request in requests {
            let downloader = self.downloader.clone();
            let semaphore = Arc::clone(&semaphore);
            let overall_progress = overall_progress.clone();
            let retry_attempts = self.retry_attempts;
            let ui_clone = ui.clone();

            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                // Retry logic
                let mut last_error = None;
                for attempt in 1..=retry_attempts {
                    match downloader.download(
                        &request.name,
                        &request.version,
                        &request.url,
                        request.integrity.as_deref(),
                        &ui_clone,
                    ).await {
                        Ok(path) => {
                            overall_progress.inc(1);
                            return DownloadResult {
                                name: request.name,
                                version: request.version,
                                path: Some(path),
                                error: None,
                                duration: Duration::from_secs(0), // TODO: Track actual duration
                            };
                        }
                        Err(e) => {
                            warn!("Download attempt {} failed for {}@{}: {}", 
                                attempt, request.name, request.version, e);
                            last_error = Some(e);
                            
                            if attempt < retry_attempts {
                                // Exponential backoff
                                let delay = Duration::from_millis(100 * (1 << (attempt - 1)));
                                tokio::time::sleep(delay).await;
                            }
                        }
                    }
                }
                
                overall_progress.inc(1);
                DownloadResult {
                    name: request.name,
                    version: request.version,
                    path: None,
                    error: Some(last_error.unwrap().to_string()),
                    duration: Duration::from_secs(0),
                }
            });
            
            handles.push(handle);
        }
        
        // Wait for all downloads in this batch
        let mut results = Vec::new();
        for handle in handles {
            results.push(handle.await?);
        }
        
        Ok(results)
    }
    
    /// Prioritize downloads (smaller packages first, critical packages first)
    async fn prioritize_downloads(&self, mut requests: Vec<DownloadRequest>) -> Vec<DownloadRequest> {
        // For now, just shuffle to distribute load
        // TODO: Implement actual prioritization based on package size and criticality
        requests.sort_by(|a, b| a.name.cmp(&b.name));
        requests
    }
    
    /// Get download statistics
    pub fn get_stats(&self) -> DownloadManagerStats {
        DownloadManagerStats {
            max_concurrent: self.max_concurrent,
            bandwidth_limit: self.bandwidth_limit,
            retry_attempts: self.retry_attempts,
        }
    }
}

/// Download batch result
#[derive(Debug)]
pub struct DownloadBatch {
    pub results: Vec<DownloadResult>,
    pub duration: Duration,
    pub successful_count: usize,
    pub failed_count: usize,
    pub total_bytes: u64,
}

impl DownloadBatch {
    /// Get successful downloads
    pub fn successful(&self) -> impl Iterator<Item = &DownloadResult> {
        self.results.iter().filter(|r| r.error.is_none())
    }
    
    /// Get failed downloads
    pub fn failed(&self) -> impl Iterator<Item = &DownloadResult> {
        self.results.iter().filter(|r| r.error.is_some())
    }
    
    /// Get download rate in bytes per second
    pub fn download_rate(&self) -> f64 {
        if self.duration.as_secs_f64() > 0.0 {
            self.total_bytes as f64 / self.duration.as_secs_f64()
        } else {
            0.0
        }
    }
}

/// Download manager statistics
#[derive(Debug, Clone)]
pub struct DownloadManagerStats {
    pub max_concurrent: usize,
    pub bandwidth_limit: Option<u64>,
    pub retry_attempts: u32,
}
