use anyhow::{anyhow, Result};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::Instant;
use tracing::{info, warn};

use crate::cache::UltraFastCache;
use crate::cli::{CiArgs, InstallArgs};
use crate::installer::UltraFastInstaller;
use crate::lockfile::Lockfile;
use crate::resolver::UltraFastResolver;
use crate::types::{InstallConfig, RegistryConfig};
use crate::ui::UI;
use crate::utils::registry::RegistryClient;

/// Execute nx install command with ultra-fast performance
pub async fn execute(args: InstallArgs, ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    
    ui.step("🚀 Starting ultra-fast installation...");
    
    // Initialize ultra-fast components
    let registry_config = RegistryConfig::default();
    let registry = Arc::new(RegistryClient::new(registry_config)?);
    let cache_dir = dirs::cache_dir()
        .unwrap_or_else(|| PathBuf::from("."))
        .join("nx");
    let cache = Arc::new(UltraFastCache::new(cache_dir)?);
    
    let install_config = InstallConfig {
        production: args.production,
        dev: !args.production && !args.save_dev,
        save_exact: args.save_exact,
        force: args.force,
        dry_run: args.dry_run,
        optional: !args.no_optional,
        global_path: if args.global {
            Some(dirs::home_dir().unwrap_or_default().join(".nx").join("global"))
        } else {
            None
        },
        ..Default::default()
    };

    let resolver = UltraFastResolver::new(Arc::clone(&registry));
    let installer = UltraFastInstaller::new(Arc::clone(&registry), Arc::clone(&cache), install_config.clone());

    // Determine what to install
    let (dependencies, dev_dependencies) = if args.packages.is_empty() {
        // Install from package.json
        read_package_json_dependencies().await?
    } else {
        // Install specific packages
        let mut deps = HashMap::new();
        for package in &args.packages {
            let (name, version) = crate::utils::parse_package_spec(package);
            deps.insert(name, version.unwrap_or_else(|| "latest".to_string()));
        }
        (deps, HashMap::new())
    };

    if dependencies.is_empty() && dev_dependencies.is_empty() {
        ui.warning("No packages to install");
        return Ok(());
    }

    let total_packages = dependencies.len() + dev_dependencies.len();
    ui.info(&format!("📦 Installing {} packages...", total_packages));

    // Phase 1: Ultra-fast dependency resolution
    let resolution_spinner = ui.create_resolution_spinner("Resolving dependencies...");
    let resolution_start = Instant::now();
    
    let dependency_tree = resolver
        .resolve_dependencies(&dependencies, &dev_dependencies, !install_config.production)
        .await?;
    
    let resolution_time = resolution_start.elapsed();
    resolution_spinner.finish_with_message(&format!(
        "🔗 Resolved {} dependencies in {:.3}s",
        dependency_tree.nodes.len(),
        resolution_time.as_secs_f64()
    ));

    // Phase 2: Ultra-fast parallel installation
    let install_start = Instant::now();
    let summaries = installer.install_packages(&dependency_tree, ui).await?;
    let install_time = install_start.elapsed();

    // Phase 3: Update package.json if needed
    if !args.no_save && !args.packages.is_empty() {
        update_package_json(&args.packages, args.save_dev, args.save_exact).await?;
    }

    // Phase 4: Generate lockfile
    if !install_config.dry_run {
        generate_lockfile(&dependency_tree).await?;
    }

    // Show beautiful summary
    ui.show_install_summary(&summaries);

    let total_time = start_time.elapsed();
    ui.success(&format!(
        "✅ Installed {} packages in {:.3}s (Resolution: {:.3}s, Installation: {:.3}s)",
        summaries.len(),
        total_time.as_secs_f64(),
        resolution_time.as_secs_f64(),
        install_time.as_secs_f64()
    ));

    // Performance validation
    if total_time.as_secs_f64() > 4.0 {
        ui.warning(&format!(
            "⚠️  Installation took {:.2}s (target: <3s). Consider clearing cache or checking network.",
            total_time.as_secs_f64()
        ));
    }

    Ok(())
}

/// Execute nx ci command for clean installs
pub async fn execute_ci(args: CiArgs, ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    
    ui.step("🚀 Starting clean install from lockfile...");

    // Check if lockfile exists
    let lockfile_path = PathBuf::from("package-lock.json");
    if !lockfile_path.exists() {
        return Err(anyhow!("package-lock.json not found. Run 'nx install' first."));
    }

    // Remove existing node_modules
    let node_modules = PathBuf::from("node_modules");
    if node_modules.exists() {
        ui.step("🗑️  Removing existing node_modules...");
        tokio::fs::remove_dir_all(&node_modules).await?;
    }

    // Install from lockfile
    let install_args = InstallArgs {
        packages: Vec::new(),
        save_dev: false,
        global: false,
        save_exact: true,
        no_save: true,
        dry_run: false,
        force: true,
        no_optional: args.no_optional,
        production: args.production,
    };

    execute(install_args, ui).await?;

    let total_time = start_time.elapsed();
    ui.success(&format!("✅ Clean install completed in {:.3}s", total_time.as_secs_f64()));

    Ok(())
}

async fn read_package_json_dependencies() -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    let package_json_path = PathBuf::from("package.json");
    if !package_json_path.exists() {
        return Err(anyhow!("package.json not found. Run 'nx init' to create one."));
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;

    let dependencies = package["dependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    let dev_dependencies = package["devDependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok((dependencies, dev_dependencies))
}

async fn update_package_json(packages: &[String], save_dev: bool, save_exact: bool) -> Result<()> {
    let package_json_path = PathBuf::from("package.json");
    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let mut package: serde_json::Value = serde_json::from_str(&content)?;

    let deps_key = if save_dev { "devDependencies" } else { "dependencies" };
    
    if package[deps_key].is_null() {
        package[deps_key] = serde_json::json!({});
    }

    for package_spec in packages {
        let (name, version) = crate::utils::parse_package_spec(package_spec);
        let version_spec = if save_exact {
            version.unwrap_or_else(|| "latest".to_string())
        } else {
            format!("^{}", version.unwrap_or_else(|| "latest".to_string()))
        };
        
        package[deps_key][name] = serde_json::Value::String(version_spec);
    }

    let updated_content = serde_json::to_string_pretty(&package)?;
    tokio::fs::write(&package_json_path, updated_content).await?;

    Ok(())
}

async fn generate_lockfile(dependency_tree: &crate::types::DependencyTree) -> Result<()> {
    use crate::types::{Lockfile, LockfileEntry};
    
    let mut packages = HashMap::new();
    
    for (_, node) in &dependency_tree.nodes {
        if let Some(resolved) = &node.resolved {
            let entry = LockfileEntry {
                version: resolved.version.clone(),
                resolved: resolved.resolved_url.clone(),
                integrity: resolved.integrity.clone(),
                dependencies: if resolved.dependencies.is_empty() {
                    None
                } else {
                    Some(resolved.dependencies.clone())
                },
                dev: Some(node.dev),
                optional: Some(node.optional),
                peer: Some(node.peer),
            };
            
            packages.insert(format!("{}@{}", resolved.name, resolved.version), entry);
        }
    }

    let lockfile = Lockfile {
        name: None,
        version: None,
        lockfile_version: 3,
        requires: Some(true),
        packages,
        dependencies: None,
    };

    let lockfile_content = serde_json::to_string_pretty(&lockfile)?;
    tokio::fs::write("package-lock.json", lockfile_content).await?;

    Ok(())
}
