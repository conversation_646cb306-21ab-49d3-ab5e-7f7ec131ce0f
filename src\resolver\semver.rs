use anyhow::{Result, Context};
use semver::{Version, VersionReq};
use std::cmp::Ordering;
use tracing::{debug, warn};

/// Semantic version resolver
#[derive(Debug, Clone)]
pub struct SemverResolver {
    /// Available versions for packages
    available_versions: std::collections::HashMap<String, Vec<Version>>,
}

impl SemverResolver {
    pub fn new() -> Self {
        Self {
            available_versions: std::collections::HashMap::new(),
        }
    }
    
    /// Add available versions for a package
    pub fn add_versions(&mut self, package: &str, versions: Vec<Version>) {
        let mut sorted_versions = versions;
        sorted_versions.sort_by(|a, b| b.cmp(a)); // Sort in descending order
        self.available_versions.insert(package.to_string(), sorted_versions);
    }
    
    /// Resolve a version requirement to a specific version
    pub fn resolve_version(&self, package: &str, requirement: &str) -> Result<Option<Version>> {
        let versions = self.available_versions.get(package)
            .context("Package not found in available versions")?;
        
        if versions.is_empty() {
            return Ok(None);
        }
        
        // Parse the version requirement
        let req = self.parse_requirement(requirement)?;
        
        // Find the best matching version
        for version in versions {
            if req.matches(version) {
                debug!("Resolved {}@{} -> {}", package, requirement, version);
                return Ok(Some(version.clone()));
            }
        }
        
        warn!("No version found for {}@{}", package, requirement);
        Ok(None)
    }
    
    /// Parse a version requirement string
    fn parse_requirement(&self, requirement: &str) -> Result<VersionReq> {
        // Handle npm-style version ranges
        let normalized = self.normalize_requirement(requirement);
        
        VersionReq::parse(&normalized)
            .context("Failed to parse version requirement")
    }
    
    /// Normalize npm-style version requirements to semver format
    fn normalize_requirement(&self, requirement: &str) -> String {
        let req = requirement.trim();
        
        // Handle common npm patterns
        match req {
            "latest" => "*".to_string(),
            "" => "*".to_string(),
            _ if req.starts_with("^") => req.to_string(),
            _ if req.starts_with("~") => req.to_string(),
            _ if req.starts_with(">=") => req.to_string(),
            _ if req.starts_with("<=") => req.to_string(),
            _ if req.starts_with(">") => req.to_string(),
            _ if req.starts_with("<") => req.to_string(),
            _ if req.starts_with("=") => req[1..].to_string(),
            _ if req.contains(" - ") => {
                // Range like "1.0.0 - 2.0.0"
                let parts: Vec<&str> = req.split(" - ").collect();
                if parts.len() == 2 {
                    format!(">={} <={}", parts[0], parts[1])
                } else {
                    req.to_string()
                }
            }
            _ if req.contains("||") => req.to_string(),
            _ => {
                // Assume it's an exact version or add caret
                if Version::parse(req).is_ok() {
                    format!("^{}", req)
                } else {
                    req.to_string()
                }
            }
        }
    }
    
    /// Find the highest version that satisfies a requirement
    pub fn find_highest_compatible(
        &self,
        package: &str,
        requirement: &str,
    ) -> Result<Option<Version>> {
        self.resolve_version(package, requirement)
    }
    
    /// Find the lowest version that satisfies a requirement
    pub fn find_lowest_compatible(
        &self,
        package: &str,
        requirement: &str,
    ) -> Result<Option<Version>> {
        let versions = self.available_versions.get(package)
            .context("Package not found in available versions")?;
        
        if versions.is_empty() {
            return Ok(None);
        }
        
        let req = self.parse_requirement(requirement)?;
        
        // Find the lowest matching version (reverse iteration)
        for version in versions.iter().rev() {
            if req.matches(version) {
                debug!("Resolved {}@{} -> {} (lowest)", package, requirement, version);
                return Ok(Some(version.clone()));
            }
        }
        
        Ok(None)
    }
    
    /// Check if two version requirements are compatible
    pub fn are_compatible(&self, req1: &str, req2: &str) -> Result<bool> {
        let parsed_req1 = self.parse_requirement(req1)?;
        let parsed_req2 = self.parse_requirement(req2)?;
        
        // TODO: Implement proper compatibility checking
        // For now, just check if they're the same
        Ok(req1 == req2)
    }
    
    /// Merge multiple version requirements into one
    pub fn merge_requirements(&self, requirements: &[String]) -> Result<String> {
        if requirements.is_empty() {
            return Ok("*".to_string());
        }
        
        if requirements.len() == 1 {
            return Ok(requirements[0].clone());
        }
        
        // TODO: Implement proper requirement merging
        // For now, just return the first one
        Ok(requirements[0].clone())
    }
}
