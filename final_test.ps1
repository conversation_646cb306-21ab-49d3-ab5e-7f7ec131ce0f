# Final Comprehensive Test Suite for nx Package Manager
# Tests all implemented functionality and validates production readiness

Write-Host "nx Package Manager - Final Comprehensive Test Suite" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan

$errors = 0
$tests = 0
$performance_results = @()

function Test-Command {
    param(
        [string]$description,
        [string]$command,
        [int]$maxSeconds = 30,
        [bool]$shouldSucceed = $true
    )
    
    $script:tests++
    Write-Host "`nTest $tests`: $description" -ForegroundColor Yellow
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $result = Invoke-Expression $command 2>&1
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        if ($shouldSucceed) {
            if ($duration -le $maxSeconds) {
                Write-Host "PASS: $description ($([math]::Round($duration, 2))s)" -ForegroundColor Green
                return $duration
            } else {
                Write-Host "SLOW: $description took $([math]::Round($duration, 2))s (target: ${maxSeconds}s)" -ForegroundColor Yellow
                return $duration
            }
        } else {
            Write-Host "FAIL: Command should have failed but succeeded" -ForegroundColor Red
            $script:errors++
            return $duration
        }
    }
    catch {
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        if ($shouldSucceed) {
            Write-Host "FAIL: $description" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            $script:errors++
        } else {
            Write-Host "PASS: $description (expected failure)" -ForegroundColor Green
        }
        return $duration
    }
}

function Clean-Environment {
    Remove-Item -Recurse -Force node_modules, .bin, nx-lock.json, package.json -ErrorAction SilentlyContinue
}

# Build the project first
Write-Host "`nBuilding nx package manager..." -ForegroundColor Cyan
try {
    $buildResult = & cargo build --release 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful!" -ForegroundColor Green
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        Write-Host $buildResult
        exit 1
    }
} catch {
    Write-Host "Build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Core functionality tests
Write-Host "`nCore Functionality Tests" -ForegroundColor Cyan
Test-Command "Version command" ".\target\release\nx.exe --version" 5
Test-Command "Help command" ".\target\release\nx.exe --help" 5

# Project initialization tests
Write-Host "`nProject Initialization Tests" -ForegroundColor Cyan
Clean-Environment

# Test basic project initialization
Test-Command "Basic project init" ".\target\release\nx.exe init --name test-basic" 10

# Verify package.json was created
if (Test-Path "package.json") {
    Write-Host "PASS: package.json created" -ForegroundColor Green
    $tests++
} else {
    Write-Host "FAIL: package.json not created" -ForegroundColor Red
    $errors++
    $tests++
}

# Performance tests - Package installations
Write-Host "`nPackage Installation Performance Tests" -ForegroundColor Cyan

Clean-Environment

# Test various package types
$testPackages = @(
    @{Name="lodash"; MaxTime=5; Description="Utility library"},
    @{Name="axios"; MaxTime=8; Description="HTTP client"},
    @{Name="express"; MaxTime=15; Description="Web framework"},
    @{Name="typescript"; MaxTime=10; Description="TypeScript compiler"},
    @{Name="@types/node"; MaxTime=8; Description="Scoped package"}
)

foreach ($pkg in $testPackages) {
    Clean-Environment
    $duration = Test-Command "Install $($pkg.Name) ($($pkg.Description))" ".\target\release\nx.exe install $($pkg.Name)" $pkg.MaxTime
    $performance_results += @{Package=$pkg.Name; Type="Single Install"; Duration=$duration}
    
    # Verify installation
    if (Test-Path "node_modules/$($pkg.Name)") {
        Write-Host "PASS: $($pkg.Name) installed correctly" -ForegroundColor Green
        $tests++
    } else {
        Write-Host "FAIL: $($pkg.Name) not found in node_modules" -ForegroundColor Red
        $errors++
        $tests++
    }
}

# Test package.json installation
Write-Host "`nPackage.json Installation Test" -ForegroundColor Cyan
Clean-Environment

# Use the test package.json
Copy-Item "tests/package.json" "package.json" -Force

$duration = Test-Command "Install from package.json" ".\target\release\nx.exe install" 30
$performance_results += @{Package="package.json"; Type="Multi Install"; Duration=$duration}

# Verify all packages were installed
$expectedPackages = @("axios", "ejs", "express", "lodash", "react")
foreach ($pkg in $expectedPackages) {
    if (Test-Path "node_modules/$pkg") {
        Write-Host "PASS: $pkg installed from package.json" -ForegroundColor Green
        $tests++
    } else {
        Write-Host "FAIL: $pkg not found after package.json install" -ForegroundColor Red
        $errors++
        $tests++
    }
}

# Binary linking test
Write-Host "`nBinary Linking Tests" -ForegroundColor Cyan
Clean-Environment
Test-Command "Install package with binaries (typescript)" ".\target\release\nx.exe install typescript" 10

if (Test-Path ".bin") {
    Write-Host "PASS: .bin directory created" -ForegroundColor Green
    $tests++
    
    if (Test-Path ".bin/tsc.cmd") {
        Write-Host "PASS: TypeScript binary linked" -ForegroundColor Green
        $tests++
        
        # Test binary execution
        Test-Command "Execute TypeScript binary" ".\.bin\tsc.cmd --version" 5
    } else {
        Write-Host "FAIL: TypeScript binary not found" -ForegroundColor Red
        $errors++
        $tests++
    }
} else {
    Write-Host "FAIL: .bin directory not created" -ForegroundColor Red
    $errors++
    $tests++
}

# Cache performance test
Write-Host "`nCache Performance Test" -ForegroundColor Cyan
Clean-Environment

# Install package first time
$duration1 = Test-Command "First install (no cache)" ".\target\release\nx.exe install lodash" 5
$performance_results += @{Package="lodash"; Type="First Install"; Duration=$duration1}

# Remove node_modules but keep cache
Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue

# Install same package again (should be faster from cache)
$duration2 = Test-Command "Second install (cached)" ".\target\release\nx.exe install lodash" 3
$performance_results += @{Package="lodash"; Type="Cached Install"; Duration=$duration2}

if ($duration2 -lt $duration1) {
    Write-Host "PASS: Cached install is faster ($([math]::Round($duration2, 2))s vs $([math]::Round($duration1, 2))s)" -ForegroundColor Green
    $tests++
} else {
    Write-Host "WARN: Cached install not faster than first install" -ForegroundColor Yellow
    $tests++
}

# Performance summary
Write-Host "`nPerformance Summary" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

$performance_results | ForEach-Object {
    $status = if ($_.Duration -le 3) { "EXCELLENT" } 
              elseif ($_.Duration -le 5) { "GOOD" }
              elseif ($_.Duration -le 10) { "ACCEPTABLE" }
              else { "SLOW" }
    
    Write-Host "$($_.Package) ($($_.Type)): $([math]::Round($_.Duration, 2))s - $status"
}

$validTimes = $performance_results | Where-Object { $_.Duration -lt 999 }
if ($validTimes.Count -gt 0) {
    $avgTime = ($validTimes | Measure-Object -Property Duration -Average).Average
    Write-Host "`nAverage Installation Time: $([math]::Round($avgTime, 2))s" -ForegroundColor White
}

# Final results
Write-Host "`nFinal Test Results" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "Total Tests: $tests" -ForegroundColor White
Write-Host "Passed: $($tests - $errors)" -ForegroundColor Green
Write-Host "Failed: $errors" -ForegroundColor Red

if ($errors -eq 0 -and $avgTime -le 5) {
    Write-Host "`nSUCCESS: nx package manager is production-ready!" -ForegroundColor Green
    Write-Host "- All tests passed" -ForegroundColor Green
    Write-Host "- Average installation time: $([math]::Round($avgTime, 2))s (Target: under 5s)" -ForegroundColor Green
    Write-Host "- Full npm ecosystem compatibility achieved" -ForegroundColor Green
    Write-Host "- Ultra-fast performance validated" -ForegroundColor Green
    exit 0
} elseif ($errors -eq 0) {
    Write-Host "`nSUCCESS: All tests passed!" -ForegroundColor Green
    Write-Host "Average installation time: $([math]::Round($avgTime, 2))s" -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "`nFAILURE: $errors test(s) failed" -ForegroundColor Red
    Write-Host "Please fix issues before production release" -ForegroundColor Red
    exit 1
}
