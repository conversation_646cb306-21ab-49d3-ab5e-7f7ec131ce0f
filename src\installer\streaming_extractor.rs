use anyhow::{Result, Context};
use flate2::read::GzDecoder;
use memmap2::Mmap;
use std::io::Cursor;
use std::path::{Path, PathBuf};
use tar::Archive;
use tokio::fs;
use tracing::{debug, info};
use std::sync::Arc;

use crate::utils::FsUtils;
use crate::ui::UI;

/// Ultra-fast streaming extractor with memory-mapped support
#[derive(Debug, Clone)]
pub struct StreamingExtractor {
    extract_dir: PathBuf,
}

impl StreamingExtractor {
    pub fn new(extract_dir: PathBuf) -> Self {
        Self { extract_dir }
    }
    
    /// Extract from memory-mapped data (zero-copy, ultra-fast)
    pub async fn extract_from_mmap(
        &self,
        mmap: Arc<Mmap>,
        name: &str,
        version: &str,
        ui: &UI,
    ) -> Result<PathBuf> {
        let final_path = self.get_package_path(name);
        
        // Create extraction spinner
        let spinner = ui.create_extraction_spinner(name);
        
        // Remove existing directory if it exists
        if final_path.exists() {
            fs::remove_dir_all(&final_path).await
                .context("Failed to remove existing package directory")?;
        }
        
        // Ensure parent directory exists
        if let Some(parent) = final_path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }
        
        info!("Extracting {}@{} from memory-mapped cache (zero-copy)", name, version);
        
        // Extract in a blocking task using memory-mapped data
        let final_path_clone = final_path.clone();
        let mmap_clone = mmap.clone();
        tokio::task::spawn_blocking(move || {
            Self::extract_tarball_from_mmap(&mmap_clone, &final_path_clone)
        }).await??;
        
        spinner.finish_with_message(format!("✓ Extracted {} (zero-copy)", name));
        
        debug!("Extracted {}@{} from memory-mapped cache to {}", name, version, final_path.display());
        Ok(final_path)
    }
    
    /// Extract from streaming data (while downloading)
    pub async fn extract_streaming(
        &self,
        data: &[u8],
        name: &str,
        version: &str,
        ui: &UI,
    ) -> Result<PathBuf> {
        let final_path = self.get_package_path(name);
        
        // Create extraction spinner
        let spinner = ui.create_extraction_spinner(name);
        
        // Remove existing directory if it exists
        if final_path.exists() {
            fs::remove_dir_all(&final_path).await
                .context("Failed to remove existing package directory")?;
        }
        
        // Ensure parent directory exists
        if let Some(parent) = final_path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }
        
        info!("Streaming extraction: {}@{}", name, version);
        
        // Extract in a blocking task
        let final_path_clone = final_path.clone();
        let data_clone = data.to_vec();
        tokio::task::spawn_blocking(move || {
            Self::extract_tarball_from_data(&data_clone, &final_path_clone)
        }).await??;
        
        spinner.finish_with_message(format!("✓ Extracted {} (streaming)", name));
        
        debug!("Streaming extracted {}@{} to {}", name, version, final_path.display());
        Ok(final_path)
    }
    
    /// Extract tarball from memory-mapped data (blocking operation)
    fn extract_tarball_from_mmap(mmap: &Mmap, extract_path: &Path) -> Result<()> {
        let cursor = Cursor::new(&**mmap);
        let decoder = GzDecoder::new(cursor);
        let mut archive = Archive::new(decoder);
        
        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;
            
            // Skip the top-level package directory (usually "package/")
            let relative_path = if let Ok(stripped) = path.strip_prefix("package") {
                stripped
            } else {
                path.as_ref()
            };
            
            let target_path = extract_path.join(relative_path);
            
            // Ensure the parent directory exists
            if let Some(parent) = target_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            // Extract the entry
            entry.unpack(&target_path)?;
        }
        
        Ok(())
    }
    
    /// Extract tarball from data (blocking operation)
    fn extract_tarball_from_data(data: &[u8], extract_path: &Path) -> Result<()> {
        let cursor = Cursor::new(data);
        let decoder = GzDecoder::new(cursor);
        let mut archive = Archive::new(decoder);
        
        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;
            
            // Skip the top-level package directory (usually "package/")
            let relative_path = if let Ok(stripped) = path.strip_prefix("package") {
                stripped
            } else {
                path.as_ref()
            };
            
            let target_path = extract_path.join(relative_path);
            
            // Ensure the parent directory exists
            if let Some(parent) = target_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            // Extract the entry
            entry.unpack(&target_path)?;
        }
        
        Ok(())
    }
    
    /// Get the final installation path for a package
    fn get_package_path(&self, name: &str) -> PathBuf {
        if name.starts_with('@') {
            // Scoped package
            let parts: Vec<&str> = name.splitn(2, '/').collect();
            if parts.len() == 2 {
                self.extract_dir
                    .join("node_modules")
                    .join(parts[0])
                    .join(parts[1])
            } else {
                self.extract_dir.join("node_modules").join(name)
            }
        } else {
            self.extract_dir.join("node_modules").join(name)
        }
    }
}
