pub mod client;
pub mod types;
pub mod auth;
pub mod manager;

use anyhow::{Result, Context};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use url::Url;

pub use client::*;
pub use types::*;
pub use auth::*;
pub use manager::*;

/// Registry client for communicating with npm registries
#[derive(Debug, Clone)]
pub struct Registry {
    client: Client,
    base_url: Url,
    auth_token: Option<String>,
}

impl Registry {
    pub fn new(registry_url: &str) -> Result<Self> {
        let client = Client::builder()
            .user_agent("nx/0.1.0")
            .gzip(true)
            .timeout(std::time::Duration::from_secs(60))
            .connect_timeout(std::time::Duration::from_secs(30))
            .build()?;
        
        let base_url = Url::parse(registry_url)?;
        
        Ok(Self {
            client,
            base_url,
            auth_token: None,
        })
    }
    
    pub fn with_auth(mut self, token: String) -> Self {
        self.auth_token = Some(token);
        self
    }
    
    /// Get package metadata from npm registry with full dependency information
    pub async fn get_package_metadata(&self, name: &str) -> Result<PackageMetadata> {
        let url = format!("{}{}", self.base_url.as_str().trim_end_matches('/'), format!("/{}", name));

        let mut request = self.client.get(&url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to fetch package metadata")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Package '{}' not found (status: {})", name, response.status()));
        }

        let package_data: serde_json::Value = response.json().await
            .context("Failed to parse package metadata")?;

        // Extract package information
        let description = package_data["description"].as_str().unwrap_or("").to_string();
        let latest_version = package_data["dist-tags"]["latest"].as_str().unwrap_or("1.0.0").to_string();

        let mut versions = HashMap::new();
        if let Some(versions_obj) = package_data["versions"].as_object() {
            for (version, version_data) in versions_obj {
                // Parse dependencies
                let dependencies = version_data["dependencies"]
                    .as_object()
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Parse peer dependencies
                let peer_dependencies = version_data["peerDependencies"]
                    .as_object()
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Parse optional dependencies
                let optional_dependencies = version_data["optionalDependencies"]
                    .as_object()
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Extract dist information
                let dist = version_data["dist"].as_object();
                let tarball = dist
                    .and_then(|d| d["tarball"].as_str())
                    .unwrap_or(&format!("https://registry.npmjs.org/{}/-/{}-{}.tgz", name, name, version))
                    .to_string();

                let integrity = dist
                    .and_then(|d| d["integrity"].as_str())
                    .map(|s| s.to_string());

                let shasum = dist
                    .and_then(|d| d["shasum"].as_str())
                    .map(|s| s.to_string());

                versions.insert(version.clone(), VersionMetadata {
                    version: version.clone(),
                    name: name.to_string(),
                    description: None,
                    main: None,
                    dependencies,
                    dev_dependencies: HashMap::new(),
                    peer_dependencies,
                    optional_dependencies,
                    dist: DistInfo {
                        tarball,
                        integrity,
                        shasum,
                    },
                });
            }
        }

        Ok(PackageMetadata {
            name: name.to_string(),
            description,
            latest_version,
            versions,
        })
    }

    /// Get specific version metadata
    pub async fn get_version_metadata(&self, name: &str, version: &str) -> Result<VersionMetadata> {
        let url = format!("{}/{}/{}", self.base_url, name, version);

        let mut request = self.client.get(&url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to fetch version metadata")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Registry returned status: {}", response.status()));
        }

        let metadata: VersionMetadata = response.json().await
            .context("Failed to parse version metadata")?;

        Ok(metadata)
    }

    /// Download package tarball
    pub async fn download_tarball(&self, url: &str) -> Result<bytes::Bytes> {
        let mut request = self.client.get(url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to download tarball")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Download failed with status: {}", response.status()));
        }

        response.bytes().await
            .context("Failed to read tarball data")
    }
}
