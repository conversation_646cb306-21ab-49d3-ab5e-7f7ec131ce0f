pub mod client;
pub mod types;
pub mod auth;
pub mod manager;

use anyhow::{Result, Context};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use url::Url;

pub use client::*;
pub use types::*;
pub use auth::*;
pub use manager::*;

/// Registry client for communicating with npm registries
#[derive(Debug, Clone)]
pub struct Registry {
    client: Client,
    base_url: Url,
    auth_token: Option<String>,
}

impl Registry {
    pub fn new(registry_url: &str) -> Result<Self> {
        let client = Client::builder()
            .user_agent("nx/0.1.0")
            .gzip(true)
            .timeout(std::time::Duration::from_secs(60))
            .connect_timeout(std::time::Duration::from_secs(30))
            .build()?;
        
        let base_url = Url::parse(registry_url)?;
        
        Ok(Self {
            client,
            base_url,
            auth_token: None,
        })
    }
    
    pub fn with_auth(mut self, token: String) -> Self {
        self.auth_token = Some(token);
        self
    }
    
    /// Get package metadata from npm registry with full dependency information
    pub async fn get_package_metadata(&self, name: &str) -> Result<PackageMetadata> {
        let url = format!("{}{}", self.base_url.as_str().trim_end_matches('/'), format!("/{}", name));

        let mut request = self.client.get(&url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to fetch package metadata")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Package '{}' not found (status: {})", name, response.status()));
        }

        let package_data: serde_json::Value = response.json().await
            .context("Failed to parse package metadata")?;

        // Extract package information with safe access
        let description = package_data.get("description")
            .and_then(|d| d.as_str())
            .unwrap_or("")
            .to_string();

        let latest_version = package_data.get("dist-tags")
            .and_then(|dt| dt.get("latest"))
            .and_then(|l| l.as_str())
            .unwrap_or("1.0.0")
            .to_string();

        let mut versions = HashMap::new();
        if let Some(versions_obj) = package_data["versions"].as_object() {
            for (version, version_data) in versions_obj {
                // Parse dependencies with safe access
                let dependencies = version_data.get("dependencies")
                    .and_then(|d| d.as_object())
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Parse peer dependencies with safe access
                let peer_dependencies = version_data.get("peerDependencies")
                    .and_then(|d| d.as_object())
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Parse optional dependencies with safe access
                let optional_dependencies = version_data.get("optionalDependencies")
                    .and_then(|d| d.as_object())
                    .map(|deps| {
                        deps.iter()
                            .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                            .collect()
                    })
                    .unwrap_or_default();

                // Extract dist information with safe access
                let dist = version_data.get("dist").and_then(|d| d.as_object());
                let tarball = dist
                    .and_then(|d| d.get("tarball"))
                    .and_then(|t| t.as_str())
                    .unwrap_or(&format!("https://registry.npmjs.org/{}/-/{}-{}.tgz", name, name, version))
                    .to_string();

                let integrity = dist
                    .and_then(|d| d.get("integrity"))
                    .and_then(|i| i.as_str())
                    .map(|s| s.to_string());

                let shasum = dist
                    .and_then(|d| d.get("shasum"))
                    .and_then(|s| s.as_str())
                    .map(|s| s.to_string());

                versions.insert(version.clone(), VersionMetadata {
                    version: version.clone(),
                    name: name.to_string(),
                    description: None,
                    main: None,
                    dependencies,
                    dev_dependencies: HashMap::new(),
                    peer_dependencies,
                    optional_dependencies,
                    dist: DistInfo {
                        tarball,
                        integrity,
                        shasum,
                    },
                });
            }
        }

        Ok(PackageMetadata {
            name: name.to_string(),
            description,
            latest_version,
            versions,
        })
    }

    /// Get specific version metadata
    pub async fn get_version_metadata(&self, name: &str, version: &str) -> Result<VersionMetadata> {
        let url = format!("{}/{}/{}", self.base_url, name, version);

        let mut request = self.client.get(&url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to fetch version metadata")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Registry returned status: {}", response.status()));
        }

        let metadata: VersionMetadata = response.json().await
            .context("Failed to parse version metadata")?;

        Ok(metadata)
    }

    /// Download package tarball
    pub async fn download_tarball(&self, url: &str) -> Result<bytes::Bytes> {
        let mut request = self.client.get(url);

        // Add authentication if available
        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        let response = request.send().await
            .context("Failed to download tarball")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Download failed with status: {}", response.status()));
        }

        response.bytes().await
            .context("Failed to read tarball data")
    }

    /// Search for packages in the npm registry
    pub async fn search_packages(&self, query: &str, limit: usize) -> Result<Vec<serde_json::Value>> {
        let search_url = format!("https://registry.npmjs.org/-/v1/search?text={}&size={}",
                                urlencoding::encode(query), limit);

        let response = self.client
            .get(&search_url)
            .send()
            .await
            .context("Failed to search packages")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Search request failed: {}", response.status()));
        }

        let search_result: serde_json::Value = response.json().await
            .context("Failed to parse search response")?;

        let packages = search_result
            .get("objects")
            .and_then(|objects| objects.as_array())
            .map(|objects| {
                objects.iter()
                    .filter_map(|obj| obj.get("package"))
                    .cloned()
                    .collect()
            })
            .unwrap_or_default();

        Ok(packages)
    }
}
