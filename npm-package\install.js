#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

const BINARY_NAME = 'nx';
const GITHUB_REPO = 'nx-package-manager/nx';
const VERSION = require('./package.json').version;

// Platform and architecture detection
function getPlatform() {
  const platform = process.platform;
  const arch = process.arch;
  
  const platformMap = {
    'win32': 'windows',
    'darwin': 'macos',
    'linux': 'linux'
  };
  
  const archMap = {
    'x64': 'x86_64',
    'arm64': 'aarch64'
  };
  
  const mappedPlatform = platformMap[platform];
  const mappedArch = archMap[arch];
  
  if (!mappedPlatform || !mappedArch) {
    throw new Error(`Unsupported platform: ${platform}-${arch}`);
  }
  
  return `${mappedPlatform}-${mappedArch}`;
}

// Get binary filename
function getBinaryName() {
  const platform = getPlatform();
  const extension = process.platform === 'win32' ? '.exe' : '';
  return `${BINARY_NAME}-${platform}${extension}`;
}

// Download binary from GitHub releases
async function downloadBinary() {
  const platform = getPlatform();
  const binaryName = getBinaryName();
  const downloadUrl = `https://github.com/${GITHUB_REPO}/releases/download/v${VERSION}/${binaryName}`;
  
  console.log(`Downloading nx binary for ${platform}...`);
  console.log(`URL: ${downloadUrl}`);
  
  const binDir = path.join(__dirname, 'bin');
  if (!fs.existsSync(binDir)) {
    fs.mkdirSync(binDir, { recursive: true });
  }
  
  const binaryPath = path.join(binDir, BINARY_NAME + (process.platform === 'win32' ? '.exe' : ''));
  
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(binaryPath);
    
    https.get(downloadUrl, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // Follow redirect
        https.get(response.headers.location, (redirectResponse) => {
          if (redirectResponse.statusCode === 200) {
            redirectResponse.pipe(file);
            file.on('finish', () => {
              file.close();
              // Make binary executable on Unix systems
              if (process.platform !== 'win32') {
                fs.chmodSync(binaryPath, '755');
              }
              console.log('✓ nx binary downloaded successfully');
              resolve(binaryPath);
            });
          } else {
            reject(new Error(`Failed to download binary: ${redirectResponse.statusCode}`));
          }
        }).on('error', reject);
      } else if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          // Make binary executable on Unix systems
          if (process.platform !== 'win32') {
            fs.chmodSync(binaryPath, '755');
          }
          console.log('✓ nx binary downloaded successfully');
          resolve(binaryPath);
        });
      } else {
        reject(new Error(`Failed to download binary: ${response.statusCode}`));
      }
    }).on('error', reject);
    
    file.on('error', (err) => {
      fs.unlink(binaryPath, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Fallback: try to build from source if Rust is available
function buildFromSource() {
  console.log('Attempting to build from source...');
  
  try {
    // Check if Rust is installed
    execSync('rustc --version', { stdio: 'ignore' });
    execSync('cargo --version', { stdio: 'ignore' });
    
    console.log('Building nx from source...');
    execSync('cargo build --release', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    const sourceBinary = path.join(__dirname, '..', 'target', 'release', BINARY_NAME + (process.platform === 'win32' ? '.exe' : ''));
    const targetBinary = path.join(__dirname, 'bin', BINARY_NAME + (process.platform === 'win32' ? '.exe' : ''));
    
    if (!fs.existsSync(path.dirname(targetBinary))) {
      fs.mkdirSync(path.dirname(targetBinary), { recursive: true });
    }
    
    fs.copyFileSync(sourceBinary, targetBinary);
    
    if (process.platform !== 'win32') {
      fs.chmodSync(targetBinary, '755');
    }
    
    console.log('✓ nx built from source successfully');
    return targetBinary;
  } catch (error) {
    throw new Error('Failed to build from source. Please install Rust or download a pre-built binary.');
  }
}

// Main installation function
async function install() {
  try {
    console.log('Installing nx package manager...');
    
    // Try to download pre-built binary first
    try {
      await downloadBinary();
    } catch (downloadError) {
      console.warn('Failed to download pre-built binary:', downloadError.message);
      console.log('Falling back to building from source...');
      
      try {
        buildFromSource();
      } catch (buildError) {
        console.error('Installation failed:', buildError.message);
        console.error('\nPlease try one of the following:');
        console.error('1. Install Rust: https://rustup.rs/');
        console.error('2. Download a pre-built binary from: https://github.com/nx-package-manager/nx/releases');
        process.exit(1);
      }
    }
    
    console.log('\n🚀 nx package manager installed successfully!');
    console.log('Run "nx --help" to get started.');
    
  } catch (error) {
    console.error('Installation failed:', error.message);
    process.exit(1);
  }
}

// Run installation if this script is executed directly
if (require.main === module) {
  install();
}

module.exports = { install, getPlatform, getBinaryName };
