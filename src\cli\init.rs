use anyhow::{Result, Context};
use clap::Args;
use colored::*;
use serde_json::{json, <PERSON>};
use std::fs;
use std::path::Path;
use tracing::{info, warn};

use crate::ui::UI;

/// Initialize a new project with templates
#[derive(Debug, Args)]
pub struct InitArgs {
    /// Project name
    pub name: Option<String>,

    /// Project template
    #[arg(short, long, default_value = "basic")]
    pub template: String,

    /// Use package.toml instead of package.json
    #[arg(long)]
    pub toml: bool,

    /// Skip dependency installation
    #[arg(long)]
    pub skip_install: bool,

    /// Force initialization in non-empty directory
    #[arg(short, long)]
    pub force: bool,
}

pub async fn init(args: InitArgs) -> Result<()> {
    let ui = UI::new();
    let current_dir = std::env::current_dir()?;

    let project_name = args.name.unwrap_or_else(|| {
        current_dir
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("my-project")
            .to_string()
    });

    // Check if directory is empty (unless force is used)
    if !args.force && is_directory_not_empty(&current_dir)? {
        ui.warning("Directory is not empty.");
        ui.info("Use --force to initialize anyway, or run in an empty directory.");
        ui.info(&format!("Current directory: {}", current_dir.display()));
        return Ok(());
    }

    ui.info(&format!("🚀 Initializing {} project: {}", args.template, project_name));

    // Create project based on template
    match args.template.as_str() {
        "basic" => create_basic_project(&project_name, args.toml).await?,
        "express" => create_express_project(&project_name).await?,
        "react" => create_react_project(&project_name).await?,
        "next" => create_next_project(&project_name).await?,
        "typescript" => create_typescript_project(&project_name).await?,
        "vue" => create_vue_project(&project_name).await?,
        "angular" => create_angular_project(&project_name).await?,
        _ => {
            ui.error(&format!("Unknown template: {}", args.template));
            ui.info("Available templates: basic, express, react, next, typescript, vue, angular");
            return Ok(());
        }
    }

    ui.success("Project initialized successfully! 🎉");

    // Install dependencies unless skipped
    if !args.skip_install {
        ui.info("Installing dependencies...");

        let cache_dir = current_dir.join(".nx-cache");
        let installer = crate::installer::Installer::new(cache_dir, current_dir, 50);

        // Read the created package.json and install dependencies
        if let Ok(package_json) = crate::package::PackageJson::read("package.json").await {
            let mut install_packages = Vec::new();
            let registry = crate::registry::Registry::new();

            // Collect all dependencies
            for (name, spec) in package_json.dependencies.iter().chain(package_json.dev_dependencies.iter()) {
                if let Ok(metadata) = registry.get_package_metadata(name).await {
                    if let Some(version_info) = metadata.versions.get(&metadata.latest_version) {
                        install_packages.push(crate::installer::PackageInstallInfo {
                            name: name.clone(),
                            version: metadata.latest_version.clone(),
                            tarball_url: version_info.dist.tarball.clone(),
                            integrity: version_info.dist.integrity.clone(),
                        });
                    }
                }
            }

            if !install_packages.is_empty() {
                installer.install_packages(install_packages, &ui).await?;
            }
        }
    }

    // Show next steps
    println!();
    ui.info("Next steps:");
    match args.template.as_str() {
        "express" => {
            println!("  {} nx run dev          # Start development server", "→".blue());
            println!("  {} nx run start        # Start production server", "→".blue());
        }
        "react" => {
            println!("  {} nx run dev          # Start development server", "→".blue());
            println!("  {} nx run build        # Build for production", "→".blue());
        }
        "next" => {
            println!("  {} nx run dev          # Start Next.js development", "→".blue());
            println!("  {} nx run build        # Build for production", "→".blue());
        }
        _ => {
            println!("  {} nx install          # Install dependencies", "→".blue());
            println!("  {} nx run dev          # Start development", "→".blue());
        }
    }

    Ok(())
}

fn is_directory_not_empty(dir: &Path) -> Result<bool> {
    let entries = fs::read_dir(dir)?;
    Ok(entries.count() > 0)
}

async fn create_basic_project(name: &str, use_toml: bool) -> Result<()> {
    if use_toml {
        create_package_toml(name).await?;
    } else {
        create_basic_package_json(name).await?;
    }

    create_basic_structure().await?;
    Ok(())
}

async fn create_express_project(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "Express.js server application",
        "main": "server.js",
        "scripts": {
            "start": "node server.js",
            "dev": "nodemon server.js",
            "test": "jest"
        },
        "dependencies": {
            "express": "^4.18.2",
            "cors": "^2.8.5",
            "helmet": "^7.0.0",
            "morgan": "^1.10.0"
        },
        "devDependencies": {
            "nodemon": "^3.0.1",
            "jest": "^29.5.0"
        },
        "keywords": ["express", "server", "api"],
        "author": "",
        "license": "ISC"
    });

    fs::write("package.json", serde_json::to_string_pretty(&package_json)?)?;

    // Create server.js
    let server_js = r#"const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to your Express.js server!',
        timestamp: new Date().toISOString()
    });
});

app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', uptime: process.uptime() });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
});

module.exports = app;
"#;

    fs::write("server.js", server_js)?;

    // Create routes directory
    fs::create_dir_all("routes")?;
    fs::write("routes/index.js", "// Add your routes here\n")?;

    // Create middleware directory
    fs::create_dir_all("middleware")?;
    fs::write("middleware/auth.js", "// Add authentication middleware here\n")?;

    // Create .env.example
    fs::write(".env.example", "PORT=3000\nNODE_ENV=development\n")?;

    // Create README
    let readme = format!(r#"# {}

Express.js server application

## Getting Started

1. Install dependencies:
   ```bash
   nx install
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

3. Start development server:
   ```bash
   nx run dev
   ```

4. Start production server:
   ```bash
   nx run start
   ```

## API Endpoints

- `GET /` - Welcome message
- `GET /api/health` - Health check

## Project Structure

```
{}
├── server.js          # Main server file
├── routes/            # Route handlers
├── middleware/        # Custom middleware
├── .env.example       # Environment variables template
└── package.json       # Dependencies and scripts
```
"#, name, name);

    fs::write("README.md", readme)?;

    Ok(())
}

async fn create_basic_package_json(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "",
        "main": "index.js",
        "scripts": {
            "test": "echo \"Error: no test specified\" && exit 1",
            "dev": "node index.js",
            "build": "echo \"Add build script here\"",
            "start": "node index.js"
        },
        "keywords": [],
        "author": "",
        "license": "ISC",
        "dependencies": {},
        "devDependencies": {}
    });
    
    let content = serde_json::to_string_pretty(&package_json)?;
    fs::write("package.json", content)
        .context("Failed to write package.json")?;
    
    Ok(())
}

async fn create_react_project(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "React application",
        "main": "src/index.js",
        "scripts": {
            "start": "react-scripts start",
            "build": "react-scripts build",
            "test": "react-scripts test",
            "eject": "react-scripts eject",
            "dev": "react-scripts start"
        },
        "dependencies": {
            "react": "^18.2.0",
            "react-dom": "^18.2.0",
            "react-scripts": "5.0.1"
        },
        "devDependencies": {
            "@testing-library/jest-dom": "^5.16.4",
            "@testing-library/react": "^13.3.0",
            "@testing-library/user-event": "^13.5.0"
        },
        "browserslist": {
            "production": [
                ">0.2%",
                "not dead",
                "not op_mini all"
            ],
            "development": [
                "last 1 chrome version",
                "last 1 firefox version",
                "last 1 safari version"
            ]
        },
        "keywords": ["react", "frontend", "spa"],
        "author": "",
        "license": "ISC"
    });

    fs::write("package.json", serde_json::to_string_pretty(&package_json)?)?;

    // Create src directory structure
    fs::create_dir_all("src")?;
    fs::create_dir_all("public")?;

    // Create public/index.html
    let index_html = format!("<!DOCTYPE html>
<html lang=\"en\">
  <head>
    <meta charset=\"utf-8\" />
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />
    <meta name=\"theme-color\" content=\"#000000\" />
    <meta name=\"description\" content=\"React application created with nx\" />
    <title>{}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id=\"root\"></div>
  </body>
</html>
", name);

    fs::write("public/index.html", index_html)?;

    // Create src/index.js
    let index_js = r#"import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
"#;

    fs::write("src/index.js", index_js)?;

    // Create src/App.js
    let app_js = format!(r#"import React from 'react';
import './App.css';

function App() {{
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to {}</h1>
        <p>
          Edit <code>src/App.js</code> and save to reload.
        </p>
        <a
          className="App-link"
          href="https://reactjs.org"
          target="_blank"
          rel="noopener noreferrer"
        >
          Learn React
        </a>
      </header>
    </div>
  );
}}

export default App;
"#, name);

    fs::write("src/App.js", app_js)?;

    // Create CSS files
    let index_css = r#"body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
"#;

    fs::write("src/index.css", index_css)?;

    let app_css = r#".App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.App-link {
  color: #61dafb;
}
"#;

    fs::write("src/App.css", app_css)?;

    // Create README
    let readme = format!(r#"# {}

React application created with nx package manager.

## Getting Started

1. Install dependencies:
   ```bash
   nx install
   ```

2. Start development server:
   ```bash
   nx run dev
   ```

3. Build for production:
   ```bash
   nx run build
   ```

## Available Scripts

- `nx run start` - Start development server
- `nx run build` - Build for production
- `nx run test` - Run tests
- `nx run eject` - Eject from Create React App

## Learn More

- [React Documentation](https://reactjs.org/)
- [Create React App Documentation](https://facebook.github.io/create-react-app/docs/getting-started)
"#, name);

    fs::write("README.md", readme)?;

    Ok(())
}

async fn create_package_toml(name: &str) -> Result<()> {
    let content = format!(r#"[package]
name = "{}"
version = "1.0.0"
description = ""
author = ""
license = "ISC"

[scripts]
test = "echo \"Error: no test specified\" && exit 1"
dev = "node index.js"
build = "echo \"Add build script here\""
start = "node index.js"

[dependencies]

[dev-dependencies]
"#, name);
    
    fs::write("package.toml", content)
        .context("Failed to write package.toml")?;
    
    Ok(())
}

async fn create_typescript_project(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "TypeScript project",
        "main": "dist/index.js",
        "scripts": {
            "build": "tsc",
            "start": "node dist/index.js",
            "dev": "ts-node src/index.ts",
            "watch": "tsc --watch",
            "test": "jest"
        },
        "dependencies": {},
        "devDependencies": {
            "typescript": "^5.0.0",
            "ts-node": "^10.9.0",
            "@types/node": "^20.0.0",
            "jest": "^29.5.0",
            "@types/jest": "^29.5.0",
            "ts-jest": "^29.1.0"
        },
        "keywords": ["typescript", "nodejs"],
        "author": "",
        "license": "ISC"
    });

    fs::write("package.json", serde_json::to_string_pretty(&package_json)?)?;

    // Create tsconfig.json
    let tsconfig = json!({
        "compilerOptions": {
            "target": "ES2020",
            "module": "commonjs",
            "outDir": "./dist",
            "rootDir": "./src",
            "strict": true,
            "esModuleInterop": true,
            "skipLibCheck": true,
            "forceConsistentCasingInFileNames": true,
            "resolveJsonModule": true,
            "declaration": true,
            "declarationMap": true,
            "sourceMap": true
        },
        "include": ["src/**/*"],
        "exclude": ["node_modules", "dist"]
    });

    fs::write("tsconfig.json", serde_json::to_string_pretty(&tsconfig)?)?;

    // Create src directory and index.ts
    fs::create_dir_all("src")?;

    let index_ts = r#"interface Greeting {
    message: string;
    timestamp: Date;
}

function createGreeting(name: string): Greeting {
    return {
        message: `Hello, ${name}! Welcome to TypeScript.`,
        timestamp: new Date()
    };
}

function main(): void {
    const greeting = createGreeting("World");
    console.log(greeting.message);
    console.log(`Generated at: ${greeting.timestamp.toISOString()}`);
}

if (require.main === module) {
    main();
}

export { createGreeting, Greeting };
"#;

    fs::write("src/index.ts", index_ts)?;

    // Create jest.config.js
    let jest_config = r#"module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
  ],
};
"#;

    fs::write("jest.config.js", jest_config)?;

    // Create a simple test
    fs::create_dir_all("src/__tests__")?;
    let test_file = r#"import { createGreeting } from '../index';

describe('createGreeting', () => {
    it('should create a greeting with the correct message', () => {
        const greeting = createGreeting('Test');
        expect(greeting.message).toBe('Hello, Test! Welcome to TypeScript.');
        expect(greeting.timestamp).toBeInstanceOf(Date);
    });
});
"#;

    fs::write("src/__tests__/index.test.ts", test_file)?;

    // Create README
    let readme = format!(r#"# {}

TypeScript project created with nx package manager.

## Getting Started

1. Install dependencies:
   ```bash
   nx install
   ```

2. Build the project:
   ```bash
   nx run build
   ```

3. Run in development mode:
   ```bash
   nx run dev
   ```

4. Run tests:
   ```bash
   nx run test
   ```

## Project Structure

```
{}
├── src/
│   ├── index.ts           # Main TypeScript file
│   └── __tests__/         # Test files
├── dist/                  # Compiled JavaScript (generated)
├── tsconfig.json          # TypeScript configuration
├── jest.config.js         # Jest test configuration
└── package.json           # Dependencies and scripts
```

## Available Scripts

- `nx run build` - Compile TypeScript to JavaScript
- `nx run start` - Run the compiled JavaScript
- `nx run dev` - Run TypeScript directly with ts-node
- `nx run watch` - Watch for changes and recompile
- `nx run test` - Run tests with Jest
"#, name, name);

    fs::write("README.md", readme)?;

    Ok(())
}

async fn create_next_project(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "Next.js application",
        "scripts": {
            "dev": "next dev",
            "build": "next build",
            "start": "next start",
            "lint": "next lint"
        },
        "dependencies": {
            "next": "^14.0.0",
            "react": "^18.2.0",
            "react-dom": "^18.2.0"
        },
        "devDependencies": {
            "eslint": "^8.0.0",
            "eslint-config-next": "^14.0.0"
        },
        "keywords": ["nextjs", "react", "ssr"],
        "author": "",
        "license": "ISC"
    });

    fs::write("package.json", serde_json::to_string_pretty(&package_json)?)?;

    // Create pages directory
    fs::create_dir_all("pages")?;

    // Create pages/index.js
    let index_page = format!(r#"import Head from 'next/head';
import styles from '../styles/Home.module.css';

export default function Home() {{
  return (
    <div className={{styles.container}}>
      <Head>
        <title>{}</title>
        <meta name="description" content="Generated by nx package manager" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={{styles.main}}>
        <h1 className={{styles.title}}>
          Welcome to <a href="https://nextjs.org">{}</a>
        </h1>

        <p className={{styles.description}}>
          Get started by editing{{' '}}
          <code className={{styles.code}}>pages/index.js</code>
        </p>

        <div className={{styles.grid}}>
          <a href="https://nextjs.org/docs" className={{styles.card}}>
            <h2>Documentation &rarr;</h2>
            <p>Find in-depth information about Next.js features and API.</p>
          </a>

          <a href="https://nextjs.org/learn" className={{styles.card}}>
            <h2>Learn &rarr;</h2>
            <p>Learn about Next.js in an interactive course with quizzes!</p>
          </a>
        </div>
      </main>
    </div>
  );
}}
"#, name, name);

    fs::write("pages/index.js", index_page)?;

    // Create styles directory
    fs::create_dir_all("styles")?;

    let home_css = r#".container {
  padding: 0 2rem;
}

.main {
  min-height: 100vh;
  padding: 4rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  margin: 0;
  line-height: 1.15;
  font-size: 4rem;
  text-align: center;
}

.title a {
  color: #0070f3;
  text-decoration: none;
}

.title a:hover,
.title a:focus,
.title a:active {
  text-decoration: underline;
}

.description {
  margin: 4rem 0;
  line-height: 1.5;
  font-size: 1.5rem;
  text-align: center;
}

.code {
  background: #fafafa;
  border-radius: 5px;
  padding: 0.75rem;
  font-size: 1.1rem;
  font-family: Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono,
    Bitstream Vera Sans Mono, Courier New, monospace;
}

.grid {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 800px;
}

.card {
  margin: 1rem;
  padding: 1.5rem;
  text-align: left;
  color: inherit;
  text-decoration: none;
  border: 1px solid #eaeaea;
  border-radius: 10px;
  transition: color 0.15s ease, border-color 0.15s ease;
  max-width: 300px;
}

.card:hover,
.card:focus,
.card:active {
  color: #0070f3;
  border-color: #0070f3;
}

.card h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.card p {
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.5;
}
"#;

    fs::write("styles/Home.module.css", home_css)?;

    let globals_css = r#"html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}
"#;

    fs::write("styles/globals.css", globals_css)?;

    // Create pages/_app.js
    let app_js = r#"import '../styles/globals.css';

export default function App({ Component, pageProps }) {
  return <Component {...pageProps} />;
}
"#;

    fs::write("pages/_app.js", app_js)?;

    // Create README
    let readme = format!(r#"# {}

Next.js application created with nx package manager.

## Getting Started

1. Install dependencies:
   ```bash
   nx install
   ```

2. Start development server:
   ```bash
   nx run dev
   ```

3. Build for production:
   ```bash
   nx run build
   ```

4. Start production server:
   ```bash
   nx run start
   ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Learn Next.js](https://nextjs.org/learn)
"#, name);

    fs::write("README.md", readme)?;

    Ok(())
}

async fn create_vue_project(name: &str) -> Result<()> {
    // Placeholder for Vue project template
    create_basic_package_json(name).await?;
    create_basic_structure().await?;
    Ok(())
}

async fn create_angular_project(name: &str) -> Result<()> {
    // Placeholder for Angular project template
    create_basic_package_json(name).await?;
    create_basic_structure().await?;
    Ok(())
}

async fn create_basic_structure() -> Result<()> {
    // Create basic files if they don't exist
    if !Path::new("index.js").exists() {
        let index_content = r#"console.log('Hello from nx!');
"#;
        fs::write("index.js", index_content)?;
        println!("{} Created {}", "✓".green().bold(), "index.js".cyan());
    }
    
    if !Path::new("README.md").exists() {
        let readme_content = r#"# Project

A new project created with nx.

## Getting Started

```bash
nx install
nx run dev
```
"#;
        fs::write("README.md", readme_content)?;
        println!("{} Created {}", "✓".green().bold(), "README.md".cyan());
    }
    
    if !Path::new(".gitignore").exists() {
        let gitignore_content = r#"node_modules/
.nx_modules/
*.log
.env
.env.local
dist/
build/
"#;
        fs::write(".gitignore", gitignore_content)?;
        println!("{} Created {}", "✓".green().bold(), ".gitignore".cyan());
    }
    
    Ok(())
}
