use anyhow::{Result, Context};
use colored::*;
use serde_json::{json, Value};
use std::fs;
use std::path::Path;
use tracing::{info, warn};

pub async fn execute(name: Option<String>, use_toml: bool) -> Result<()> {
    let current_dir = std::env::current_dir()?;
    let project_name = name.unwrap_or_else(|| {
        current_dir
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("my-project")
            .to_string()
    });

    info!("Initializing project: {}", project_name);
    
    if use_toml {
        create_package_toml(&project_name).await?;
        println!("{} Created {}", "✓".green().bold(), "package.toml".cyan());
    } else {
        create_package_json(&project_name).await?;
        println!("{} Created {}", "✓".green().bold(), "package.json".cyan());
    }
    
    // Create basic directory structure
    create_project_structure().await?;
    
    println!("{} Project initialized successfully!", "✓".green().bold());
    println!();
    println!("Next steps:");
    println!("  {} nx install          # Install dependencies", "→".blue());
    println!("  {} nx run dev          # Start development", "→".blue());
    
    Ok(())
}

async fn create_package_json(name: &str) -> Result<()> {
    let package_json = json!({
        "name": name,
        "version": "1.0.0",
        "description": "",
        "main": "index.js",
        "scripts": {
            "test": "echo \"Error: no test specified\" && exit 1",
            "dev": "node index.js",
            "build": "echo \"Add build script here\"",
            "start": "node index.js"
        },
        "keywords": [],
        "author": "",
        "license": "ISC",
        "dependencies": {},
        "devDependencies": {}
    });
    
    let content = serde_json::to_string_pretty(&package_json)?;
    fs::write("package.json", content)
        .context("Failed to write package.json")?;
    
    Ok(())
}

async fn create_package_toml(name: &str) -> Result<()> {
    let content = format!(r#"[package]
name = "{}"
version = "1.0.0"
description = ""
author = ""
license = "ISC"

[scripts]
test = "echo \"Error: no test specified\" && exit 1"
dev = "node index.js"
build = "echo \"Add build script here\""
start = "node index.js"

[dependencies]

[dev-dependencies]
"#, name);
    
    fs::write("package.toml", content)
        .context("Failed to write package.toml")?;
    
    Ok(())
}

async fn create_project_structure() -> Result<()> {
    // Create basic files if they don't exist
    if !Path::new("index.js").exists() {
        let index_content = r#"console.log('Hello from nx!');
"#;
        fs::write("index.js", index_content)?;
        println!("{} Created {}", "✓".green().bold(), "index.js".cyan());
    }
    
    if !Path::new("README.md").exists() {
        let readme_content = r#"# Project

A new project created with nx.

## Getting Started

```bash
nx install
nx run dev
```
"#;
        fs::write("README.md", readme_content)?;
        println!("{} Created {}", "✓".green().bold(), "README.md".cyan());
    }
    
    if !Path::new(".gitignore").exists() {
        let gitignore_content = r#"node_modules/
.nx_modules/
*.log
.env
.env.local
dist/
build/
"#;
        fs::write(".gitignore", gitignore_content)?;
        println!("{} Created {}", "✓".green().bold(), ".gitignore".cyan());
    }
    
    Ok(())
}
