#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Find the nx binary
function findBinary() {
  const binaryName = 'nx' + (process.platform === 'win32' ? '.exe' : '');
  const binaryPath = path.join(__dirname, binaryName);
  
  if (fs.existsSync(binaryPath)) {
    return binaryPath;
  }
  
  // Fallback: look in parent directory
  const fallbackPath = path.join(__dirname, '..', 'bin', binaryName);
  if (fs.existsSync(fallbackPath)) {
    return fallbackPath;
  }
  
  throw new Error(`nx binary not found. Please run "npm install" to download it.`);
}

// Execute the nx binary with all arguments
function main() {
  try {
    const binaryPath = findBinary();
    const args = process.argv.slice(2);
    
    const child = spawn(binaryPath, args, {
      stdio: 'inherit',
      windowsHide: false
    });
    
    child.on('close', (code) => {
      process.exit(code || 0);
    });
    
    child.on('error', (error) => {
      if (error.code === 'ENOENT') {
        console.error('nx binary not found. Please run "npm install" to download it.');
      } else {
        console.error('Failed to execute nx:', error.message);
      }
      process.exit(1);
    });
    
    // Handle process signals
    process.on('SIGINT', () => {
      child.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
      child.kill('SIGTERM');
    });
    
  } catch (error) {
    console.error(error.message);
    process.exit(1);
  }
}

main();
