# Simple Test Suite for nx Package Manager
Write-Host "nx Package Manager - Test Suite" -ForegroundColor Cyan

$errors = 0
$tests = 0

function Test-Install {
    param([string]$package, [int]$maxSeconds = 10)
    
    $script:tests++
    Write-Host "Test $tests`: Installing $package" -ForegroundColor Yellow
    
    Remove-Item -Recurse -Force node_modules, .bin -ErrorAction SilentlyContinue
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $result = & ".\target\release\nx.exe" install $package 2>&1
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        if ($duration -le $maxSeconds) {
            Write-Host "PASS: $package installed in $([math]::Round($duration, 2))s" -ForegroundColor Green
            return $duration
        } else {
            Write-Host "SLOW: $package took $([math]::Round($duration, 2))s" -ForegroundColor Yellow
            return $duration
        }
    }
    catch {
        $stopwatch.Stop()
        Write-Host "FAIL: $package installation failed" -ForegroundColor Red
        $script:errors++
        return 999
    }
}

# Core tests
Write-Host "`nCore Functionality Tests" -ForegroundColor Cyan
try {
    & ".\target\release\nx.exe" --version | Out-Null
    Write-Host "PASS: Version command works" -ForegroundColor Green
    $tests++
} catch {
    Write-Host "FAIL: Version command failed" -ForegroundColor Red
    $errors++
    $tests++
}

# Performance tests
Write-Host "`nPerformance Tests" -ForegroundColor Cyan

$lodash1 = Test-Install "lodash" 5
$lodash2 = Test-Install "lodash" 3  # Should be faster (cached)
$express = Test-Install "express" 10
$typescript = Test-Install "typescript" 10

# Verify TypeScript binary
if (Test-Path ".bin/tsc.cmd") {
    try {
        & ".\.bin\tsc.cmd" --version | Out-Null
        Write-Host "PASS: TypeScript binary works" -ForegroundColor Green
        $tests++
    } catch {
        Write-Host "FAIL: TypeScript binary failed" -ForegroundColor Red
        $errors++
        $tests++
    }
} else {
    Write-Host "FAIL: TypeScript binary not found" -ForegroundColor Red
    $errors++
    $tests++
}

# Scoped package test
$scoped = Test-Install "@types/node" 8

# Results
Write-Host "`nResults" -ForegroundColor Cyan
Write-Host "Tests: $tests, Errors: $errors" -ForegroundColor White

$validTimes = @($lodash1, $lodash2, $express, $typescript, $scoped) | Where-Object { $_ -lt 999 }
if ($validTimes.Count -gt 0) {
    $avgTime = ($validTimes | Measure-Object -Average).Average
    Write-Host "Average install time: $([math]::Round($avgTime, 2))s" -ForegroundColor White
    
    if ($errors -eq 0 -and $avgTime -le 5) {
        Write-Host "SUCCESS: All tests passed with excellent performance!" -ForegroundColor Green
        exit 0
    } elseif ($errors -eq 0) {
        Write-Host "SUCCESS: All tests passed" -ForegroundColor Green
        exit 0
    }
}

Write-Host "FAILURE: $errors tests failed" -ForegroundColor Red
exit 1
