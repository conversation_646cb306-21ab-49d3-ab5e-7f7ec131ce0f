# Comprehensive Test Suite for nx Package Manager
# Tests all functionality and validates performance targets

Write-Host "🚀 nx Package Manager - Comprehensive Test Suite" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

$errors = 0
$tests = 0
$performance_results = @()

function Test-Command {
    param(
        [string]$description,
        [string]$command,
        [int]$maxSeconds = 30,
        [bool]$shouldSucceed = $true
    )
    
    $script:tests++
    Write-Host "`n📋 Test $tests`: $description" -ForegroundColor Yellow
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $result = Invoke-Expression $command 2>&1
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        if ($shouldSucceed) {
            if ($duration -le $maxSeconds) {
                Write-Host "✅ PASS: $description ($([math]::Round($duration, 2))s)" -ForegroundColor Green
                return $duration
            } else {
                Write-Host "⚠️  SLOW: $description took $([math]::Round($duration, 2))s (target: ${maxSeconds}s)" -ForegroundColor Yellow
                return $duration
            }
        } else {
            Write-Host "❌ FAIL: Command should have failed but succeeded" -ForegroundColor Red
            $script:errors++
            return $duration
        }
    }
    catch {
        $stopwatch.Stop()
        $duration = $stopwatch.Elapsed.TotalSeconds
        
        if ($shouldSucceed) {
            Write-Host "❌ FAIL: $description" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            $script:errors++
        } else {
            Write-Host "✅ PASS: $description (expected failure)" -ForegroundColor Green
        }
        return $duration
    }
}

function Clean-Environment {
    Remove-Item -Recurse -Force node_modules, .bin, nx-lock.json -ErrorAction SilentlyContinue
}

# Core functionality tests
Write-Host "`n🔧 Core Functionality Tests" -ForegroundColor Cyan
Test-Command "Version command" ".\target\release\nx.exe --version" 5
Test-Command "Help command" ".\target\release\nx.exe --help" 5

# Performance tests - Single packages
Write-Host "`n⚡ Performance Tests - Single Packages" -ForegroundColor Cyan

Clean-Environment
$duration = Test-Command "Install lodash (first time)" ".\target\release\nx.exe install lodash" 3
$performance_results += @{Package="lodash"; Type="First Install"; Duration=$duration}

Clean-Environment
$duration = Test-Command "Install lodash (cached)" ".\target\release\nx.exe install lodash" 2
$performance_results += @{Package="lodash"; Type="Cached Install"; Duration=$duration}

Clean-Environment
$duration = Test-Command "Install express (complex dependencies)" ".\target\release\nx.exe install express" 5
$performance_results += @{Package="express"; Type="Complex Install"; Duration=$duration}

Clean-Environment
$duration = Test-Command "Install typescript (with binaries)" ".\target\release\nx.exe install typescript" 5
$performance_results += @{Package="typescript"; Type="Binary Install"; Duration=$duration}

# Test binary linking
Write-Host "`n🔗 Binary Linking Tests" -ForegroundColor Cyan
if (Test-Path ".bin/tsc.cmd") {
    Test-Command "TypeScript compiler binary" ".\.bin\tsc.cmd --version" 5
} else {
    Write-Host "❌ FAIL: TypeScript binary not found" -ForegroundColor Red
    $errors++
}

# Package.json installation test
Write-Host "`n📦 Package.json Installation Test" -ForegroundColor Cyan
Clean-Environment

# Create test package.json
$packageJson = @{
    name = "test-project"
    version = "1.0.0"
    dependencies = @{
        lodash = "^4.17.21"
        axios = "^1.0.0"
    }
    devDependencies = @{
        "@types/node" = "^20.0.0"
    }
} | ConvertTo-Json -Depth 3

$packageJson | Out-File -FilePath "package.json" -Encoding UTF8

$duration = Test-Command "Install from package.json" ".\target\release\nx.exe install" 10
$performance_results += @{Package="package.json"; Type="Multi Install"; Duration=$duration}

# Scoped package test
Write-Host "`n🏷️  Scoped Package Tests" -ForegroundColor Cyan
Clean-Environment
$duration = Test-Command "Install scoped package (@types/node)" ".\target\release\nx.exe install @types/node" 5
$performance_results += @{Package="@types/node"; Type="Scoped Install"; Duration=$duration}

# Large package test
Write-Host "`n📈 Large Package Tests" -ForegroundColor Cyan
Clean-Environment
$duration = Test-Command "Install large package (react)" ".\target\release\nx.exe install react" 5
$performance_results += @{Package="react"; Type="Large Install"; Duration=$duration}

# Concurrent installation test
Write-Host "`n🔄 Concurrent Installation Test" -ForegroundColor Cyan
Clean-Environment
$packages = "lodash axios express react typescript"
$duration = Test-Command "Install multiple packages concurrently" ".\target\release\nx.exe install $packages" 15
$performance_results += @{Package="Multiple"; Type="Concurrent Install"; Duration=$duration}

# Verify installations
Write-Host "`n✅ Installation Verification" -ForegroundColor Cyan
$expectedPackages = @("lodash", "axios", "express", "react", "typescript")
foreach ($package in $expectedPackages) {
    if (Test-Path "node_modules/$package") {
        Write-Host "✅ $package installed correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ $package not found" -ForegroundColor Red
        $errors++
    }
}

# Performance summary
Write-Host "`n📊 Performance Summary" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

$performance_results | ForEach-Object {
    $status = if ($_.Duration -le 3) { "🚀 EXCELLENT" } 
              elseif ($_.Duration -le 5) { "✅ GOOD" }
              elseif ($_.Duration -le 10) { "⚠️  ACCEPTABLE" }
              else { "❌ SLOW" }
    
    Write-Host "$($_.Package) ($($_.Type)): $([math]::Round($_.Duration, 2))s $status"
}

$avgDuration = ($performance_results | Measure-Object -Property Duration -Average).Average
Write-Host "`nAverage Installation Time: $([math]::Round($avgDuration, 2))s" -ForegroundColor White

# Final results
Write-Host "`n📊 Test Results" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "Total Tests: $tests" -ForegroundColor White
Write-Host "Passed: $($tests - $errors)" -ForegroundColor Green
Write-Host "Failed: $errors" -ForegroundColor Red

if ($errors -eq 0 -and $avgDuration -le 5) {
    Write-Host "`n🎉 ALL TESTS PASSED! nx is production-ready with excellent performance!" -ForegroundColor Green
    Write-Host "Average installation time: $([math]::Round($avgDuration, 2))s (Target: under 5s)" -ForegroundColor Green
    exit 0
} elseif ($errors -eq 0) {
    Write-Host "`n✅ All tests passed, but performance could be improved." -ForegroundColor Yellow
    Write-Host "Average installation time: $([math]::Round($avgDuration, 2))s (Target: under 5s)" -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "`n❌ $errors test(s) failed. Please fix before production release." -ForegroundColor Red
    exit 1
}
