pub mod init;
pub mod install;
pub mod uninstall;
pub mod run;
pub mod start;
pub mod test;
pub mod list;
pub mod link;
pub mod cache;
pub mod bench;
pub mod publish;
pub mod config;
pub mod search;
pub mod info;
pub mod outdated;
pub mod update;

use crate::{CacheCommands, ConfigCommands};
use anyhow::Result;

// Re-export command handlers for easy access
pub use init::*;
pub use install::*;
pub use uninstall::*;
pub use run::*;
pub use link::*;
pub use cache::*;
pub use bench::*;
pub use publish::*;
pub use config::*;
