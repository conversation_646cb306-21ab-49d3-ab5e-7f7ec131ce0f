use anyhow::Result;
use colored::*;
use crate::ui::UI;
use crate::commands::run::{check_node_available, get_node_version, check_npm_available};

/// Execute nx doctor command
pub async fn execute(ui: &UI) -> Result<()> {
    ui.step("🩺 Running system diagnostics...");

    println!("\n🩺 System Health Check");
    println!("┌─────────────────────┬───────────────────────────────────────┐");

    // Check Node.js
    let node_available = check_node_available().await;
    let node_status = if node_available { "✅ Available" } else { "❌ Not found" };
    println!("│ {} │ {:<37} │", "Node.js".bright_white().bold(), node_status);

    if node_available {
        if let Ok(version) = get_node_version().await {
            println!("│ {} │ {:<37} │", "Node.js Version".bright_white().bold(), version);
        }
    }

    // Check npm
    let npm_available = check_npm_available().await;
    let npm_status = if npm_available { "✅ Available" } else { "❌ Not found" };
    println!("│ {} │ {:<37} │", "npm".bright_white().bold(), npm_status);

    // Check cache directory
    let cache_dir = dirs::cache_dir()
        .unwrap_or_else(|| std::path::PathBuf::from("."))
        .join("nx");
    let cache_status = if cache_dir.exists() { "✅ Exists" } else { "⚠️  Not found" };
    println!("│ {} │ {:<37} │", "Cache Directory".bright_white().bold(), cache_status);

    // Check current directory
    let has_package_json = std::path::Path::new("package.json").exists();
    let project_status = if has_package_json { "✅ Valid project" } else { "⚠️  No package.json" };
    println!("│ {} │ {:<37} │", "Project".bright_white().bold(), project_status);

    println!("└─────────────────────┴───────────────────────────────────────┘");

    // Recommendations
    println!("\n💡 Recommendations:");
    if !node_available {
        println!("  • Install Node.js from https://nodejs.org/");
    }
    if !has_package_json {
        println!("  • Run 'nx init' to create a new project");
    }

    ui.success("✅ System diagnostics completed");
    Ok(())
}
