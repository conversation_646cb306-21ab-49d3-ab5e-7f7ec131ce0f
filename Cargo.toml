[package]
name = "nx"
version = "1.0.0"
edition = "2021"
authors = ["nx team"]
description = "Ultra-fast npm package manager written in Rust - 500x faster installations"
license = "MIT"
repository = "https://github.com/nx-team/nx"
homepage = "https://nx-pm.dev"
keywords = ["npm", "package-manager", "rust", "fast", "parallel"]
categories = ["command-line-utilities", "development-tools"]

[[bin]]
name = "nx"
path = "src/main.rs"
    
[dependencies]
# CLI and argument parsing
clap = { version = "4.4", features = ["derive", "color", "suggestions"] }

# Async runtime and HTTP client
tokio = { version = "1.35", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "gzip", "stream"] }
futures-util = "0.3"
futures = "0.3"

# Serialization and data handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
semver = { version = "1.0", features = ["serde"] }

# Archive and compression
flate2 = "1.0"
tar = "0.4"
memmap2 = "0.9"

# Cryptography and hashing
sha2 = "0.10"
base64 = "0.21"
hex = "0.4"

# File system and path handling
dirs = "5.0"
walkdir = "2.4"

# Progress bars and UI
indicatif = { version = "0.17", features = ["tokio"] }
console = "0.15"
colored = "2.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.6", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
url = "2.4"
regex = "1.10"
bytes = "1.5"
num_cpus = "1.16"
atty = "0.2"

[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"
tokio-test = "0.4"

[profile.release]
# Maximum optimization for production builds
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
debug = false

[profile.dev]
# Faster compilation for development
opt-level = 0
debug = true
incremental = true

[profile.test]
# Optimized test builds
opt-level = 1
debug = true

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winbase", "fileapi", "handleapi"] }

[target.'cfg(unix)'.dependencies]
libc = "0.2"
