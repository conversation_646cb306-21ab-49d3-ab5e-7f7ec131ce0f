use anyhow::{anyhow, Result};
use futures::stream::{self, StreamExt};
use lru::LruCache;

use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque};
use std::num::NonZeroUsize;
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::types::{DependencyNode, DependencyTree, PackageMetadata, ResolvedPackage, VersionMetadata};
use crate::utils::registry::RegistryClient;

pub struct UltraFastResolver {
    registry: Arc<RegistryClient>,
    metadata_cache: Arc<RwLock<LruCache<String, PackageMetadata>>>,
    resolution_cache: Arc<RwLock<LruCache<String, ResolvedPackage>>>,
    conflict_resolver: ConflictResolver,
}

impl UltraFastResolver {
    pub fn new(registry: Arc<RegistryClient>) -> Self {
        Self {
            registry,
            metadata_cache: Arc::new(RwLock::new(LruCache::new(
                NonZeroUsize::new(10000).unwrap()
            ))),
            resolution_cache: Arc::new(RwLock::new(LruCache::new(
                NonZeroUsize::new(50000).unwrap()
            ))),
            conflict_resolver: ConflictResolver::new(),
        }
    }

    /// Ultra-fast dependency resolution with massive parallelization
    pub async fn resolve_dependencies(
        &self,
        dependencies: &HashMap<String, String>,
        dev_dependencies: &HashMap<String, String>,
        include_dev: bool,
    ) -> Result<DependencyTree> {
        let start_time = Instant::now();
        info!("🚀 Starting ultra-fast dependency resolution for {} packages", 
              dependencies.len() + if include_dev { dev_dependencies.len() } else { 0 });

        let mut all_deps = dependencies.clone();
        if include_dev {
            all_deps.extend(dev_dependencies.clone());
        }

        if all_deps.is_empty() {
            return Ok(DependencyTree {
                nodes: HashMap::new(),
                root: "root".to_string(),
            });
        }

        // Phase 1: Parallel metadata fetching with 1000 concurrent requests
        let metadata_futures = all_deps.iter().map(|(name, spec)| {
            let resolver = self.clone();
            let name = name.clone();
            let spec = spec.clone();
            async move {
                resolver.get_package_metadata(&name).await
                    .map(|metadata| (name, spec, metadata))
            }
        });

        let metadata_results: Vec<_> = stream::iter(metadata_futures)
            .buffer_unordered(1000) // Ultra-high concurrency
            .collect()
            .await;

        // Phase 2: Ultra-fast version resolution
        let _resolved_packages: HashMap<String, ResolvedPackage> = HashMap::new();
        let mut pending_queue = VecDeque::new();
        let mut processed = HashSet::new();

        for result in metadata_results {
            match result {
                Ok((name, spec, metadata)) => {
                    if let Some(version) = self.resolve_version(&metadata, &spec)? {
                        let key = format!("{}@{}", name, version);
                        if !processed.contains(&key) {
                            pending_queue.push_back((name.clone(), version.clone(), 0, false, false));
                            processed.insert(key);
                        }
                    }
                }
                Err(e) => warn!("Failed to fetch metadata: {}", e),
            }
        }

        // Phase 3: Recursive dependency resolution with conflict handling
        let mut nodes = HashMap::new();
        let mut total_resolved = 0;

        while let Some((name, version, depth, is_dev, is_optional)) = pending_queue.pop_front() {
            let cache_key = format!("{}@{}", name, version);
            
            // Check resolution cache first
            let cached_resolved = {
                let mut cache = self.resolution_cache.write().await;
                cache.get(&cache_key).cloned()
            };

            if let Some(resolved) = cached_resolved {
                self.add_node_to_tree(&mut nodes, &mut pending_queue, &mut processed,
                                    &resolved, depth, is_dev, is_optional);
                total_resolved += 1;
                continue;
            }

            // Resolve new package
            if let Ok(metadata) = self.get_package_metadata(&name).await {
                if let Some(version_meta) = metadata.versions.get(&version) {
                    let resolved = self.create_resolved_package(&name, &version, version_meta, &metadata);
                    
                    // Cache the resolution
                    {
                        let mut cache = self.resolution_cache.write().await;
                        cache.put(cache_key, resolved.clone());
                    }

                    self.add_node_to_tree(&mut nodes, &mut pending_queue, &mut processed, 
                                        &resolved, depth, is_dev, is_optional);
                    total_resolved += 1;
                }
            }
        }

        let resolution_time = start_time.elapsed();
        info!("✅ Resolved {} packages in {:.3}s ({:.0} packages/sec)", 
              total_resolved, 
              resolution_time.as_secs_f64(),
              total_resolved as f64 / resolution_time.as_secs_f64());

        Ok(DependencyTree {
            nodes,
            root: "root".to_string(),
        })
    }

    async fn get_package_metadata(&self, name: &str) -> Result<PackageMetadata> {
        // Check cache first
        {
            let mut cache = self.metadata_cache.write().await;
            if let Some(metadata) = cache.get(name) {
                debug!("Cache hit for package: {}", name);
                return Ok(metadata.clone());
            }
        }

        // Fetch from registry
        debug!("Fetching metadata for package: {}", name);
        let metadata = self.registry.get_package_metadata(name).await?;

        // Cache the result
        {
            let mut cache = self.metadata_cache.write().await;
            cache.put(name.to_string(), metadata.clone());
        }

        Ok(metadata)
    }

    fn resolve_version(&self, metadata: &PackageMetadata, spec: &str) -> Result<Option<String>> {
        if spec == "latest" || spec == "*" {
            return Ok(Some(metadata.latest.clone()));
        }

        // Parse semver requirement
        let req = VersionReq::parse(spec)
            .map_err(|e| anyhow!("Invalid version spec '{}': {}", spec, e))?;

        // Find best matching version
        let mut versions: Vec<_> = metadata.versions.keys()
            .filter_map(|v| Version::parse(v).ok().map(|parsed| (parsed, v.clone())))
            .collect();

        versions.sort_by(|a, b| b.0.cmp(&a.0)); // Sort descending

        for (version, version_str) in versions {
            if req.matches(&version) {
                return Ok(Some(version_str));
            }
        }

        Ok(None)
    }

    fn create_resolved_package(
        &self,
        name: &str,
        version: &str,
        version_meta: &VersionMetadata,
        _metadata: &PackageMetadata,
    ) -> ResolvedPackage {
        ResolvedPackage {
            name: name.to_string(),
            version: version.to_string(),
            resolved_url: version_meta.dist.tarball.clone(),
            integrity: version_meta.dist.integrity.clone(),
            dependencies: version_meta.dependencies.clone(),
            dev_dependencies: version_meta.dev_dependencies.clone(),
            peer_dependencies: version_meta.peer_dependencies.clone(),
            optional_dependencies: version_meta.optional_dependencies.clone(),
            bin: version_meta.bin.clone(),
            engines: version_meta.engines.clone(),
            os: version_meta.os.clone(),
            cpu: version_meta.cpu.clone(),
            size: version_meta.dist.unpacked_size,
        }
    }

    fn add_node_to_tree(
        &self,
        nodes: &mut HashMap<String, DependencyNode>,
        pending_queue: &mut VecDeque<(String, String, usize, bool, bool)>,
        processed: &mut HashSet<String>,
        resolved: &ResolvedPackage,
        depth: usize,
        is_dev: bool,
        is_optional: bool,
    ) {
        let node_key = format!("{}@{}", resolved.name, resolved.version);
        
        if nodes.contains_key(&node_key) {
            return;
        }

        let mut children = Vec::new();

        // Add dependencies to queue
        for (dep_name, dep_spec) in &resolved.dependencies {
            let child_key = format!("{}@{}", dep_name, dep_spec);
            children.push(child_key.clone());
            
            if !processed.contains(&child_key) && depth < 100 { // Prevent infinite recursion
                pending_queue.push_back((dep_name.clone(), dep_spec.clone(), depth + 1, false, false));
                processed.insert(child_key);
            }
        }

        // Add optional dependencies
        for (dep_name, dep_spec) in &resolved.optional_dependencies {
            let child_key = format!("{}@{}", dep_name, dep_spec);
            children.push(child_key.clone());
            
            if !processed.contains(&child_key) && depth < 100 {
                pending_queue.push_back((dep_name.clone(), dep_spec.clone(), depth + 1, false, true));
                processed.insert(child_key);
            }
        }

        let node = DependencyNode {
            name: resolved.name.clone(),
            version: resolved.version.clone(),
            spec: format!("^{}", resolved.version),
            resolved: Some(resolved.clone()),
            children,
            depth,
            dev: is_dev,
            optional: is_optional,
            peer: false,
        };

        nodes.insert(node_key, node);
    }
}

impl Clone for UltraFastResolver {
    fn clone(&self) -> Self {
        Self {
            registry: Arc::clone(&self.registry),
            metadata_cache: Arc::clone(&self.metadata_cache),
            resolution_cache: Arc::clone(&self.resolution_cache),
            conflict_resolver: self.conflict_resolver.clone(),
        }
    }
}

#[derive(Clone)]
struct ConflictResolver {
    strategies: Arc<Mutex<HashMap<String, ConflictStrategy>>>,
}

#[derive(Clone)]
enum ConflictStrategy {
    Latest,
    Semver,
    Manual(String),
}

impl ConflictResolver {
    fn new() -> Self {
        Self {
            strategies: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    fn resolve_conflict(
        &self,
        _package: &str,
        _versions: &[String],
    ) -> Result<String> {
        // For now, always use latest
        // TODO: Implement sophisticated conflict resolution
        Ok("latest".to_string())
    }
}
