# Production Validation Script for nx Package Manager
# Validates all core functionality before production release

Write-Host "🚀 nx Package Manager - Production Validation" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$errors = 0
$tests = 0

function Test-Command {
    param(
        [string]$description,
        [string]$command,
        [bool]$shouldSucceed = $true
    )
    
    $tests++
    Write-Host "`n📋 Test $tests`: $description" -ForegroundColor Yellow
    
    try {
        $result = Invoke-Expression $command 2>&1
        if ($shouldSucceed) {
            Write-Host "✅ PASS: $description" -ForegroundColor Green
        } else {
            Write-Host "❌ FAIL: Command should have failed but succeeded" -ForegroundColor Red
            $script:errors++
        }
    }
    catch {
        if ($shouldSucceed) {
            Write-Host "❌ FAIL: $description" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            $script:errors++
        } else {
            Write-Host "✅ PASS: $description (expected failure)" -ForegroundColor Green
        }
    }
}

# Core functionality tests
Test-Command "Version command" ".\target\release\nx.exe --version"
Test-Command "Help command" ".\target\release\nx.exe --help"
Test-Command "Init command" ".\target\release\nx.exe init --name test-validation"

# Package installation tests
Write-Host "`n🔧 Testing Package Installation..." -ForegroundColor Cyan

# Clean up first
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
}

Test-Command "Install single package" ".\target\release\nx.exe install lodash"
Test-Command "Install from package.json" ".\target\release\nx.exe install"

# Verify installations
if (Test-Path "node_modules/lodash") {
    Write-Host "✅ PASS: lodash package installed correctly" -ForegroundColor Green
} else {
    Write-Host "❌ FAIL: lodash package not found" -ForegroundColor Red
    $errors++
}

# Performance test
Write-Host "`n⚡ Performance Test..." -ForegroundColor Cyan
$stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
try {
    .\target\release\nx.exe install axios | Out-Null
    $stopwatch.Stop()
    $duration = $stopwatch.Elapsed.TotalSeconds
    
    if ($duration -lt 10) {
        Write-Host "✅ PASS: Installation completed in $([math]::Round($duration, 2))s (under 10s target)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  WARN: Installation took $([math]::Round($duration, 2))s (over 10s target)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ FAIL: Performance test failed" -ForegroundColor Red
    $errors++
}

# Script execution test
Test-Command "Run command test" ".\target\release\nx.exe run --help"

# Benchmark test
Test-Command "Benchmark command" ".\target\release\nx.exe bench --iterations 1"

# Final results
Write-Host "`n📊 Validation Results" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "Total Tests: $tests" -ForegroundColor White
Write-Host "Passed: $($tests - $errors)" -ForegroundColor Green
Write-Host "Failed: $errors" -ForegroundColor Red

if ($errors -eq 0) {
    Write-Host "`n🎉 ALL TESTS PASSED! nx is ready for production!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ $errors test(s) failed. Please fix before production release." -ForegroundColor Red
    exit 1
}
