use anyhow::{Result, Context};
use memmap2::Mmap;
use std::fs::File;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// Memory-mapped cache for ultra-fast zero-copy package access
#[derive(Debug)]
pub struct MemoryMappedCache {
    cache_dir: PathBuf,
    mappings: Arc<RwLock<HashMap<String, Arc<Mmap>>>>,
}

impl MemoryMappedCache {
    pub fn new(cache_dir: PathBuf) -> Self {
        Self {
            cache_dir,
            mappings: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Get a memory-mapped view of a cached package (zero-copy)
    pub async fn get_mapped_package(&self, name: &str, version: &str) -> Result<Option<Arc<Mmap>>> {
        let cache_key = format!("{}-{}", name, version);
        let cache_path = self.get_cache_path(name, version);
        
        // Check if already mapped
        {
            let mappings = self.mappings.read().await;
            if let Some(mapping) = mappings.get(&cache_key) {
                debug!("Memory-mapped cache hit for {}@{}", name, version);
                return Ok(Some(mapping.clone()));
            }
        }
        
        // Check if file exists
        if !cache_path.exists() {
            return Ok(None);
        }
        
        // Create memory mapping
        let file = File::open(&cache_path)
            .context("Failed to open cached package file")?;
        let mmap = unsafe { Mmap::map(&file) }
            .context("Failed to create memory mapping")?;
        let mmap = Arc::new(mmap);
        
        // Store mapping for reuse
        {
            let mut mappings = self.mappings.write().await;
            mappings.insert(cache_key, mmap.clone());
        }
        
        info!("Created memory mapping for {}@{}", name, version);
        Ok(Some(mmap))
    }
    
    /// Store a package in cache for memory mapping
    pub async fn store_package(&self, name: &str, version: &str, data: &[u8]) -> Result<PathBuf> {
        let cache_path = self.get_cache_path(name, version);
        
        // Ensure cache directory exists
        if let Some(parent) = cache_path.parent() {
            tokio::fs::create_dir_all(parent).await
                .context("Failed to create cache directory")?;
        }
        
        // Write data atomically
        let temp_path = cache_path.with_extension("tmp");
        tokio::fs::write(&temp_path, data).await
            .context("Failed to write package to cache")?;
        
        tokio::fs::rename(&temp_path, &cache_path).await
            .context("Failed to move package to final cache location")?;
        
        debug!("Stored {}@{} in cache ({} bytes)", name, version, data.len());
        Ok(cache_path)
    }
    
    /// Check if a package is cached
    pub fn is_cached(&self, name: &str, version: &str) -> bool {
        self.get_cache_path(name, version).exists()
    }
    
    /// Get the cache path for a package
    fn get_cache_path(&self, name: &str, version: &str) -> PathBuf {
        let normalized_name = name.replace('/', "_");
        self.cache_dir
            .join("packages")
            .join(&normalized_name)
            .join(format!("{}-{}.tgz", normalized_name, version))
    }
    
    /// Clear memory mappings (for cleanup)
    pub async fn clear_mappings(&self) {
        let mut mappings = self.mappings.write().await;
        mappings.clear();
        info!("Cleared all memory mappings");
    }
    
    /// Get cache statistics
    pub async fn stats(&self) -> CacheStats {
        let mappings = self.mappings.read().await;
        let mapped_count = mappings.len();
        let total_mapped_size: usize = mappings.values()
            .map(|mmap| mmap.len())
            .sum();
        
        CacheStats {
            mapped_packages: mapped_count,
            total_mapped_size,
        }
    }
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub mapped_packages: usize,
    pub total_mapped_size: usize,
}

impl Clone for MemoryMappedCache {
    fn clone(&self) -> Self {
        Self {
            cache_dir: self.cache_dir.clone(),
            mappings: Arc::clone(&self.mappings),
        }
    }
}
