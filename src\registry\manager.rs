use anyhow::{Result, Context};
use std::collections::HashMap;
use tracing::{debug, info, warn};

use super::{Registry, RegistryClient, RegistryConfig, PackageMetadata, VersionMetadata, AuthManager};

/// Registry manager that handles multiple registries and mirrors
#[derive(Debug)]
pub struct RegistryManager {
    primary_registry: Registry,
    mirrors: Vec<Registry>,
    auth_manager: AuthManager,
    config: RegistryConfig,
}

impl RegistryManager {
    pub fn new(config: RegistryConfig) -> Result<Self> {
        let auth_manager = AuthManager::new()?;
        
        // Load authentication for primary registry (ignore errors for public registries)
        let auth = auth_manager.load_auth(&config.url).unwrap_or(None);
        let mut primary_registry = Registry::with_url(&config.url)?;
        if let Some(auth_info) = auth {
            primary_registry = primary_registry.with_auth(auth_info.token);
        }
        
        // Set up mirrors (ignore auth errors for public registries)
        let mut mirrors = Vec::new();
        for mirror_url in &config.mirrors {
            let mirror_auth = auth_manager.load_auth(mirror_url).unwrap_or(None);
            let mut mirror_registry = Registry::with_url(mirror_url)?;
            if let Some(auth_info) = mirror_auth {
                mirror_registry = mirror_registry.with_auth(auth_info.token);
            }
            mirrors.push(mirror_registry);
        }
        
        Ok(Self {
            primary_registry,
            mirrors,
            auth_manager,
            config,
        })
    }
    
    /// Get package metadata, trying primary registry first, then mirrors
    pub async fn get_package_metadata(&self, name: &str) -> Result<PackageMetadata> {
        debug!("Fetching metadata for package: {}", name);
        
        // Try primary registry first
        match self.primary_registry.get_package_metadata(name).await {
            Ok(metadata) => {
                debug!("Got metadata from primary registry for: {}", name);
                return Ok(metadata);
            }
            Err(e) => {
                warn!("Primary registry failed for {}: {}", name, e);
            }
        }
        
        // Try mirrors
        for (i, mirror) in self.mirrors.iter().enumerate() {
            match mirror.get_package_metadata(name).await {
                Ok(metadata) => {
                    info!("Got metadata from mirror {} for: {}", i + 1, name);
                    return Ok(metadata);
                }
                Err(e) => {
                    warn!("Mirror {} failed for {}: {}", i + 1, name, e);
                }
            }
        }
        
        Err(anyhow::anyhow!("Failed to fetch metadata for {} from all registries", name))
    }
    
    /// Get version metadata, trying primary registry first, then mirrors
    pub async fn get_version_metadata(&self, name: &str, version: &str) -> Result<VersionMetadata> {
        debug!("Fetching version metadata for: {}@{}", name, version);
        
        // Try primary registry first
        match self.primary_registry.get_version_metadata(name, version).await {
            Ok(metadata) => {
                debug!("Got version metadata from primary registry for: {}@{}", name, version);
                return Ok(metadata);
            }
            Err(e) => {
                warn!("Primary registry failed for {}@{}: {}", name, version, e);
            }
        }
        
        // Try mirrors
        for (i, mirror) in self.mirrors.iter().enumerate() {
            match mirror.get_version_metadata(name, version).await {
                Ok(metadata) => {
                    info!("Got version metadata from mirror {} for: {}@{}", i + 1, name, version);
                    return Ok(metadata);
                }
                Err(e) => {
                    warn!("Mirror {} failed for {}@{}: {}", i + 1, name, version, e);
                }
            }
        }
        
        Err(anyhow::anyhow!("Failed to fetch version metadata for {}@{} from all registries", name, version))
    }
    
    /// Download tarball, trying primary registry first, then mirrors
    pub async fn download_tarball(&self, url: &str) -> Result<bytes::Bytes> {
        debug!("Downloading tarball from: {}", url);
        
        // Try primary registry first
        match self.primary_registry.download_tarball(url).await {
            Ok(data) => {
                debug!("Downloaded tarball from primary registry");
                return Ok(data);
            }
            Err(e) => {
                warn!("Primary registry download failed: {}", e);
            }
        }
        
        // Try mirrors - replace the registry URL in the tarball URL
        for (i, mirror) in self.mirrors.iter().enumerate() {
            // Replace the registry URL in the tarball URL with the mirror URL
            let mirror_url = url.replace(&self.config.url, &self.config.mirrors[i]);
            
            match mirror.download_tarball(&mirror_url).await {
                Ok(data) => {
                    info!("Downloaded tarball from mirror {}", i + 1);
                    return Ok(data);
                }
                Err(e) => {
                    warn!("Mirror {} download failed: {}", i + 1, e);
                }
            }
        }
        
        Err(anyhow::anyhow!("Failed to download tarball from all registries"))
    }
    
    /// Get registry statistics
    pub async fn get_stats(&self) -> RegistryStats {
        RegistryStats {
            primary_url: self.config.url.clone(),
            mirror_count: self.mirrors.len(),
            mirrors: self.config.mirrors.clone(),
            timeout: self.config.timeout,
            retry_attempts: self.config.retry_attempts,
        }
    }
    
    /// Test connectivity to all registries
    pub async fn test_connectivity(&self) -> HashMap<String, bool> {
        let mut results = HashMap::new();
        
        // Test primary registry
        let primary_ok = self.test_registry_connectivity(&self.primary_registry, &self.config.url).await;
        results.insert(self.config.url.clone(), primary_ok);
        
        // Test mirrors
        for (i, mirror) in self.mirrors.iter().enumerate() {
            let mirror_url = &self.config.mirrors[i];
            let mirror_ok = self.test_registry_connectivity(mirror, mirror_url).await;
            results.insert(mirror_url.clone(), mirror_ok);
        }
        
        results
    }
    
    /// Test connectivity to a single registry
    async fn test_registry_connectivity(&self, registry: &Registry, url: &str) -> bool {
        // Try to fetch a well-known package (like 'lodash') to test connectivity
        match registry.get_package_metadata("lodash").await {
            Ok(_) => {
                debug!("Registry {} is accessible", url);
                true
            }
            Err(e) => {
                warn!("Registry {} is not accessible: {}", url, e);
                false
            }
        }
    }
}

/// Registry statistics
#[derive(Debug, Clone)]
pub struct RegistryStats {
    pub primary_url: String,
    pub mirror_count: usize,
    pub mirrors: Vec<String>,
    pub timeout: u64,
    pub retry_attempts: u32,
}
