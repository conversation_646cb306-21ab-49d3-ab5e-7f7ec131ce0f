use anyhow::{Result, Context};
use std::fs;
use std::path::PathBuf;
use tracing::{debug, warn};

use super::types::AuthInfo;

/// Authentication manager for npm registries
#[derive(Debug)]
pub struct AuthManager {
    auth_file: PathBuf,
}

impl AuthManager {
    pub fn new() -> Result<Self> {
        let auth_file = get_auth_file_path()?;
        Ok(Self { auth_file })
    }
    
    /// Load authentication information for a registry
    pub fn load_auth(&self, registry: &str) -> Result<Option<AuthInfo>> {
        if !self.auth_file.exists() {
            debug!("No auth file found at: {}", self.auth_file.display());
            return Ok(None);
        }
        
        let content = fs::read_to_string(&self.auth_file)
            .context("Failed to read auth file")?;
        
        // Parse .npmrc format
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            if let Some((key, value)) = line.split_once('=') {
                let key = key.trim();
                let value = value.trim();
                
                // Look for registry-specific auth tokens
                if key.contains(registry) && key.contains("_authToken") {
                    return Ok(Some(AuthInfo {
                        token: value.to_string(),
                        registry: registry.to_string(),
                        username: None,
                        email: None,
                    }));
                }
                
                // Look for global auth token
                if key == "_authToken" {
                    return Ok(Some(AuthInfo {
                        token: value.to_string(),
                        registry: registry.to_string(),
                        username: None,
                        email: None,
                    }));
                }
            }
        }
        
        Ok(None)
    }
    
    /// Save authentication information
    pub fn save_auth(&self, auth: &AuthInfo) -> Result<()> {
        // Ensure directory exists
        if let Some(parent) = self.auth_file.parent() {
            fs::create_dir_all(parent)
                .context("Failed to create auth directory")?;
        }
        
        // Read existing content
        let mut content = if self.auth_file.exists() {
            fs::read_to_string(&self.auth_file)
                .context("Failed to read existing auth file")?
        } else {
            String::new()
        };
        
        // Add or update auth token
        let auth_line = format!("{}:_authToken={}\n", auth.registry, auth.token);
        
        // Remove existing auth for this registry
        let lines: Vec<&str> = content.lines().collect();
        let mut new_lines = Vec::new();
        
        for line in lines {
            if !line.contains(&auth.registry) || !line.contains("_authToken") {
                new_lines.push(line);
            }
        }
        
        // Add new auth line
        new_lines.push(&auth_line);
        
        let new_content = new_lines.join("\n");
        fs::write(&self.auth_file, new_content)
            .context("Failed to write auth file")?;
        
        debug!("Saved auth for registry: {}", auth.registry);
        Ok(())
    }
    
    /// Remove authentication for a registry
    pub fn remove_auth(&self, registry: &str) -> Result<()> {
        if !self.auth_file.exists() {
            return Ok(());
        }
        
        let content = fs::read_to_string(&self.auth_file)
            .context("Failed to read auth file")?;
        
        let lines: Vec<&str> = content.lines()
            .filter(|line| !line.contains(registry) || !line.contains("_authToken"))
            .collect();
        
        let new_content = lines.join("\n");
        fs::write(&self.auth_file, new_content)
            .context("Failed to write auth file")?;
        
        debug!("Removed auth for registry: {}", registry);
        Ok(())
    }
}

fn get_auth_file_path() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .context("Could not determine home directory")?;
    
    Ok(home_dir.join(".npmrc"))
}
