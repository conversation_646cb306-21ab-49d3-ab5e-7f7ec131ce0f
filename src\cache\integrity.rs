use anyhow::{Result, Context};
use tracing::{debug, warn};

use crate::utils::HashUtils;

/// Integrity checker for cached packages
#[derive(Debug, Clone)]
pub struct IntegrityChecker {
    // Configuration for integrity checking
}

impl IntegrityChecker {
    pub fn new() -> Self {
        Self {}
    }
    
    /// Verify data integrity using npm-style integrity string
    pub fn verify(&self, data: &[u8], expected_integrity: &str) -> Result<()> {
        debug!("Verifying integrity: {}", expected_integrity);
        
        if HashUtils::verify_integrity(data, expected_integrity)? {
            debug!("Integrity verification passed");
            Ok(())
        } else {
            warn!("Integrity verification failed for: {}", expected_integrity);
            Err(anyhow::anyhow!("Integrity verification failed"))
        }
    }
    
    /// Generate integrity string for data
    pub fn generate(&self, data: &[u8]) -> String {
        HashUtils::generate_integrity(data, "sha512")
            .unwrap_or_else(|_| "sha512-invalid".to_string())
    }
    
    /// Verify multiple integrity strings (npm supports multiple algorithms)
    pub fn verify_multiple(&self, data: &[u8], integrity_strings: &[String]) -> Result<()> {
        if integrity_strings.is_empty() {
            return Err(anyhow::anyhow!("No integrity strings provided"));
        }
        
        let mut last_error = None;
        
        for integrity in integrity_strings {
            match self.verify(data, integrity) {
                Ok(()) => return Ok(()),
                Err(e) => last_error = Some(e),
            }
        }
        
        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("All integrity checks failed")))
    }
    
    /// Check if an integrity string is valid format
    pub fn is_valid_format(&self, integrity: &str) -> bool {
        if let Some((algorithm, hash)) = integrity.split_once('-') {
            matches!(algorithm, "sha256" | "sha512") && !hash.is_empty()
        } else {
            false
        }
    }
    
    /// Extract algorithm from integrity string
    pub fn get_algorithm(&self, integrity: &str) -> Option<String> {
        integrity.split_once('-').map(|(alg, _)| alg.to_string())
    }
    
    /// Extract hash from integrity string
    pub fn get_hash(&self, integrity: &str) -> Option<String> {
        integrity.split_once('-').map(|(_, hash)| hash.to_string())
    }
}
