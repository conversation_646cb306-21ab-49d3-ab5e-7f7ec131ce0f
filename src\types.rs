use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub main: Option<String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub bin: Option<HashMap<String, String>>,
    pub scripts: Option<HashMap<String, String>>,
    pub engines: Option<HashMap<String, String>>,
    pub os: Option<Vec<String>>,
    pub cpu: Option<Vec<String>>,
    pub license: Option<String>,
    pub author: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<Repository>,
    pub bugs: Option<String>,
    pub keywords: Option<Vec<String>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Repository {
    #[serde(rename = "type")]
    pub repo_type: Option<String>,
    pub url: String,
    pub directory: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageMetadata {
    pub name: String,
    pub description: Option<String>,
    pub versions: HashMap<String, VersionMetadata>,
    pub latest: String,
    pub time: HashMap<String, String>,
    pub maintainers: Option<Vec<Maintainer>>,
    pub license: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<Repository>,
    pub bugs: Option<String>,
    pub keywords: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionMetadata {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub main: Option<String>,
    pub dependencies: HashMap<String, String>,
    #[serde(rename = "devDependencies")]
    pub dev_dependencies: HashMap<String, String>,
    #[serde(rename = "peerDependencies")]
    pub peer_dependencies: HashMap<String, String>,
    #[serde(rename = "optionalDependencies")]
    pub optional_dependencies: HashMap<String, String>,
    pub bin: Option<HashMap<String, String>>,
    pub scripts: Option<HashMap<String, String>>,
    pub engines: Option<HashMap<String, String>>,
    pub os: Option<Vec<String>>,
    pub cpu: Option<Vec<String>>,
    pub dist: DistInfo,
    pub license: Option<String>,
    pub author: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<Repository>,
    pub bugs: Option<String>,
    pub keywords: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DistInfo {
    pub tarball: String,
    pub shasum: String,
    pub integrity: Option<String>,
    #[serde(rename = "unpackedSize")]
    pub unpacked_size: Option<u64>,
    #[serde(rename = "fileCount")]
    pub file_count: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Maintainer {
    pub name: String,
    pub email: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ResolvedPackage {
    pub name: String,
    pub version: String,
    pub resolved_url: String,
    pub integrity: Option<String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub bin: Option<HashMap<String, String>>,
    pub engines: Option<HashMap<String, String>>,
    pub os: Option<Vec<String>>,
    pub cpu: Option<Vec<String>>,
    pub size: Option<u64>,
}

#[derive(Debug, Clone)]
pub struct DependencyNode {
    pub name: String,
    pub version: String,
    pub spec: String,
    pub resolved: Option<ResolvedPackage>,
    pub children: Vec<String>,
    pub depth: usize,
    pub dev: bool,
    pub optional: bool,
    pub peer: bool,
}

#[derive(Debug, Clone)]
pub struct DependencyTree {
    pub nodes: HashMap<String, DependencyNode>,
    pub root: String,
}

#[derive(Debug, Clone)]
pub struct InstallPlan {
    pub packages: Vec<InstallPackage>,
    pub total_size: u64,
    pub total_count: usize,
}

#[derive(Debug, Clone)]
pub struct InstallPackage {
    pub name: String,
    pub version: String,
    pub resolved_url: String,
    pub integrity: Option<String>,
    pub install_path: PathBuf,
    pub size: Option<u64>,
    pub bin: Option<HashMap<String, String>>,
    pub engines: Option<HashMap<String, String>>,
    pub os: Option<Vec<String>>,
    pub cpu: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LockfileEntry {
    pub version: String,
    pub resolved: String,
    pub integrity: Option<String>,
    pub dependencies: Option<HashMap<String, String>>,
    pub dev: Option<bool>,
    pub optional: Option<bool>,
    pub peer: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Lockfile {
    pub name: Option<String>,
    pub version: Option<String>,
    #[serde(rename = "lockfileVersion")]
    pub lockfile_version: u32,
    pub requires: Option<bool>,
    pub packages: HashMap<String, LockfileEntry>,
    pub dependencies: Option<HashMap<String, LockfileEntry>>,
}

#[derive(Debug, Clone)]
pub struct CacheEntry {
    pub key: String,
    pub data: Vec<u8>,
    pub metadata: CacheMetadata,
}

#[derive(Debug, Clone)]
pub struct CacheMetadata {
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub accessed_at: chrono::DateTime<chrono::Utc>,
    pub size: u64,
    pub hash: String,
}

#[derive(Debug, Clone)]
pub struct RegistryConfig {
    pub url: String,
    pub auth_token: Option<String>,
    pub timeout: std::time::Duration,
    pub retries: u32,
}

#[derive(Debug, Clone)]
pub struct InstallConfig {
    pub node_modules_path: PathBuf,
    pub cache_path: PathBuf,
    pub global_path: Option<PathBuf>,
    pub production: bool,
    pub optional: bool,
    pub dev: bool,
    pub save_exact: bool,
    pub force: bool,
    pub dry_run: bool,
}

#[derive(Debug, Clone)]
pub struct AuditResult {
    pub vulnerabilities: Vec<Vulnerability>,
    pub total_count: usize,
    pub severity_counts: HashMap<String, usize>,
}

#[derive(Debug, Clone)]
pub struct Vulnerability {
    pub id: String,
    pub title: String,
    pub severity: String,
    pub vulnerable_versions: String,
    pub patched_versions: Option<String>,
    pub module_name: String,
    pub overview: String,
    pub recommendation: String,
    pub url: String,
}

impl Default for RegistryConfig {
    fn default() -> Self {
        Self {
            url: "https://registry.npmjs.org".to_string(),
            auth_token: None,
            timeout: std::time::Duration::from_secs(30),
            retries: 5,
        }
    }
}

impl Default for InstallConfig {
    fn default() -> Self {
        Self {
            node_modules_path: PathBuf::from("node_modules"),
            cache_path: dirs::cache_dir()
                .unwrap_or_else(|| PathBuf::from("."))
                .join("nx"),
            global_path: None,
            production: false,
            optional: true,
            dev: true,
            save_exact: false,
            force: false,
            dry_run: false,
        }
    }
}
