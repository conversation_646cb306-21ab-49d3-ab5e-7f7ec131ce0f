use anyhow::{Result, Context};
use std::process::Command;
use std::path::Path;
use crate::ui::UI;

pub async fn execute(script: String, args: Vec<String>) -> Result<()> {
    // Clean output - just use println! for npm-like behavior

    // Check if it's a JavaScript file to run directly
    if script.ends_with(".js") || script.ends_with(".mjs") || script.ends_with(".cjs") {
        return run_javascript_file(&script, &args).await;
    }

    // Try to read package.json to find the script
    if let Ok(package_json) = std::fs::read_to_string("package.json") {
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&package_json) {
            if let Some(scripts) = parsed.get("scripts").and_then(|s| s.as_object()) {
                if let Some(script_command) = scripts.get(&script).and_then(|s| s.as_str()) {
                    println!("\n> {}", script_command);
                    return run_script_command(script_command, &args).await;
                }
            }
        }
    }

    // If not found in package.json, check if it's a common Node.js command
    match script.as_str() {
        "node" => run_node_command(&args).await,
        _ => {
            eprintln!("npm ERR! Missing script: \"{}\"", script);
            eprintln!("npm ERR! Available scripts:");
            list_available_scripts().await?;
            std::process::exit(1);
        }
    }
}

async fn run_javascript_file(file: &str, args: &[String]) -> Result<()> {
    if !Path::new(file).exists() {
        eprintln!("npm ERR! Cannot find module '{}'", file);
        std::process::exit(1);
    }

    println!("\n> node {}", file);

    let mut cmd = Command::new("node");
    cmd.arg(file);
    cmd.args(args);

    let status = cmd.status()
        .context("Failed to execute JavaScript file")?;

    if !status.success() {
        std::process::exit(status.code().unwrap_or(1));
    }

    Ok(())
}

async fn run_script_command(script_command: &str, args: &[String]) -> Result<()> {
    let mut full_command = script_command.to_string();
    if !args.is_empty() {
        full_command.push(' ');
        full_command.push_str(&args.join(" "));
    }

    let mut cmd = if cfg!(target_os = "windows") {
        let mut cmd = Command::new("cmd");
        cmd.args(&["/C", &full_command]);
        cmd
    } else {
        let mut cmd = Command::new("sh");
        cmd.args(&["-c", &full_command]);
        cmd
    };

    let status = cmd.status()
        .context("Failed to execute script")?;

    if !status.success() {
        std::process::exit(status.code().unwrap_or(1));
    }

    Ok(())
}

async fn run_node_command(args: &[String]) -> Result<()> {
    if args.is_empty() {
        eprintln!("npm ERR! Missing required argument");
        std::process::exit(1);
    }

    println!("\n> node {}", args.join(" "));

    let mut cmd = Command::new("node");
    cmd.args(args);

    let status = cmd.status()
        .context("Failed to execute node command")?;

    if !status.success() {
        std::process::exit(status.code().unwrap_or(1));
    }

    Ok(())
}

async fn list_available_scripts() -> Result<()> {
    if let Ok(package_json) = std::fs::read_to_string("package.json") {
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&package_json) {
            if let Some(scripts) = parsed.get("scripts").and_then(|s| s.as_object()) {
                for (name, _) in scripts {
                    println!("  {}", name);
                }
                return Ok(());
            }
        }
    }
    println!("  (none)");
    Ok(())
}
