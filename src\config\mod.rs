use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tracing::debug;

/// Configuration manager for nx
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub registry: RegistryConfig,
    pub cache: CacheConfig,
    pub install: InstallConfig,
    pub ui: UIConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RegistryConfig {
    pub url: String,
    pub timeout: u64,
    pub retry_attempts: u32,
    pub mirrors: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub dir: PathBuf,
    pub max_size_gb: Option<u64>,
    pub max_age_days: u64,
    pub cleanup_on_exit: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct InstallConfig {
    pub parallel_downloads: u32,
    pub parallel_extractions: u32,
    pub verify_integrity: bool,
    pub prefer_offline: bool,
    pub hoist_dependencies: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UIConfig {
    pub progress_bars: bool,
    pub colors: bool,
    pub verbose: bool,
    pub quiet: bool,
}

impl Default for Config {
    fn default() -> Self {
        let cache_dir = dirs::cache_dir()
            .unwrap_or_else(|| dirs::home_dir().unwrap().join(".cache"))
            .join("nx");
        
        Self {
            registry: RegistryConfig {
                url: "https://registry.npmjs.org".to_string(),
                timeout: 30,
                retry_attempts: 3,
                mirrors: vec![],
            },
            cache: CacheConfig {
                dir: cache_dir,
                max_size_gb: Some(5),
                max_age_days: 30,
                cleanup_on_exit: false,
            },
            install: InstallConfig {
                parallel_downloads: 8,
                parallel_extractions: 4,
                verify_integrity: true,
                prefer_offline: false,
                hoist_dependencies: true,
            },
            ui: UIConfig {
                progress_bars: true,
                colors: true,
                verbose: false,
                quiet: false,
            },
        }
    }
}

impl Config {
    /// Load configuration from file
    pub fn load() -> Result<Self> {
        let config_path = Self::get_config_path()?;
        
        if !config_path.exists() {
            debug!("No config file found, using defaults");
            return Ok(Self::default());
        }
        
        let content = std::fs::read_to_string(&config_path)
            .context("Failed to read config file")?;
        
        let config: Config = toml::from_str(&content)
            .context("Failed to parse config file")?;
        
        debug!("Loaded config from {}", config_path.display());
        Ok(config)
    }
    
    /// Save configuration to file
    pub fn save(&self) -> Result<()> {
        let config_path = Self::get_config_path()?;
        
        // Ensure config directory exists
        if let Some(parent) = config_path.parent() {
            std::fs::create_dir_all(parent)
                .context("Failed to create config directory")?;
        }
        
        let content = toml::to_string_pretty(self)
            .context("Failed to serialize config")?;
        
        std::fs::write(&config_path, content)
            .context("Failed to write config file")?;
        
        debug!("Saved config to {}", config_path.display());
        Ok(())
    }
    
    /// Get configuration file path
    fn get_config_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .or_else(|| dirs::home_dir().map(|h| h.join(".config")))
            .context("Could not determine config directory")?;
        
        Ok(config_dir.join("nx").join("config.toml"))
    }
    
    /// Get a configuration value by key
    pub fn get(&self, key: &str) -> Option<String> {
        match key {
            "registry.url" => Some(self.registry.url.clone()),
            "registry.timeout" => Some(self.registry.timeout.to_string()),
            "registry.retry_attempts" => Some(self.registry.retry_attempts.to_string()),
            "cache.dir" => Some(self.cache.dir.display().to_string()),
            "cache.max_size_gb" => self.cache.max_size_gb.map(|s| s.to_string()),
            "cache.max_age_days" => Some(self.cache.max_age_days.to_string()),
            "install.parallel_downloads" => Some(self.install.parallel_downloads.to_string()),
            "install.parallel_extractions" => Some(self.install.parallel_extractions.to_string()),
            "install.verify_integrity" => Some(self.install.verify_integrity.to_string()),
            "ui.progress_bars" => Some(self.ui.progress_bars.to_string()),
            "ui.colors" => Some(self.ui.colors.to_string()),
            _ => None,
        }
    }
    
    /// Set a configuration value by key
    pub fn set(&mut self, key: &str, value: &str) -> Result<()> {
        match key {
            "registry.url" => self.registry.url = value.to_string(),
            "registry.timeout" => {
                self.registry.timeout = value.parse()
                    .context("Invalid timeout value")?;
            }
            "registry.retry_attempts" => {
                self.registry.retry_attempts = value.parse()
                    .context("Invalid retry attempts value")?;
            }
            "cache.dir" => self.cache.dir = PathBuf::from(value),
            "cache.max_size_gb" => {
                self.cache.max_size_gb = Some(value.parse()
                    .context("Invalid max size value")?);
            }
            "cache.max_age_days" => {
                self.cache.max_age_days = value.parse()
                    .context("Invalid max age value")?;
            }
            "install.parallel_downloads" => {
                self.install.parallel_downloads = value.parse()
                    .context("Invalid parallel downloads value")?;
            }
            "install.verify_integrity" => {
                self.install.verify_integrity = value.parse()
                    .context("Invalid verify integrity value")?;
            }
            "ui.progress_bars" => {
                self.ui.progress_bars = value.parse()
                    .context("Invalid progress bars value")?;
            }
            "ui.colors" => {
                self.ui.colors = value.parse()
                    .context("Invalid colors value")?;
            }
            _ => return Err(anyhow::anyhow!("Unknown configuration key: {}", key)),
        }
        
        Ok(())
    }
}
