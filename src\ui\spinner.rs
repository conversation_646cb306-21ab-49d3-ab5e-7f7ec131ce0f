use indicatif::{ProgressBar, ProgressStyle};
use std::time::Duration;

/// Spinner configurations for different operations
pub struct SpinnerConfig;

impl SpinnerConfig {
    /// Default spinner style
    pub fn default_style() -> ProgressStyle {
        ProgressStyle::default_spinner()
            .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
            .template("{spinner:.blue} {msg}")
            .unwrap()
    }
    
    /// Dots spinner style
    pub fn dots_style() -> ProgressStyle {
        ProgressStyle::default_spinner()
            .tick_strings(&["⠁", "⠂", "⠄", "⡀", "⢀", "⠠", "⠐", "⠈"])
            .template("{spinner:.green} {msg}")
            .unwrap()
    }
    
    /// Arrow spinner style
    pub fn arrow_style() -> ProgressStyle {
        ProgressStyle::default_spinner()
            .tick_strings(&["←", "↖", "↑", "↗", "→", "↘", "↓", "↙"])
            .template("{spinner:.yellow} {msg}")
            .unwrap()
    }
    
    /// Clock spinner style
    pub fn clock_style() -> ProgressStyle {
        ProgressStyle::default_spinner()
            .tick_strings(&["🕐", "🕑", "🕒", "🕓", "🕔", "🕕", "🕖", "🕗", "🕘", "🕙", "🕚", "🕛"])
            .template("{spinner} {msg}")
            .unwrap()
    }
}

/// Spinner factory for different operations
pub struct SpinnerFactory;

impl SpinnerFactory {
    /// Create a spinner for resolving dependencies
    pub fn resolving() -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(SpinnerConfig::default_style());
        spinner.set_message("Resolving dependencies...");
        spinner.enable_steady_tick(Duration::from_millis(100));
        spinner
    }
    
    /// Create a spinner for downloading packages
    pub fn downloading() -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(SpinnerConfig::dots_style());
        spinner.set_message("Downloading packages...");
        spinner.enable_steady_tick(Duration::from_millis(80));
        spinner
    }
    
    /// Create a spinner for extracting packages
    pub fn extracting() -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(SpinnerConfig::arrow_style());
        spinner.set_message("Extracting packages...");
        spinner.enable_steady_tick(Duration::from_millis(120));
        spinner
    }
    
    /// Create a spinner for linking packages
    pub fn linking() -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(SpinnerConfig::default_style());
        spinner.set_message("Linking packages...");
        spinner.enable_steady_tick(Duration::from_millis(100));
        spinner
    }
    
    /// Create a spinner for cache operations
    pub fn cache_operation(message: &str) -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(SpinnerConfig::clock_style());
        spinner.set_message(message.to_string());
        spinner.enable_steady_tick(Duration::from_millis(200));
        spinner
    }
    
    /// Create a custom spinner with message
    pub fn custom(message: &str, style: ProgressStyle) -> ProgressBar {
        let spinner = ProgressBar::new_spinner();
        spinner.set_style(style);
        spinner.set_message(message.to_string());
        spinner.enable_steady_tick(Duration::from_millis(100));
        spinner
    }
}
