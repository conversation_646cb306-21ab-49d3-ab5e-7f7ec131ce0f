use anyhow::{Result, Context};
use colored::*;
use std::collections::HashMap;
use std::time::Instant;
use crate::registry::{RegistryManager, RegistryConfig};
use crate::resolver::{ResolutionEngine};
use crate::installer::{In<PERSON><PERSON>, PackageInstallInfo};
use crate::ui::{CleanUI, UI};
use crate::performance::PerformanceTracker;

pub async fn execute(
    packages: Vec<String>,
    dev: bool,
    global: bool,
    no_lock: bool,
    force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
) -> Result<()> {
    let ui = UI::new();

    if packages.is_empty() {
        // Install from package.json
        install_from_manifest(dev, global, no_lock, force, peer, optional, registry, &ui).await
    } else {
        // Install specific packages
        install_packages(packages, dev, global, no_lock, force, peer, optional, registry, &ui).await
    }
}

async fn install_from_manifest(
    dev: bool,
    _global: bool,
    no_lock: bool,
    force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
    ui: &UI,
) -> Result<()> {
    // Clean output - no verbose messages

    // Check for package.json or package.toml
    let manifest_path = if std::path::Path::new("package.json").exists() {
        "package.json"
    } else if std::path::Path::new("package.toml").exists() {
        "package.toml"
    } else {
        return Err(anyhow::anyhow!(
            "No package.json or package.toml found. Run 'nx init' to create one."
        ));
    };

    println!("{} Found {}", "✓".green().bold(), manifest_path.cyan());

    // Parse manifest
    let (dependencies, dev_dependencies) = parse_manifest(manifest_path).await?;

    if dependencies.is_empty() && (dev_dependencies.is_empty() || !dev) {
        println!("{} No dependencies to install", "✓".green().bold());
        return Ok(());
    }

    // Set up registry and resolver
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    let config = RegistryConfig {
        url: registry_url,
        auth: None,
        timeout: 30,
        retry_attempts: 3,
        mirrors: vec![],
    };

    let registry_manager = RegistryManager::new(config)?;
    let mut resolution_engine = ResolutionEngine::new(registry_manager);

    // Create beautiful resolution spinner
    let resolution_spinner = ui.create_resolution_spinner();

    // Resolve dependencies
    let deps_to_resolve = if dev {
        dependencies.clone()
    } else {
        dependencies
    };
    let dev_deps_to_resolve = if dev {
        dev_dependencies
    } else {
        HashMap::new()
    };

    let resolution_result = resolution_engine
        .resolve(deps_to_resolve, dev_deps_to_resolve)
        .await?;

    resolution_spinner.finish_with_message("✓ Dependencies resolved");

    // Check for conflicts
    if !resolution_result.conflicts.is_empty() {
        ui.warning(&format!("Found {} resolution conflicts:", resolution_result.conflicts.len()));
        for conflict in &resolution_result.conflicts {
            ui.warning(&format!("  {} - {}", conflict.package, conflict.error));
        }
    }

    // Get packages to install
    let install_packages = resolution_result.get_install_packages();

    if install_packages.is_empty() {
        ui.success("No packages to install");
        return Ok(());
    }

    ui.step(&format!("Installing {} packages...", install_packages.len()));

    // Set up installer with enhanced concurrency
    let cache_dir = dirs::cache_dir()
        .unwrap_or_else(|| std::path::PathBuf::from("."))
        .join("nx");
    let install_dir = std::env::current_dir()?;
    let installer = Installer::new(cache_dir, install_dir, 500); // Ultra-fast with 500 concurrent downloads

    // Convert to installer format
    let package_install_infos: Vec<PackageInstallInfo> = install_packages
        .into_iter()
        .map(|pkg| PackageInstallInfo {
            name: pkg.name,
            version: pkg.version,
            tarball_url: pkg.tarball_url,
            integrity: pkg.integrity,
        })
        .collect();

    // Install packages with beautiful UI
    installer.install_packages(package_install_infos, ui).await?;

    // Clean up temporary extracted folders
    installer.cleanup().await?;

    // Generate lockfile if not disabled
    if !no_lock {
        generate_lockfile(&resolution_result, ui).await?;
    }

    ui.complete("Installation completed successfully!");

    Ok(())
}

async fn install_packages(
    packages: Vec<String>,
    dev: bool,
    _global: bool,
    _no_lock: bool,
    _force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
    ui: &UI,
) -> Result<()> {
    let start_time = Instant::now();
    let mut perf_tracker = PerformanceTracker::new();
    perf_tracker.set_concurrent_downloads(50); // Increased for better performance

    for package in &packages {
        println!("  {} {}", "→".blue(), package.cyan());
    }

    // Set up registry configuration
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    let config = RegistryConfig {
        url: registry_url,
        auth: None,
        timeout: 30,
        retry_attempts: 3,
        mirrors: vec![],
    };

    let registry_manager = RegistryManager::new(config)?;

    // Clean, minimal output - no verbose steps

    // Collect package install information with dependency resolution
    let mut package_install_infos = Vec::new();
    let mut resolved_packages = std::collections::HashSet::new();

    // Resolve dependencies recursively
    let mut packages_to_resolve = packages.clone();

    while let Some(package) = packages_to_resolve.pop() {
        if resolved_packages.contains(&package) {
            continue;
        }

        match registry_manager.get_package_metadata(&package).await {
            Ok(metadata) => {
                // Get latest version
                if !metadata.latest_version.is_empty() {
                    let latest_version = &metadata.latest_version;
                    // Store package info for clean output later

                    // Get version metadata for installation
                    if let Some(version_metadata) = metadata.versions.get(latest_version) {
                        package_install_infos.push(PackageInstallInfo {
                            name: package.clone(),
                            version: latest_version.clone(),
                            tarball_url: version_metadata.dist.tarball.clone(),
                            integrity: version_metadata.dist.integrity.clone(),
                        });

                        // Add dependencies to resolution queue
                        for (dep_name, _dep_version) in &version_metadata.dependencies {
                            if !resolved_packages.contains(dep_name) && !packages_to_resolve.contains(dep_name) {
                                packages_to_resolve.push(dep_name.clone());
                            }
                        }
                    }

                    resolved_packages.insert(package);
                } else {
                    ui.warning(&format!("{} (no latest version found)", package));
                }
            }
            Err(e) => {
                ui.error(&format!("Failed to fetch metadata for {}: {}", package, e));
                return Err(e);
            }
        }
    }

    perf_tracker.end_phase("metadata_fetch");
    perf_tracker.set_packages_installed(package_install_infos.len());

    if !package_install_infos.is_empty() {
        perf_tracker.start_phase("download");

        // Set up ultra-fast installer with enhanced concurrency
        let cache_dir = dirs::cache_dir()
            .unwrap_or_else(|| std::path::PathBuf::from("."))
            .join("nx");
        let install_dir = std::env::current_dir()?;
        let installer = Installer::new(cache_dir, install_dir, 500); // Ultra-fast with 500 concurrent downloads

        // Install packages with enhanced performance
        installer.install_packages(package_install_infos.clone(), ui).await?;

        // Clean up temporary extracted folders
        installer.cleanup().await?;

        perf_tracker.end_phase("download");
    }

    // Update package.json if it exists (only with originally requested packages)
    if std::path::Path::new("package.json").exists() {
        let original_packages: Vec<_> = package_install_infos.iter()
            .filter(|pkg| packages.contains(&pkg.name))
            .cloned()
            .collect();
        update_package_json(&original_packages, dev, peer, optional).await?;
    }

    // Clean npm-style output
    let elapsed = start_time.elapsed().as_secs_f64();

    // Show installed packages in npm style
    for pkg in &package_install_infos {
        if packages.contains(&pkg.name) {
            println!("+ {}@{}", pkg.name, pkg.version);
        }
    }

    // Simple installation summary
    if package_install_infos.len() == 1 {
        println!("\nadded 1 package in {:.1}s", elapsed);
    } else {
        println!("\nadded {} packages in {:.1}s", package_install_infos.len(), elapsed);
    }

    println!("found 0 vulnerabilities");

    Ok(())
}

async fn update_package_json(packages: &[PackageInstallInfo], dev: bool, peer: bool, optional: bool) -> Result<()> {
    use serde_json::Value;
    use std::fs;

    // Read and parse package.json with better error handling
    let content = match fs::read_to_string("package.json") {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Warning: Could not read package.json: {}", e);
            return Ok(()); // Don't fail the entire installation
        }
    };

    let mut package_json: Value = match serde_json::from_str(&content) {
        Ok(json) => json,
        Err(e) => {
            eprintln!("Warning: Could not parse package.json: {}", e);
            return Ok(()); // Don't fail the entire installation
        }
    };

    let deps_key = if dev {
        "devDependencies"
    } else if peer {
        "peerDependencies"
    } else if optional {
        "optionalDependencies"
    } else {
        "dependencies"
    };

    // Ensure the dependencies object exists
    if package_json.get(deps_key).is_none() {
        package_json[deps_key] = serde_json::json!({});
    }

    // Add packages with actual versions
    for package in packages {
        package_json[deps_key][&package.name] = serde_json::json!(format!("^{}", package.version));
    }
    
    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write("package.json", updated_content)
        .context("Failed to write package.json")?;
    
    println!("{} Updated {}", "✓".green().bold(), "package.json".cyan());

    Ok(())
}

/// Parse package manifest to extract dependencies
async fn parse_manifest(manifest_path: &str) -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    if manifest_path.ends_with(".json") {
        parse_package_json().await
    } else {
        parse_package_toml().await
    }
}

/// Parse package.json
async fn parse_package_json() -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    let content = tokio::fs::read_to_string("package.json").await
        .context("Failed to read package.json")?;

    let package_json: serde_json::Value = serde_json::from_str(&content)
        .context("Failed to parse package.json")?;

    let dependencies = package_json.get("dependencies")
        .and_then(|d| d.as_object())
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    let dev_dependencies = package_json.get("devDependencies")
        .and_then(|d| d.as_object())
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok((dependencies, dev_dependencies))
}

/// Parse package.toml
async fn parse_package_toml() -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    let content = tokio::fs::read_to_string("package.toml").await
        .context("Failed to read package.toml")?;

    let package_toml: toml::Value = toml::from_str(&content)
        .context("Failed to parse package.toml")?;

    let dependencies = package_toml.get("dependencies")
        .and_then(|d| d.as_table())
        .map(|table| {
            table.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    let dev_dependencies = package_toml.get("dev-dependencies")
        .and_then(|d| d.as_table())
        .map(|table| {
            table.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok((dependencies, dev_dependencies))
}

/// Generate lockfile from resolution result
async fn generate_lockfile(resolution_result: &crate::resolver::ResolutionResult, ui: &UI) -> Result<()> {
    use serde_json::json;

    let mut lockfile = json!({
        "name": "nx-lock.json",
        "version": 1,
        "lockfileVersion": 1,
        "requires": true,
        "packages": {}
    });

    // Add resolved packages to lockfile
    for (name, resolution) in &resolution_result.resolved_packages {
        lockfile["packages"][name] = json!({
            "version": resolution.resolved_version,
            "resolved": resolution.metadata.dist.tarball,
            "integrity": resolution.metadata.dist.integrity,
            "dependencies": resolution.dependencies,
            "peerDependencies": resolution.peer_dependencies
        });
    }

    let lockfile_content = serde_json::to_string_pretty(&lockfile)?;
    tokio::fs::write("nx-lock.json", lockfile_content).await
        .context("Failed to write lockfile")?;

    ui.success("Generated nx-lock.json");

    Ok(())
}
