use anyhow::{Result, Context};
use std::collections::HashMap;
use std::time::Instant;
use crate::registry::Registry;
use crate::resolver::Resolver;
use crate::installer::{Installer, PackageInstallInfo};
use crate::ui::UI;
use crate::package::PackageJson;

/// Ultra-fast package installation with sub-3 second performance
pub async fn execute(
    packages: Vec<String>,
    dev: bool,
    global: bool,
    no_lock: bool,
    force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
) -> Result<()> {
    let start_time = Instant::now();
    let ui = UI::new();

    let result = if packages.is_empty() {
        // Install from package.json
        install_from_manifest(dev, global, no_lock, force, peer, optional, registry, &ui).await
    } else {
        // Install specific packages
        install_packages(packages, dev, global, no_lock, force, peer, optional, registry, &ui).await
    };

    let total_time = start_time.elapsed();
    if total_time.as_secs_f64() > 3.0 {
        ui.warning(&format!("Installation took {:.2}s (target: <3s)", total_time.as_secs_f64()));
    }

    result
}

/// Ultra-fast installation from package.json
async fn install_from_manifest(
    dev: bool,
    _global: bool,
    no_lock: bool,
    force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
    ui: &UI,
) -> Result<()> {
    let start_time = Instant::now();
    
    // Check for package.json
    if !std::path::Path::new("package.json").exists() {
        return Err(anyhow::anyhow!("No package.json found. Run 'nx init' to create one."));
    }

    ui.info("📦 Installing dependencies...");
    
    // Read package.json
    let package_json = PackageJson::read("package.json").await
        .context("Failed to read package.json")?;
    
    // Collect dependencies to install
    let mut all_dependencies = HashMap::new();
    
    // Add regular dependencies
    for (name, version) in &package_json.dependencies {
        all_dependencies.insert(name.clone(), version.clone());
    }
    
    // Add dev dependencies if requested
    if dev {
        for (name, version) in &package_json.dev_dependencies {
            all_dependencies.insert(name.clone(), version.clone());
        }
    }
    
    if all_dependencies.is_empty() {
        ui.success("No dependencies to install");
        return Ok(());
    }
    
    // Ultra-fast dependency resolution and installation
    install_dependency_map(all_dependencies, ui).await?;
    
    let total_time = start_time.elapsed();
    ui.success(&format!("🎉 Installed dependencies in {:.2}s", total_time.as_secs_f64()));
    
    Ok(())
}

/// Ultra-fast installation of specific packages
async fn install_packages(
    packages: Vec<String>,
    dev: bool,
    _global: bool,
    no_lock: bool,
    force: bool,
    peer: bool,
    optional: bool,
    registry: Option<String>,
    ui: &UI,
) -> Result<()> {
    let start_time = Instant::now();
    
    ui.info(&format!("📦 Installing {} packages...", packages.len()));
    
    // Create dependencies map with latest versions
    let mut dependencies = HashMap::new();
    for package in &packages {
        dependencies.insert(package.clone(), "latest".to_string());
    }
    
    // Ultra-fast dependency resolution and installation
    install_dependency_map(dependencies, ui).await?;
    
    // Update package.json if it exists
    if std::path::Path::new("package.json").exists() {
        update_package_json(&packages, dev).await?;
    }
    
    let total_time = start_time.elapsed();
    ui.success(&format!("🎉 Installed {} packages in {:.2}s", packages.len(), total_time.as_secs_f64()));
    
    Ok(())
}

/// Core ultra-fast installation logic
async fn install_dependency_map(dependencies: HashMap<String, String>, ui: &UI) -> Result<()> {
    let resolution_start = Instant::now();
    
    // Ultra-fast dependency resolution
    let registry = Registry::new();
    let resolver = Resolver::new(registry.clone());
    
    let dependency_graph = resolver.resolve_dependencies(&dependencies).await
        .context("Failed to resolve dependencies")?;
    
    let resolution_time = resolution_start.elapsed();
    
    // Convert resolved packages to install info
    let mut install_packages = Vec::new();
    for node in dependency_graph.get_install_order() {
        if let Ok(metadata) = registry.get_package_metadata(&node.name).await {
            if let Some(version_info) = metadata.versions.get(&node.version) {
                install_packages.push(PackageInstallInfo {
                    name: node.name.clone(),
                    version: node.version.clone(),
                    tarball_url: version_info.dist.tarball.clone(),
                    integrity: version_info.dist.integrity.clone(),
                });
            }
        }
    }
    
    if install_packages.is_empty() {
        ui.warning("No packages to install");
        return Ok(());
    }
    
    // Ultra-fast parallel installation
    let cache_dir = std::env::current_dir()?.join(".nx-cache");
    let install_dir = std::env::current_dir()?;
    let installer = Installer::new(cache_dir, install_dir, 100); // Max concurrency
    
    let install_start = Instant::now();
    installer.install_packages(install_packages, ui).await
        .context("Failed to install packages")?;
    let install_time = install_start.elapsed();
    
    ui.info(&format!("✅ Resolution: {:.2}s, Installation: {:.2}s", 
        resolution_time.as_secs_f64(), 
        install_time.as_secs_f64()
    ));
    
    Ok(())
}

/// Update package.json with installed packages
async fn update_package_json(packages: &[String], dev: bool) -> Result<()> {
    let mut package_json = PackageJson::read("package.json").await
        .context("Failed to read package.json")?;
    
    // Add packages to appropriate dependency section
    for package in packages {
        let version = "^1.0.0"; // Placeholder - should get actual resolved version
        if dev {
            package_json.dev_dependencies.insert(package.clone(), version.to_string());
        } else {
            package_json.dependencies.insert(package.clone(), version.to_string());
        }
    }
    
    package_json.write("package.json").await
        .context("Failed to update package.json")?;
    
    Ok(())
}
