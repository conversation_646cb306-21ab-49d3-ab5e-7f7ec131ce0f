use anyhow::{Result, Context};
use flate2::read::GzDecoder;
use std::path::{Path, PathBuf};
use tar::Archive;
use tokio::fs;
use tracing::{debug, info, warn};
use std::fs::File;

use crate::utils::FsUtils;
use crate::ui::UI;

/// Package extractor for tarballs
#[derive(Debug, Clone)]
pub struct Extractor {
    extract_dir: PathBuf,
}

impl Extractor {
    pub fn new(extract_dir: PathBuf) -> Self {
        Self { extract_dir }
    }

    pub fn new_default() -> Self {
        let extract_dir = std::env::current_dir()
            .unwrap_or_else(|_| PathBuf::from("."));
        Self { extract_dir }
    }
    
    /// Extract a tarball directly to node_modules with UI feedback
    pub async fn extract(
        &self,
        tarball_path: &Path,
        name: &str,
        version: &str,
        ui: &UI,
    ) -> Result<PathBuf> {
        // Extract directly to final node_modules location
        let final_path = self.get_package_path(name);

        // Create extraction spinner
        let spinner = ui.create_extraction_spinner(name);

        // Remove existing directory if it exists
        if final_path.exists() {
            fs::remove_dir_all(&final_path).await
                .context("Failed to remove existing package directory")?;
        }

        // Ensure parent directory exists
        if let Some(parent) = final_path.parent() {
            FsUtils::create_dir_all(parent).await?;
        }

        info!("Extracting {}@{} directly to node_modules", name, version);

        // Extract in a blocking task to avoid blocking the async runtime
        let tarball_path_clone = tarball_path.to_owned();
        let final_path_clone = final_path.clone();
        tokio::task::spawn_blocking(move || {
            Self::extract_tarball_from_file(&tarball_path_clone, &final_path_clone)
        }).await??;

        spinner.finish_with_message(format!("✓ Extracted {}", name));

        debug!("Extracted {}@{} to {}", name, version, final_path.display());
        Ok(final_path)
    }
    
    /// Extract tarball data to a directory
    fn extract_tarball(data: &[u8], extract_path: &Path) -> Result<()> {
        let decoder = GzDecoder::new(data);
        let mut archive = Archive::new(decoder);
        
        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;
            
            // Skip the top-level package directory (usually "package/")
            let relative_path = if let Ok(stripped) = path.strip_prefix("package") {
                stripped
            } else {
                path.as_ref()
            };
            
            let target_path = extract_path.join(relative_path);
            
            // Ensure the parent directory exists
            if let Some(parent) = target_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            // Extract the entry
            entry.unpack(&target_path)?;
        }
        
        Ok(())
    }

    /// Extract tarball from file (blocking operation)
    fn extract_tarball_from_file(tarball_path: &Path, extract_path: &Path) -> Result<()> {
        let file = File::open(tarball_path)
            .context("Failed to open tarball")?;

        let decoder = GzDecoder::new(file);
        let mut archive = Archive::new(decoder);

        for entry in archive.entries()? {
            let mut entry = entry?;
            let path = entry.path()?;

            // Skip the top-level package directory (usually "package/")
            let relative_path = if let Ok(stripped) = path.strip_prefix("package") {
                stripped
            } else {
                path.as_ref()
            };

            let target_path = extract_path.join(relative_path);

            // Ensure the parent directory exists
            if let Some(parent) = target_path.parent() {
                std::fs::create_dir_all(parent)?;
            }

            // Extract the entry
            entry.unpack(&target_path)?;
        }

        Ok(())
    }

    /// Get the extraction path for a package
    fn get_extract_path(&self, name: &str, version: &str) -> PathBuf {
        let normalized_name = crate::utils::Utils::normalize_package_name(name);
        self.extract_dir
            .join(&normalized_name)
            .join(version)
    }
    
    /// Clean up extracted files
    pub async fn cleanup(&self) -> Result<()> {
        if self.extract_dir.exists() {
            FsUtils::remove_dir_all(&self.extract_dir).await?;
        }
        Ok(())
    }

    /// Get the final installation path for a package
    fn get_package_path(&self, name: &str) -> PathBuf {
        if name.starts_with('@') {
            // Scoped package
            let parts: Vec<&str> = name.splitn(2, '/').collect();
            if parts.len() == 2 {
                self.extract_dir
                    .join("node_modules")
                    .join(parts[0])
                    .join(parts[1])
            } else {
                self.extract_dir.join("node_modules").join(name)
            }
        } else {
            self.extract_dir.join("node_modules").join(name)
        }
    }
}
