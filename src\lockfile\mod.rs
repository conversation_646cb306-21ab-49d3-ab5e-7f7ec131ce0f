use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::fs;

/// Lockfile format for nx package manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NxLockfile {
    /// Lockfile format version
    pub lockfile_version: u32,
    /// Name of the project
    pub name: Option<String>,
    /// Version of the project
    pub version: Option<String>,
    /// Resolved packages
    pub packages: HashMap<String, LockedPackage>,
    /// Dependencies tree
    pub dependencies: HashMap<String, DependencyEntry>,
    /// Dev dependencies tree
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub dev_dependencies: HashMap<String, DependencyEntry>,
    /// Peer dependencies tree
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub peer_dependencies: HashMap<String, DependencyEntry>,
    /// Optional dependencies tree
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub optional_dependencies: HashMap<String, DependencyEntry>,
}

/// A locked package entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LockedPackage {
    /// Resolved version
    pub version: String,
    /// Resolved tarball URL
    pub resolved: String,
    /// Integrity hash
    pub integrity: String,
    /// Dependencies of this package
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub dependencies: HashMap<String, String>,
    /// Dev dependencies of this package
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub dev_dependencies: HashMap<String, String>,
    /// Peer dependencies of this package
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub peer_dependencies: HashMap<String, String>,
    /// Optional dependencies of this package
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub optional_dependencies: HashMap<String, String>,
    /// Whether this package is bundled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bundled: Option<bool>,
    /// Whether this package is dev-only
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dev: Option<bool>,
    /// Whether this package is optional
    #[serde(skip_serializing_if = "Option::is_none")]
    pub optional: Option<bool>,
}

/// Dependency entry in the lockfile
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyEntry {
    /// Version specifier
    pub version: String,
    /// Resolved version
    pub resolved: String,
    /// Integrity hash
    pub integrity: String,
    /// Nested dependencies
    #[serde(skip_serializing_if = "HashMap::is_empty")]
    pub dependencies: HashMap<String, DependencyEntry>,
}

impl NxLockfile {
    /// Create a new empty lockfile
    pub fn new() -> Self {
        Self {
            lockfile_version: 3, // Compatible with npm lockfile v3
            name: None,
            version: None,
            packages: HashMap::new(),
            dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            optional_dependencies: HashMap::new(),
        }
    }

    /// Load lockfile from nx-lock.json
    pub fn load<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref())
            .context("Failed to read nx-lock.json")?;
        
        let lockfile: NxLockfile = serde_json::from_str(&content)
            .context("Failed to parse nx-lock.json")?;
        
        Ok(lockfile)
    }

    /// Save lockfile to nx-lock.json
    pub fn save<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)
            .context("Failed to serialize lockfile")?;
        
        fs::write(path.as_ref(), content)
            .context("Failed to write nx-lock.json")?;
        
        Ok(())
    }

    /// Load from package-lock.json (npm compatibility)
    pub fn from_npm_lockfile<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref())
            .context("Failed to read package-lock.json")?;
        
        let npm_lockfile: serde_json::Value = serde_json::from_str(&content)
            .context("Failed to parse package-lock.json")?;
        
        let mut lockfile = NxLockfile::new();
        
        // Extract basic info
        if let Some(name) = npm_lockfile.get("name").and_then(|n| n.as_str()) {
            lockfile.name = Some(name.to_string());
        }
        
        if let Some(version) = npm_lockfile.get("version").and_then(|v| v.as_str()) {
            lockfile.version = Some(version.to_string());
        }
        
        // Convert packages
        if let Some(packages) = npm_lockfile.get("packages").and_then(|p| p.as_object()) {
            for (key, package) in packages {
                if key.is_empty() {
                    continue; // Skip root package
                }
                
                let locked_package = LockedPackage {
                    version: package.get("version")
                        .and_then(|v| v.as_str())
                        .unwrap_or("0.0.0")
                        .to_string(),
                    resolved: package.get("resolved")
                        .and_then(|r| r.as_str())
                        .unwrap_or("")
                        .to_string(),
                    integrity: package.get("integrity")
                        .and_then(|i| i.as_str())
                        .unwrap_or("")
                        .to_string(),
                    dependencies: extract_deps(package, "dependencies"),
                    dev_dependencies: extract_deps(package, "devDependencies"),
                    peer_dependencies: extract_deps(package, "peerDependencies"),
                    optional_dependencies: extract_deps(package, "optionalDependencies"),
                    bundled: package.get("bundled").and_then(|b| b.as_bool()),
                    dev: package.get("dev").and_then(|d| d.as_bool()),
                    optional: package.get("optional").and_then(|o| o.as_bool()),
                };
                
                lockfile.packages.insert(key.clone(), locked_package);
            }
        }
        
        Ok(lockfile)
    }

    /// Add a package to the lockfile
    pub fn add_package(&mut self, name: &str, package: LockedPackage) {
        self.packages.insert(name.to_string(), package);
    }

    /// Get a package from the lockfile
    pub fn get_package(&self, name: &str) -> Option<&LockedPackage> {
        self.packages.get(name)
    }

    /// Check if a package exists in the lockfile
    pub fn has_package(&self, name: &str) -> bool {
        self.packages.contains_key(name)
    }

    /// Remove a package from the lockfile
    pub fn remove_package(&mut self, name: &str) -> Option<LockedPackage> {
        self.packages.remove(name)
    }

    /// Update package metadata
    pub fn update_metadata(&mut self, name: Option<String>, version: Option<String>) {
        self.name = name;
        self.version = version;
    }

    /// Validate lockfile integrity
    pub fn validate(&self) -> Result<()> {
        // Check for missing required fields
        for (name, package) in &self.packages {
            if package.version.is_empty() {
                return Err(anyhow::anyhow!("Package {} missing version", name));
            }
            
            if package.resolved.is_empty() {
                return Err(anyhow::anyhow!("Package {} missing resolved URL", name));
            }
            
            if package.integrity.is_empty() {
                return Err(anyhow::anyhow!("Package {} missing integrity hash", name));
            }
        }
        
        Ok(())
    }

    /// Get all package names
    pub fn package_names(&self) -> Vec<String> {
        self.packages.keys().cloned().collect()
    }

    /// Get lockfile statistics
    pub fn stats(&self) -> LockfileStats {
        let total_packages = self.packages.len();
        let dev_packages = self.packages.values()
            .filter(|p| p.dev.unwrap_or(false))
            .count();
        let optional_packages = self.packages.values()
            .filter(|p| p.optional.unwrap_or(false))
            .count();
        
        LockfileStats {
            total_packages,
            dev_packages,
            optional_packages,
            production_packages: total_packages - dev_packages,
        }
    }
}

/// Lockfile statistics
#[derive(Debug, Clone)]
pub struct LockfileStats {
    pub total_packages: usize,
    pub production_packages: usize,
    pub dev_packages: usize,
    pub optional_packages: usize,
}

impl Default for NxLockfile {
    fn default() -> Self {
        Self::new()
    }
}

/// Extract dependencies from npm lockfile package entry
fn extract_deps(package: &serde_json::Value, key: &str) -> HashMap<String, String> {
    package.get(key)
        .and_then(|deps| deps.as_object())
        .map(|deps| {
            deps.iter()
                .filter_map(|(k, v)| v.as_str().map(|version| (k.clone(), version.to_string())))
                .collect()
        })
        .unwrap_or_default()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new_lockfile() {
        let lockfile = NxLockfile::new();
        assert_eq!(lockfile.lockfile_version, 3);
        assert!(lockfile.packages.is_empty());
    }

    #[test]
    fn test_add_package() {
        let mut lockfile = NxLockfile::new();
        let package = LockedPackage {
            version: "1.0.0".to_string(),
            resolved: "https://registry.npmjs.org/test/-/test-1.0.0.tgz".to_string(),
            integrity: "sha512-test".to_string(),
            dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            optional_dependencies: HashMap::new(),
            bundled: None,
            dev: None,
            optional: None,
        };
        
        lockfile.add_package("test", package);
        assert!(lockfile.has_package("test"));
    }
}
