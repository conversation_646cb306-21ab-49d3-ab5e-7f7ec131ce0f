pub mod fs;
pub mod hash;
pub mod time;

use anyhow::Result;
use std::path::Path;

pub use fs::*;
pub use hash::*;
pub use time::*;

/// Utility functions for nx
pub struct Utils;

impl Utils {
    /// Check if a path is a valid npm package directory
    pub fn is_package_dir<P: AsRef<Path>>(path: P) -> bool {
        let path = path.as_ref();
        path.join("package.json").exists() || path.join("package.toml").exists()
    }
    
    /// Get the package name from a path
    pub fn get_package_name<P: AsRef<Path>>(path: P) -> Result<String> {
        let path = path.as_ref();
        
        if let Some(name) = path.file_name() {
            if let Some(name_str) = name.to_str() {
                return Ok(name_str.to_string());
            }
        }
        
        Err(anyhow::anyhow!("Could not determine package name from path"))
    }
    
    /// Normalize a package name for file system storage
    pub fn normalize_package_name(name: &str) -> String {
        name.replace('/', "_")
            .replace('@', "_at_")
            .replace('\\', "_")
            .replace(':', "_")
            .replace('*', "_")
            .replace('?', "_")
            .replace('"', "_")
            .replace('<', "_")
            .replace('>', "_")
            .replace('|', "_")
    }
    
    /// Format bytes as human readable string
    pub fn format_bytes(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }
    
    /// Format duration as human readable string
    pub fn format_duration(duration: std::time::Duration) -> String {
        let total_secs = duration.as_secs();
        let millis = duration.subsec_millis();
        
        if total_secs >= 60 {
            let mins = total_secs / 60;
            let secs = total_secs % 60;
            format!("{}m {}s", mins, secs)
        } else if total_secs > 0 {
            format!("{}.{:03}s", total_secs, millis)
        } else {
            format!("{}ms", millis)
        }
    }
    
    /// Check if we're running in a CI environment
    pub fn is_ci() -> bool {
        std::env::var("CI").is_ok() ||
        std::env::var("CONTINUOUS_INTEGRATION").is_ok() ||
        std::env::var("GITHUB_ACTIONS").is_ok() ||
        std::env::var("TRAVIS").is_ok() ||
        std::env::var("CIRCLECI").is_ok()
    }
    
    /// Get the number of CPU cores for parallel processing
    pub fn get_cpu_count() -> usize {
        num_cpus::get().max(1)
    }
    
    /// Check if colors should be enabled
    pub fn should_use_colors() -> bool {
        if Utils::is_ci() {
            return false;
        }
        
        if let Ok(no_color) = std::env::var("NO_COLOR") {
            if !no_color.is_empty() {
                return false;
            }
        }
        
        if let Ok(force_color) = std::env::var("FORCE_COLOR") {
            if !force_color.is_empty() {
                return true;
            }
        }
        
        atty::is(atty::Stream::Stdout)
    }
}
