use indicatif::ProgressStyle;
use colored::*;

/// UI theme configuration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Theme {
    pub use_colors: bool,
    pub use_unicode: bool,
}

impl Default for Theme {
    fn default() -> Self {
        Self {
            use_colors: crate::utils::Utils::should_use_colors(),
            use_unicode: !crate::utils::Utils::is_ci(),
        }
    }
}

impl Theme {
    /// Create a new theme with custom settings
    pub fn new(use_colors: bool, use_unicode: bool) -> Self {
        Self {
            use_colors,
            use_unicode,
        }
    }
    
    /// Get progress bar style
    pub fn progress_style(&self) -> ProgressStyle {
        if self.use_unicode {
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {pos}/{len} ({per_sec}, {eta})")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  ")
        } else {
            ProgressStyle::default_bar()
                .template("{spinner} [{elapsed_precise}] [{wide_bar}] {pos}/{len} ({per_sec}, {eta})")
                .unwrap()
                .progress_chars("#>-")
        }
    }
    
    /// Get spinner style
    pub fn spinner_style(&self) -> ProgressStyle {
        if self.use_unicode {
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.blue} {msg}")
                .unwrap()
        } else {
            ProgressStyle::default_spinner()
                .tick_strings(&["|", "/", "-", "\\"])
                .template("{spinner} {msg}")
                .unwrap()
        }
    }
    
    /// Get download progress style
    pub fn download_style(&self) -> ProgressStyle {
        if self.use_unicode {
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes} ({bytes_per_sec}, {eta})")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  ")
        } else {
            ProgressStyle::default_bar()
                .template("{spinner} [{elapsed_precise}] [{wide_bar}] {bytes}/{total_bytes} ({bytes_per_sec}, {eta})")
                .unwrap()
                .progress_chars("#>-")
        }
    }
    
    /// Format success message
    pub fn success(&self, message: &str) -> String {
        if self.use_colors {
            format!("{} {}", "✓".green().bold(), message)
        } else {
            format!("✓ {}", message)
        }
    }
    
    /// Format error message
    pub fn error(&self, message: &str) -> String {
        if self.use_colors {
            format!("{} {}", "✗".red().bold(), message.red())
        } else {
            format!("✗ {}", message)
        }
    }
    
    /// Format warning message
    pub fn warning(&self, message: &str) -> String {
        if self.use_colors {
            format!("{} {}", "⚠".yellow().bold(), message.yellow())
        } else {
            format!("⚠ {}", message)
        }
    }
    
    /// Format info message
    pub fn info(&self, message: &str) -> String {
        if self.use_colors {
            format!("{} {}", "ℹ".blue().bold(), message)
        } else {
            format!("ℹ {}", message)
        }
    }
    
    /// Format step message
    pub fn step(&self, message: &str) -> String {
        if self.use_colors {
            format!("{} {}", "→".blue().bold(), message)
        } else {
            format!("→ {}", message)
        }
    }
    
    /// Format package name
    pub fn package_name(&self, name: &str) -> String {
        if self.use_colors {
            name.cyan().to_string()
        } else {
            name.to_string()
        }
    }
    
    /// Format version
    pub fn version(&self, version: &str) -> String {
        if self.use_colors {
            version.green().to_string()
        } else {
            version.to_string()
        }
    }
    
    /// Format duration
    pub fn duration(&self, duration: &str) -> String {
        if self.use_colors {
            duration.dimmed().to_string()
        } else {
            duration.to_string()
        }
    }
}
