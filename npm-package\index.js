const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Nx Package Manager - Node.js API
 * 
 * This module provides a Node.js API for the nx package manager.
 */

class NxPackageManager {
  constructor() {
    this.binaryPath = this.findBinary();
  }

  findBinary() {
    const binaryName = 'nx' + (process.platform === 'win32' ? '.exe' : '');
    const binaryPath = path.join(__dirname, 'bin', binaryName);
    
    if (fs.existsSync(binaryPath)) {
      return binaryPath;
    }
    
    throw new Error('nx binary not found. Please run "npm install" to download it.');
  }

  /**
   * Execute nx command programmatically
   * @param {string[]} args - Command arguments
   * @param {object} options - Execution options
   * @returns {Promise<{code: number, stdout: string, stderr: string}>}
   */
  async exec(args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(this.binaryPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        ...options
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({ code, stdout, stderr });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Install packages
   * @param {string[]} packages - Package names to install
   * @param {object} options - Installation options
   */
  async install(packages = [], options = {}) {
    const args = ['install'];
    
    if (options.dev) args.push('--dev');
    if (options.global) args.push('--global');
    if (options.force) args.push('--force');
    if (options.noLock) args.push('--no-lock');
    
    args.push(...packages);
    
    return this.exec(args);
  }

  /**
   * Uninstall packages
   * @param {string[]} packages - Package names to uninstall
   * @param {object} options - Uninstallation options
   */
  async uninstall(packages, options = {}) {
    const args = ['uninstall'];
    
    if (options.dev) args.push('--dev');
    if (options.global) args.push('--global');
    
    args.push(...packages);
    
    return this.exec(args);
  }

  /**
   * Run a script
   * @param {string} script - Script name
   * @param {string[]} args - Script arguments
   */
  async run(script, args = []) {
    return this.exec(['run', script, ...args]);
  }

  /**
   * List installed packages
   * @param {object} options - List options
   */
  async list(options = {}) {
    const args = ['list'];
    
    if (options.global) args.push('--global');
    if (options.depth !== undefined) args.push('--depth', options.depth.toString());
    
    return this.exec(args);
  }

  /**
   * Initialize a new project
   * @param {object} options - Initialization options
   */
  async init(options = {}) {
    const args = ['init'];
    
    if (options.name) args.push('--name', options.name);
    if (options.toml) args.push('--toml');
    
    return this.exec(args);
  }

  /**
   * Get nx version
   */
  async version() {
    return this.exec(['--version']);
  }
}

// Export both class and convenience functions
const nx = new NxPackageManager();

module.exports = {
  NxPackageManager,
  nx,
  
  // Convenience functions
  install: (packages, options) => nx.install(packages, options),
  uninstall: (packages, options) => nx.uninstall(packages, options),
  run: (script, args) => nx.run(script, args),
  list: (options) => nx.list(options),
  init: (options) => nx.init(options),
  version: () => nx.version(),
  exec: (args, options) => nx.exec(args, options)
};
