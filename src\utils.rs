pub mod registry;
pub mod fs;
pub mod semver;

use anyhow::Result;
use std::path::Path;
use std::time::Duration;

/// Format bytes into human-readable string
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Format duration into human-readable string
pub fn format_duration(duration: Duration) -> String {
    let total_secs = duration.as_secs();
    let millis = duration.subsec_millis();

    if total_secs >= 60 {
        let mins = total_secs / 60;
        let secs = total_secs % 60;
        format!("{}m {}s", mins, secs)
    } else if total_secs > 0 {
        format!("{}.{:03}s", total_secs, millis)
    } else {
        format!("{}ms", millis)
    }
}

/// Check if a directory is a valid Node.js package
pub fn is_package_directory(path: &Path) -> bool {
    path.join("package.json").exists()
}

/// Get package name from package.json
pub async fn get_package_name(path: &Path) -> Result<Option<String>> {
    let package_json_path = path.join("package.json");
    if !package_json_path.exists() {
        return Ok(None);
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;
    
    Ok(package.get("name")
        .and_then(|v| v.as_str())
        .map(|s| s.to_string()))
}

/// Validate package name according to npm rules
pub fn is_valid_package_name(name: &str) -> bool {
    if name.is_empty() || name.len() > 214 {
        return false;
    }

    // Must be lowercase
    if name != name.to_lowercase() {
        return false;
    }

    // Must start with alphanumeric or @
    let first_char = name.chars().next().unwrap();
    if !first_char.is_alphanumeric() && first_char != '@' {
        return false;
    }

    // Can only contain alphanumeric, hyphens, underscores, dots, and forward slashes
    name.chars().all(|c| {
        c.is_alphanumeric() || c == '-' || c == '_' || c == '.' || c == '/' || c == '@'
    })
}

/// Extract package name and version from a spec like "package@1.0.0"
pub fn parse_package_spec(spec: &str) -> (String, Option<String>) {
    if let Some(at_pos) = spec.rfind('@') {
        // Handle scoped packages like @scope/package@1.0.0
        if spec.starts_with('@') && at_pos > 0 {
            let name = &spec[..at_pos];
            let version = &spec[at_pos + 1..];
            (name.to_string(), Some(version.to_string()))
        } else {
            (spec.to_string(), None)
        }
    } else {
        (spec.to_string(), None)
    }
}

/// Get CPU count for optimal parallelism
pub fn get_cpu_count() -> usize {
    num_cpus::get().max(1)
}

/// Get the size of a path (file or directory)
pub async fn get_path_size<P: AsRef<Path>>(path: P) -> Result<u64> {
    let path = path.as_ref();
    let metadata = tokio::fs::metadata(path).await?;

    if metadata.is_file() {
        Ok(metadata.len())
    } else if metadata.is_dir() {
        fs::FsUtils::dir_size(path).await
    } else {
        Ok(0)
    }
}

/// Create a retry strategy with exponential backoff
pub async fn retry_with_backoff<F, Fut, T, E>(
    mut operation: F,
    max_retries: usize,
    initial_delay: Duration,
) -> Result<T, E>
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = Result<T, E>>,
{
    let mut delay = initial_delay;
    
    for attempt in 0..=max_retries {
        match operation().await {
            Ok(result) => return Ok(result),
            Err(e) => {
                if attempt == max_retries {
                    return Err(e);
                }
                tokio::time::sleep(delay).await;
                delay = delay * 2; // Exponential backoff
            }
        }
    }
    
    unreachable!()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(0), "0 B");
        assert_eq!(format_bytes(1023), "1023 B");
        assert_eq!(format_bytes(1024), "1.0 KB");
        assert_eq!(format_bytes(1536), "1.5 KB");
        assert_eq!(format_bytes(1048576), "1.0 MB");
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(Duration::from_millis(500)), "500ms");
        assert_eq!(format_duration(Duration::from_secs(1)), "1.000s");
        assert_eq!(format_duration(Duration::from_secs(65)), "1m 5s");
    }

    #[test]
    fn test_is_valid_package_name() {
        assert!(is_valid_package_name("express"));
        assert!(is_valid_package_name("@babel/core"));
        assert!(is_valid_package_name("lodash.debounce"));
        assert!(!is_valid_package_name("Express")); // Uppercase
        assert!(!is_valid_package_name("")); // Empty
        assert!(!is_valid_package_name("package with spaces")); // Spaces
    }

    #[test]
    fn test_parse_package_spec() {
        assert_eq!(parse_package_spec("express"), ("express".to_string(), None));
        assert_eq!(parse_package_spec("express@4.18.2"), ("express".to_string(), Some("4.18.2".to_string())));
        assert_eq!(parse_package_spec("@babel/core@7.22.0"), ("@babel/core".to_string(), Some("7.22.0".to_string())));
    }
}
