use anyhow::Result;
use clap::Parser;
use std::time::Instant;
use tracing::{info, error};

mod cli;
mod commands;
mod resolver;
mod installer;
mod cache;
mod ui;
mod lockfile;
mod types;
mod utils;

use cli::{Cli, Commands};
use ui::UI;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize ultra-fast logging
    init_logging()?;
    
    let start_time = Instant::now();
    let cli = Cli::parse();
    let ui = UI::new();
    
    // Show beautiful startup banner
    ui.show_banner();
    
    let result = match cli.command {
        Commands::Install(args) => commands::install::execute(args, &ui).await,
        Commands::Uninstall(args) => commands::uninstall::execute(args, &ui).await,
        Commands::Update(args) => commands::update::execute(args, &ui).await,
        Commands::List(args) => commands::list::execute(args, &ui).await,
        Commands::Info(args) => commands::info::execute(args, &ui).await,
        Commands::Audit(args) => commands::audit::execute(args, &ui).await,
        Commands::Run(args) => commands::run::execute(args, &ui).await,
        Commands::Rebuild(args) => commands::rebuild::execute(args, &ui).await,
        Commands::Dedupe(args) => commands::dedupe::execute(args, &ui).await,
        Commands::Ci(args) => commands::install::execute_ci(args, &ui).await,
        Commands::Cache(args) => commands::cache::execute(args, &ui).await,
        Commands::Exec(args) => commands::run::execute_exec(args, &ui).await,
        Commands::Outdated(args) => commands::list::execute_outdated(args, &ui).await,
        Commands::Doctor => commands::doctor::execute(&ui).await,
        Commands::Benchmark(args) => commands::benchmark::execute(args, &ui).await,
    };
    
    match result {
        Ok(_) => {
            let total_time = start_time.elapsed();
            ui.final_success(total_time);
            info!("Command completed successfully in {:.2}s", total_time.as_secs_f64());
        }
        Err(e) => {
            ui.error(&format!("Command failed: {}", e));
            error!("Command failed: {:?}", e);
            std::process::exit(1);
        }
    }
    
    Ok(())
}

fn init_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    
    let filter = std::env::var("RUST_LOG")
        .unwrap_or_else(|_| "nx=info,warn".to_string());
    
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| filter.into()),
        )
        .with(tracing_subscriber::fmt::layer().with_target(false))
        .init();
    
    Ok(())
}
