use anyhow::Result;
use clap::Parser;
use std::time::Instant;

mod cli;
mod commands;
mod resolver;
mod installer;
mod cache;
mod ui;
mod lockfile;
mod types;
mod utils;

use cli::{Cli, Commands};
use ui::UI;

#[tokio::main]
async fn main() -> Result<()> {
    let start_time = Instant::now();
    let cli = Cli::parse();
    let ui = UI::new();

    // Show beautiful startup banner only for install commands
    if matches!(cli.command, Commands::Install(_)) {
        ui.show_banner();
    }
    
    let result = match cli.command {
        Commands::Install(args) => commands::install::execute(args, &ui).await,
        Commands::Uninstall(args) => commands::uninstall::execute(args, &ui).await,
        Commands::Update(args) => commands::update::execute(args, &ui).await,
        Commands::List(args) => commands::list::execute(args, &ui).await,
        Commands::Info(args) => commands::info::execute(args, &ui).await,
        Commands::Audit(args) => commands::audit::execute(args, &ui).await,
        Commands::Run(args) => commands::run::execute(args, &ui).await,
        Commands::Rebuild(args) => commands::rebuild::execute(args, &ui).await,
        Commands::Dedupe(args) => commands::dedupe::execute(args, &ui).await,
        Commands::Ci(args) => commands::install::execute_ci(args, &ui).await,
        Commands::Cache(args) => commands::cache::execute(args, &ui).await,
        Commands::Exec(args) => commands::run::execute_exec(args, &ui).await,
        Commands::Outdated(args) => commands::list::execute_outdated(args, &ui).await,
        Commands::Doctor => commands::doctor::execute(&ui).await,
        Commands::Benchmark(args) => commands::benchmark::execute(args, &ui).await,
    };
    
    match result {
        Ok(_) => {
            let total_time = start_time.elapsed();
            // Only show final success for install commands
            if matches!(cli.command, Commands::Install(_)) {
                ui.final_success(total_time);
            }
        }
        Err(e) => {
            ui.error(&format!("{}", e));
            std::process::exit(1);
        }
    }

    Ok(())
}
