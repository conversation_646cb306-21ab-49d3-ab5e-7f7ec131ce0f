#![recursion_limit = "512"]

use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::*;
use std::time::Instant;
use tracing::{info, warn, error};

mod cli;
mod registry;
mod resolver;
mod installer;
mod cache;
mod ui;
mod config;
mod utils;
mod performance;
mod workspace;
mod lockfile;
mod package;

use cli::*;

#[derive(Parser)]
#[command(
    name = "nx",
    version = env!("CARGO_PKG_VERSION"),
    about = "Ultra-fast npm package manager written in Rust",
    long_about = "nx is a blazing-fast npm package manager that completes installations in 2-3 seconds with parallel downloads and modern UI"
)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    #[arg(short, long, global = true)]
    verbose: bool,

    #[arg(short, long, global = true)]
    quiet: bool,
    
    /// Use specific registry URL
    #[arg(long, global = true)]
    registry: Option<String>,
    
    /// Working directory
    #[arg(short = 'C', long, global = true)]
    cwd: Option<String>,
}

#[derive(Subcommand)]
enum Commands {
    /// Initialize a new project with package.json
    #[command(alias = "create")]
    Init {
        /// Project name
        #[arg(short, long)]
        name: Option<String>,

        /// Use package.toml instead of package.json
        #[arg(long)]
        toml: bool,
    },

    /// Install dependencies (aliases: i, add)
    #[command(alias = "i")]
    #[command(alias = "add")]
    Install {
        /// Specific packages to install
        packages: Vec<String>,

        /// Install as dev dependency
        #[arg(short = 'D', long)]
        dev: bool,

        /// Install globally
        #[arg(short, long)]
        global: bool,

        /// Skip lockfile generation
        #[arg(long)]
        no_lock: bool,

        /// Force reinstall all packages
        #[arg(long)]
        force: bool,

        /// Save exact version
        #[arg(short = 'E', long)]
        exact: bool,

        /// Install as peer dependency
        #[arg(short = 'P', long)]
        peer: bool,

        /// Install as optional dependency
        #[arg(short = 'O', long)]
        optional: bool,
    },
    
    /// Uninstall packages (aliases: remove, rm, r, un)
    #[command(alias = "remove")]
    #[command(alias = "rm")]
    #[command(alias = "r")]
    #[command(alias = "un")]
    Uninstall {
        /// Packages to remove
        packages: Vec<String>,
        
        /// Remove from dev dependencies
        #[arg(short = 'D', long)]
        dev: bool,
        
        /// Remove globally
        #[arg(short, long)]
        global: bool,
    },
    
    /// Run package scripts (aliases: run-script, exec)
    #[command(alias = "run-script")]
    #[command(alias = "exec")]
    Run {
        /// Script name to execute
        script: String,
        
        /// Arguments to pass to the script
        args: Vec<String>,
    },

    /// Start the application (equivalent to npm start)
    Start {
        /// Additional arguments
        args: Vec<String>,
    },

    /// Run tests (equivalent to npm test)
    #[command(alias = "t")]
    Test {
        /// Additional arguments
        args: Vec<String>,
    },

    /// List installed packages
    #[command(alias = "ls")]
    List {
        /// Show global packages
        #[arg(short, long)]
        global: bool,

        /// Show only top-level packages
        #[arg(long)]
        depth: Option<usize>,
    },

    /// Link local packages for development
    Link {
        /// Package to link (current directory if not specified)
        package: Option<String>,
        
        /// Link globally
        #[arg(short, long)]
        global: bool,
    },
    
    /// Manage package cache
    Cache {
        #[command(subcommand)]
        action: CacheCommands,
    },
    
    /// Performance benchmarking
    Bench {
        /// Number of iterations
        #[arg(short, long, default_value = "3")]
        iterations: u32,
        
        /// Package to benchmark (uses test package if not specified)
        package: Option<String>,
    },
    
    /// Publish package to registry
    Publish {
        /// Registry to publish to
        #[arg(long)]
        registry: Option<String>,
        
        /// Dry run without actually publishing
        #[arg(long)]
        dry_run: bool,
    },
    
    /// Configuration management
    Config {
        #[command(subcommand)]
        action: ConfigCommands,
    },

    /// Search for packages in the npm registry
    Search {
        /// Search query
        query: String,

        /// Maximum number of results to show
        #[arg(short, long, default_value = "20")]
        limit: usize,

        /// Show detailed information
        #[arg(short, long)]
        detailed: bool,

        /// Search only in package names
        #[arg(long)]
        names_only: bool,
    },

    /// Show detailed information about a package
    Info {
        /// Package name to get information about
        package: String,

        /// Show specific version information
        #[arg(short, long)]
        version: Option<String>,

        /// Show all versions
        #[arg(long)]
        versions: bool,

        /// Show dependencies
        #[arg(long)]
        dependencies: bool,

        /// Output format (json, table)
        #[arg(long, default_value = "table")]
        format: String,
    },

    /// Show outdated packages
    Outdated {
        /// Check global packages
        #[arg(short, long)]
        global: bool,

        /// Output format (table, json)
        #[arg(long, default_value = "table")]
        format: String,

        /// Show all packages, not just outdated ones
        #[arg(long)]
        all: bool,
    },

    /// Update packages to their latest versions
    Update {
        /// Specific packages to update
        packages: Vec<String>,

        /// Update all packages
        #[arg(short, long)]
        all: bool,

        /// Save exact versions (no ^ or ~)
        #[arg(long)]
        save_exact: bool,

        /// Update dev dependencies
        #[arg(long)]
        dev: bool,

        /// Dry run - show what would be updated without actually updating
        #[arg(long)]
        dry_run: bool,
    },
}

#[derive(Subcommand)]
enum CacheCommands {
    /// Clear all cached packages
    Clear,
    
    /// Show cache information and statistics
    Info,
    
    /// Verify cache integrity
    Verify,
    
    /// Prune unused cache entries
    Prune,
}

#[derive(Subcommand)]
enum ConfigCommands {
    /// Get configuration value
    Get { key: String },
    
    /// Set configuration value
    Set { key: String, value: String },
    
    /// List all configuration
    List,
    
    /// Reset configuration to defaults
    Reset,
}

#[tokio::main]
async fn main() -> Result<()> {
    let start_time = Instant::now();
    let cli = Cli::parse();
    
    // Initialize minimal logging
    init_logging(cli.verbose, cli.quiet)?;
    
    // Change working directory if specified
    if let Some(cwd) = &cli.cwd {
        std::env::set_current_dir(cwd)?;
    }
    
    // Execute command
    let result = match cli.command {
        Commands::Init { name, toml } => {
            cli::init::init(cli::init::InitArgs {
                name,
                template: "basic".to_string(),
                toml,
                skip_install: false,
                force: false,
            }).await
        }
        Commands::Install { packages, dev, global, no_lock, force, exact: _, peer, optional } => {
            cli::install::execute(packages, dev, global, no_lock, force, peer, optional, cli.registry).await
        }
        Commands::Uninstall { packages, dev, global } => {
            cli::uninstall::execute(packages, dev, global).await
        }
        Commands::Run { script, args } => {
            cli::run::execute(script, args).await
        }
        Commands::Start { args } => {
            cli::start::execute(args).await
        }
        Commands::Test { args } => {
            cli::test::execute(args).await
        }
        Commands::List { global, depth } => {
            cli::list::execute(global, depth).await
        }
        Commands::Link { package, global } => {
            cli::link::execute(package, global).await
        }
        Commands::Cache { action } => {
            cli::cache::execute(action).await
        }
        Commands::Bench { iterations, package } => {
            cli::bench::execute(iterations, package).await
        }
        Commands::Publish { registry, dry_run } => {
            cli::publish::execute(registry, dry_run).await
        }
        Commands::Config { action } => {
            cli::config::execute(action).await
        }
        Commands::Search { query, limit, detailed, names_only } => {
            cli::search::search(cli::search::SearchArgs {
                query,
                limit,
                detailed,
                names_only,
            }).await
        }
        Commands::Info { package, version, versions, dependencies, format } => {
            cli::info::info(cli::info::InfoArgs {
                package,
                version,
                versions,
                dependencies,
                format,
            }).await
        }
        Commands::Outdated { global, format, all } => {
            cli::outdated::outdated(cli::outdated::OutdatedArgs {
                global,
                format,
                all,
            }).await
        }
        Commands::Update { packages, all, save_exact, dev, dry_run } => {
            cli::update::update(cli::update::UpdateArgs {
                packages,
                all,
                save_exact,
                dev,
                dry_run,
            }).await
        }
    };
    
    // Handle result with minimal output
    match result {
        Ok(_) => Ok(()),
        Err(e) => {
            eprintln!("npm ERR! {}", e);
            std::process::exit(1);
        }
    }
}

fn init_logging(verbose: bool, quiet: bool) -> Result<()> {
    use tracing_subscriber::{EnvFilter, fmt};

    // Only show errors by default, nothing in quiet mode
    let filter = if verbose {
        EnvFilter::new("nx=debug")
    } else {
        EnvFilter::new("error")
    };

    fmt()
        .with_env_filter(filter)
        .with_target(false)
        .with_level(false)
        .with_thread_ids(false)
        .with_thread_names(false)
        .without_time()
        .init();

    Ok(())
}


