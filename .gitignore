# Rust
/target/
**/*.rs.bk

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (for testing)
node_modules/
.nx_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# nx specific
extracted/
nx-lock.json
.nx-cache/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# Build artifacts
dist/
build/

# Environment
.env
.env.local
.env.*.local

# Coverage
coverage/
*.lcov

# Benchmarks
benchmarks/results/

# Documentation build
docs/_build/
