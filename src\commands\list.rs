use anyhow::Result;
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tracing::warn;

use crate::cli::{ListArgs, OutdatedArgs};
use crate::types::RegistryConfig;
use crate::ui::{OutdatedPackage, UI};
use crate::utils::registry::RegistryClient;
use crate::utils::semver::SemverUtils;
use crate::utils::{format_bytes, get_path_size};

/// Execute nx list command
pub async fn execute(args: ListArgs, ui: &UI) -> Result<()> {
    let node_modules_path = if args.global {
        dirs::home_dir()
            .unwrap_or_default()
            .join(".nx")
            .join("global")
            .join("node_modules")
    } else {
        PathBuf::from("node_modules")
    };

    if !node_modules_path.exists() {
        ui.warning("node_modules directory not found");
        return Ok(());
    }

    ui.step("📋 Listing installed packages...");

    let packages = scan_installed_packages(&node_modules_path, args.size).await?;

    if packages.is_empty() {
        ui.info("No packages installed");
        return Ok(());
    }

    if args.tree {
        show_dependency_tree(&packages, args.depth, ui).await?;
    } else {
        show_package_list(&packages, ui)?;
    }

    Ok(())
}

/// Execute nx outdated command
pub async fn execute_outdated(args: OutdatedArgs, ui: &UI) -> Result<()> {
    ui.step("🔍 Checking for outdated packages...");

    let registry_config = RegistryConfig::default();
    let registry = Arc::new(RegistryClient::new(registry_config)?);

    let node_modules_path = if args.global {
        dirs::home_dir()
            .unwrap_or_default()
            .join(".nx")
            .join("global")
            .join("node_modules")
    } else {
        PathBuf::from("node_modules")
    };

    if !node_modules_path.exists() {
        ui.warning("node_modules directory not found");
        return Ok(());
    }

    // Get current dependencies from package.json
    let (dependencies, dev_dependencies) = if !args.global {
        read_package_json_dependencies().await?
    } else {
        (HashMap::new(), HashMap::new())
    };

    let mut all_deps = dependencies.clone();
    all_deps.extend(dev_dependencies);

    if all_deps.is_empty() && !args.all {
        ui.info("No dependencies found in package.json");
        return Ok(());
    }

    let mut outdated_packages = Vec::new();

    // Check each dependency
    for (name, current_spec) in &all_deps {
        // Get installed version
        let installed_version = get_installed_version(&node_modules_path, name).await?;
        
        if let Some(installed) = installed_version {
            // Get latest version from registry
            match registry.get_package_metadata(name).await {
                Ok(metadata) => {
                    let latest = &metadata.latest;
                    
                    // Check if update is available
                    if should_update(&installed, latest, current_spec)? {
                        outdated_packages.push(OutdatedPackage {
                            name: name.clone(),
                            current: installed,
                            latest: latest.clone(),
                        });
                    }
                }
                Err(e) => {
                    warn!("Failed to check updates for {}: {}", name, e);
                }
            }
        }
    }

    // If showing all packages, also check installed packages not in package.json
    if args.all {
        let installed_packages = scan_installed_packages(&node_modules_path, false).await?;
        
        for package in installed_packages {
            if !all_deps.contains_key(&package.name) {
                match registry.get_package_metadata(&package.name).await {
                    Ok(metadata) => {
                        let latest = &metadata.latest;
                        
                        if should_update(&package.version, latest, "*")? {
                            outdated_packages.push(OutdatedPackage {
                                name: package.name,
                                current: package.version,
                                latest: latest.clone(),
                            });
                        }
                    }
                    Err(e) => {
                        warn!("Failed to check updates for {}: {}", package.name, e);
                    }
                }
            }
        }
    }

    ui.show_outdated_table(&outdated_packages);

    if !outdated_packages.is_empty() {
        ui.info(&format!("\n💡 Run 'nx update' to update {} outdated packages", outdated_packages.len()));
    }

    Ok(())
}

#[derive(Debug, Clone)]
struct InstalledPackage {
    name: String,
    version: String,
    path: PathBuf,
    size: Option<u64>,
    dependencies: Vec<String>,
}

async fn scan_installed_packages(node_modules_path: &PathBuf, include_size: bool) -> Result<Vec<InstalledPackage>> {
    let mut packages = Vec::new();
    let mut entries = tokio::fs::read_dir(node_modules_path).await?;

    while let Some(entry) = entries.next_entry().await? {
        let entry_path = entry.path();
        let entry_name = entry.file_name().to_string_lossy().to_string();

        // Skip special directories
        if entry_name.starts_with('.') {
            continue;
        }

        if entry_name.starts_with('@') {
            // Handle scoped packages
            let mut scope_entries = tokio::fs::read_dir(&entry_path).await?;
            while let Some(scope_entry) = scope_entries.next_entry().await? {
                let scoped_path = scope_entry.path();
                let scoped_name = format!("{}/{}", entry_name, scope_entry.file_name().to_string_lossy());
                
                if let Some(package) = read_package_info(&scoped_path, &scoped_name, include_size).await? {
                    packages.push(package);
                }
            }
        } else {
            // Handle regular packages
            if let Some(package) = read_package_info(&entry_path, &entry_name, include_size).await? {
                packages.push(package);
            }
        }
    }

    // Sort packages by name
    packages.sort_by(|a, b| a.name.cmp(&b.name));

    Ok(packages)
}

async fn read_package_info(package_path: &PathBuf, package_name: &str, include_size: bool) -> Result<Option<InstalledPackage>> {
    let package_json_path = package_path.join("package.json");
    
    if !package_json_path.exists() {
        return Ok(None);
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package_json: serde_json::Value = serde_json::from_str(&content)?;

    let version = package_json["version"]
        .as_str()
        .unwrap_or("unknown")
        .to_string();

    let dependencies = package_json["dependencies"]
        .as_object()
        .map(|deps| deps.keys().cloned().collect())
        .unwrap_or_default();

    let size = if include_size {
        get_path_size(package_path).await.ok()
    } else {
        None
    };

    Ok(Some(InstalledPackage {
        name: package_name.to_string(),
        version,
        path: package_path.clone(),
        size,
        dependencies,
    }))
}

fn show_package_list(packages: &[InstalledPackage], _ui: &UI) -> Result<()> {
    use colored::*;

    println!("\n📦 {} ({})", "Installed Packages".bright_cyan().bold(), packages.len());
    println!("┌─────────────────────┬─────────────┬─────────────────────────┐");
    println!("│ {} │ {} │ {} │", 
             "Package".bright_white().bold(),
             "Version".bright_white().bold(),
             "Size".bright_white().bold());
    println!("├─────────────────────┼─────────────┼─────────────────────────┤");

    for package in packages {
        let size_str = if let Some(size) = package.size {
            format_bytes(size)
        } else {
            "-".to_string()
        };

        println!("│ {:<19} │ {:<11} │ {:<23} │",
                 package.name.bright_green(),
                 package.version.bright_blue(),
                 size_str.bright_yellow());
    }

    println!("└─────────────────────┴─────────────┴─────────────────────────┘");

    let total_size: u64 = packages.iter()
        .filter_map(|p| p.size)
        .sum();

    if total_size > 0 {
        println!("\n📊 Total size: {}", format_bytes(total_size).bright_cyan().bold());
    }

    Ok(())
}

async fn show_dependency_tree(packages: &[InstalledPackage], max_depth: usize, _ui: &UI) -> Result<()> {
    use colored::*;

    println!("\n🌳 {}", "Dependency Tree".bright_cyan().bold());
    println!("└─ {}", "project".bright_white().bold());

    // Build dependency map
    let mut package_map: HashMap<String, &InstalledPackage> = HashMap::new();
    for package in packages {
        package_map.insert(package.name.clone(), package);
    }

    // Show tree for each top-level package
    for package in packages {
        if is_top_level_dependency(package).await.unwrap_or(false) {
            show_package_tree(package, &package_map, 1, max_depth, "   ");
        }
    }

    Ok(())
}

fn show_package_tree(
    package: &InstalledPackage,
    package_map: &HashMap<String, &InstalledPackage>,
    current_depth: usize,
    max_depth: usize,
    prefix: &str,
) {
    use colored::*;

    if current_depth > max_depth {
        return;
    }

    println!("{}├─ {} {}", 
             prefix, 
             package.name.bright_green(), 
             package.version.bright_blue());

    if current_depth < max_depth {
        let new_prefix = format!("{}│  ", prefix);
        for dep_name in &package.dependencies {
            if let Some(dep_package) = package_map.get(dep_name) {
                show_package_tree(dep_package, package_map, current_depth + 1, max_depth, &new_prefix);
            }
        }
    }
}

async fn is_top_level_dependency(package: &InstalledPackage) -> Result<bool> {
    // Check if package is listed in package.json dependencies
    let package_json_path = PathBuf::from("package.json");
    if !package_json_path.exists() {
        return Ok(true); // If no package.json, consider all as top-level
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package_json: serde_json::Value = serde_json::from_str(&content)?;

    let is_dependency = package_json["dependencies"]
        .as_object()
        .map(|deps| deps.contains_key(&package.name))
        .unwrap_or(false);

    let is_dev_dependency = package_json["devDependencies"]
        .as_object()
        .map(|deps| deps.contains_key(&package.name))
        .unwrap_or(false);

    Ok(is_dependency || is_dev_dependency)
}

async fn read_package_json_dependencies() -> Result<(HashMap<String, String>, HashMap<String, String>)> {
    let package_json_path = PathBuf::from("package.json");
    if !package_json_path.exists() {
        return Ok((HashMap::new(), HashMap::new()));
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package: serde_json::Value = serde_json::from_str(&content)?;

    let dependencies = package["dependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    let dev_dependencies = package["devDependencies"]
        .as_object()
        .map(|obj| {
            obj.iter()
                .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                .collect()
        })
        .unwrap_or_default();

    Ok((dependencies, dev_dependencies))
}

async fn get_installed_version(node_modules_path: &PathBuf, package_name: &str) -> Result<Option<String>> {
    let package_path = node_modules_path.join(package_name);
    let package_json_path = package_path.join("package.json");

    if !package_json_path.exists() {
        return Ok(None);
    }

    let content = tokio::fs::read_to_string(&package_json_path).await?;
    let package_json: serde_json::Value = serde_json::from_str(&content)?;

    Ok(package_json["version"].as_str().map(|s| s.to_string()))
}

fn should_update(current: &str, latest: &str, spec: &str) -> Result<bool> {
    // Parse versions
    let current_version = SemverUtils::parse_version(current)?;
    let latest_version = SemverUtils::parse_version(latest)?;

    // If latest is newer than current
    if latest_version > current_version {
        // Check if the latest version satisfies the spec
        if spec == "*" || spec == "latest" {
            return Ok(true);
        }
        
        return SemverUtils::satisfies(latest, spec);
    }

    Ok(false)
}
